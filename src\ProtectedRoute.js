import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './AuthProvider';
import Cookies from 'js-cookie';

const ProtectedRoute = ({ children, requiredPermission }) => {
  const { user, isAuthenticated } = useAuth();
  const location = useLocation();
  const token = Cookies.get('authToken');
  // console.log("test: "+ isAuthenticated);
  if (!token) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requiredPermission && !user.permissions.includes(requiredPermission)) {
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  return children;
};

export default ProtectedRoute;




// import React from 'react';
// import { Navigate, useLocation } from 'react-router-dom';
// import { useAuth } from './AuthProvider';

// const ProtectedRoute = ({ children, requiredPermission }) => {
//   const { user } = useAuth();
//   const location = useLocation(); 

//   const hasPermission = user.permissions.includes(requiredPermission);

//   if (!hasPermission) {
//     return <Navigate to="/unauthorized" state={{ from: location }} replace />;
//   }

//   return children;
// };

// export default ProtectedRoute;
