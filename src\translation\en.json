{"10": "Complaints System", "11": "ISO System", "12": "Document Archiving System", "13": "Branches Plan System", "15": "Solar System", "16": "BID System", "17": "Rent System", "18": "AlShamel System", "19": "Call Center System", "20": "Fix System", "21": "Damage System", "23": "Daughter Company Follow up System", "1001": "Complaints Sub System", "1101": "Quality Management System (ISO)", "1102": "Follow up Daily Procedures System", "1201": "Document Archiving Sub System", "1301": "Branches Plan Sub System", "1302": "Monthly Plan System", "1501": "Solar Sub System", "1601": "Bidding System", "1701": "Rent Sub System", "1801": "AlShamel System for Field Activity", "1901": "Setup Sub System", "1902": "Contacting Services System", "1903": "Evaluation System", "2001": "Maintenance FollowUp System", "2101": "Damage Sub System", "2201": "Project Management System", "2202": "Installation and Estimation System", "2301": "Daughter Company Follow up System", "100101": "Settings", "100102": "Transactions", "100103": "Reports", "110101": "Settings", "110102": "Transactions", "110103": "Reports", "110104": "Audit Plan", "110201": "Follow up Procedures", "110202": "Reports", "120101": "Settings", "120102": "Transactions", "120103": "Reports", "130100": "Settings", "130101": "Outstandings", "130102": "Sales", "130103": "Credit Notes", "130104": "Collections", "130105": "Others", "130106": "Reports", "130107": "Monthly Plan", "130108": "Readings", "130201": "Settings", "130202": "Transactions", "130203": "Reports", "150101": "Settings", "150102": "Transactions", "150103": "Reports", "150104": "<PERSON><PERSON><PERSON>", "160101": "Settings", "160102": "Transactions", "160103": "Reports", "170101": "Settings", "170102": "Transactions", "170103": "Reports", "180101": "Settings", "180102": "Transactions", "180103": "Reports", "190101": "Settings", "190201": "Settings", "190202": "Transactions", "190203": "Reports", "190301": "Settings", "190302": "Transactions", "190303": "Reports", "200101": "Settings", "200102": "Transactions", "200103": "Reports", "210101": "Settings", "210102": "Transactions", "210103": "Reports", "220101": "Settings", "220102": "Transactions", "220103": "Reports", "220201": "Settings", "220202": "Transactions", "220203": "Reports", "230101": "Settings", "230102": "Transactions", "230103": "Reports", "10010100": "Companies System Param", "10010101": "Comp Complaint Types", "10010102": "Comp Complaint Status", "10010103": "Comp Action Codes", "10010104": "Comp Employees", "10010105": "Comp Complaint Resources", "10010106": "Comp Note Types", "10010107": "Comp Complaint Groups", "10010108": "Comp Attachment Types", "10010109": "Comp Branches Info", "10010110": "Comp Complaint Categories", "10010201": "Comp Comp<PERSON><PERSON>", "10010202": "Comp Complaints Inbox", "10010203": "Comp Complaints Review", "10010204": "Comp Complaints Unarchived", "10010205": "Comp Complaints Temp", "10010206": "Comp Complaints Temp Query", "10010207": "Comp Complaints Employees Log", "10010208": "Comp Complaints All Query Only", "10010209": "Comp Complaints Call Center", "10010210": "Comp Complaints Call Center Review", "10010301": "Rep Comp Complaints Summary Rep", "10010302": "Rep Comp Complaints Due Period", "10010303": "Rep <PERSON> Due Period", "10010304": "Rep Comp <PERSON>", "10010305": "Rep Complaints Actions Due Period", "10010306": "Rep Comp Monthly Summary", "10010307": "Rep Comp Complaints Days Log", "10010308": "Rep Comp Complaints Temp", "11010100": "Companies System Param", "11010101": "Iso Document Types", "11010102": "Iso Org Tree Div", "11010103": "Iso Org Tree Dep", "11010104": "Iso Report Format", "11010105": "Iso Employees", "11010106": "<PERSON><PERSON>", "11010107": "Iso Plan Keys", "11010108": "Iso Holidays", "11010109": "Iso Corrective Action Sources", "11010110": "Iso Unconformity Reasons", "11010201": "Iso Documents", "11010202": "Iso Templates Employees", "11010203": "Iso Documents Employees", "11010204": "Iso Documents Temp", "11010205": "Iso Documents H", "11010206": "Iso Requests", "11010301": "Rep Iso Updated Documents", "11010302": "Rep Iso New Documents", "11010303": "Rep <PERSON><PERSON>mplates Employees", "11010304": "Rep <PERSON><PERSON>mplates Employees E", "11010305": "Rep Iso Employees Div", "11010306": "Rep Iso Del Documents", "11010307": "Rep Iso Requests", "11010308": "Rep Iso Main Menu", "11010309": "Rep Iso Document Records", "11010401": "Iso Audit Plan", "11010402": "Iso Audit Year", "11010403": "Iso Audit Plan Schedule", "11010404": "Iso Audit Plan Audit Seq Vw", "11010405": "Iso Corrective Actions", "11010406": "Iso Audit Plan Schedule Edit", "11010407": "Iso Audit Plan Notes Review", "11010421": "Rep Iso Audit Plan", "11010422": "Rep Iso Audit Plan Schedule", "11010423": "Rep Iso Audit Plan Summary", "11010424": "Rep Iso Corrective Actions", "11010425": "Rep Iso Audit Plan Notes", "11010426": "Rep Iso Plan Keys Matrix", "11010427": "Rep Iso Audit Plan Comparison", "11010428": "Rep Iso Auditor Summary Rep", "11010429": "Rep Iso Audit Plan Comparison H", "11010430": "Rep Iso Audit Plan Notes Mgr", "11010431": "Rep Iso Audit Plan Notes Rev", "11020101": "Iso Documents Processes", "11020102": "Iso Documents Processes Log", "11020103": "Iso Documents Processes Log Review", "12010100": "Companies System Param", "12010101": "Edm First Level Tree", "12010102": "<PERSON><PERSON>", "12010103": "Edm Document Formats", "12010104": "<PERSON><PERSON>", "12010105": "Edm Document Types", "12010106": "Edm Users File Privs", "12010107": "Edm Users Params", "12010108": "Edm Users Tree Privs", "12010109": "Edm File Categories", "12010201": "Edm Users Tree", "12010202": "Edm Users Tree Search", "12010203": "Edm Users Tree Search M", "12010204": "Edm Browse Logs Vw", "12010301": "Rep Edm Files Docs", "12010302": "Rep Edm Tree Summary", "12010303": "Rep Edm Files Docs Not Confirmed", "12010304": "Rep Edm User Tree Privs", "12010305": "Rep Edm Tree User Privs", "12010306": "Rep Edm Tree Details", "12010307": "Rep Edm Files Docs Summary", "13010001": "Miz Paths", "13010002": "Mis Employees", "13010101": "Miz Monthly Plan Param O", "13010102": "Miz Monthly Rep O", "13010103": "Miz Monthly Plan Param O Dtl", "13010104": "Miz Monthly Rep O Dtl", "13010105": "Miz Monthly Plan Param O C", "13010106": "Miz Monthly Plan Param O S", "13010201": "Miz Monthly Plan Param S", "13010202": "Miz Monthly Plan Param S Dtl", "13010203": "Miz Monthly Sales S", "13010204": "Miz Monthly Sales S Dtl", "13010301": "Miz Monthly Plan Param D", "13010302": "Miz Monthly Plan Param D Dtl", "13010303": "Miz Monthly Credit D Dtl", "13010304": "Miz Monthly Credit D", "13010401": "Miz Monthly Plan Param C", "13010402": "Miz Monthly Plan Param C Dtl", "13010501": "Miz Sulta Camps Monthly B", "13010502": "Miz Sulta Camps Monthly", "13010601": "Rep Miz Monthly Plan Rep", "13010602": "Rep Miz Monthly Plan Rep 2", "13010603": "Rep Miz Monthly Plan Rep Path", "13010604": "Rep Miz Monthly Plan Rep Sbranch", "13010605": "Rep Miz Monthly Plan Rep Path Comp", "13010606": "Rep Miz Monthly Plan Rep Sbranch Comp", "13010701": "Miz Monthly Plan", "13010801": "Miz Monthly Readings Est Stat", "13010802": "Miz Monthly Readings Calc Stat", "13020101": "Mis R Items Groups", "13020102": "Mis R Branches Offices", "13020103": "Mis R Offices Sbranches", "13020104": "Mis R Offices Centers", "13020105": "<PERSON>s R <PERSON>", "13020106": "Mis R Tree Users", "13020107": "Mis R Templates", "13020201": "Mis R Report Master", "13020301": "Rep Mis R Report Master", "13020302": "Rep Mis R Report Comparison", "15010100": "Companies System Param", "15010101": "Sol System Types", "15010102": "Sol Cell Types", "15010103": "Sol Inverter Types", "15010104": "Sol Meter Types", "15010105": "Sol Suppliers", "15010106": "Sol Communication Devices", "15010107": "Sol System Types Groups", "15010108": "Sol Transaction Types", "15010109": "Sol Organizations", "15010110": "Sol Attachment Types", "15010112": "Sol Tariff Types Mapping", "15010113": "Sol Items Groups", "15010114": "Sol Lookup Tables", "15010115": "Sol Result Codes", "15010116": "Sol Stations Categories", "15010117": "Sol Visits Readings Setup", "15010118": "Sol Extras", "15010119": "Sol Items", "15010201": "Sol Stations", "15010202": "Sol Visits", "15010203": "Sol Visits Transfer", "15010204": "Sol Stations Query", "15010205": "Sol Visits Query", "15010206": "Sol Visits Transfer M", "15010207": "Sol Visits Tabular", "15010208": "Sol Stations Visits Meters Vw", "15010209": "Sol Recycle Balance Netting", "15010210": "Sol Billing Consumption Qty", "15010211": "Sol Visits Solar Qty Vw", "15010212": "Sol Stations Visits Meters Vw M", "15010213": "Sol Integration Vw", "15010214": "Sol Stations Visits Meters Vw Q", "15010215": "Sol Stations Meters Read Vw", "15010216": "Sol Recycle Balance Diff Vw", "15010217": "Sol Stations Organizations", "15010218": "Sol Visits Organizations", "15010219": "Sol Stations Meters P Read Vw", "15010220": "Sol Old Recycle Balance Dif Vw", "15010221": "Sol Stations Info Change", "15010222": "Sol Move Recycle Balance", "15010223": "Sol Check Monthly Visits", "15010224": "Sol Stations Meters Changed", "15010225": "Sol Ben Stations Cons Readings", "15010226": "Sol Stations Meters Info Change", "15010227": "Sol Update Visit Date Log", "15010228": "Sol Stations Uploaded Vw", "15010301": "Rep Sol Stations Summary Rep", "15010302": "Rep Sol Stations Details", "15010303": "Rep Sol Stations", "15010304": "Rep Sol Visits Summary Rep", "15010305": "Rep Sol Visits Details", "15010306": "Rep <PERSON> V<PERSON>", "15010307": "Rep Sol Stations Qty Stat Rep", "15010308": "Rep Sol Visits Organizations", "15010309": "Rep Sol Visits Summary Dtl Rep", "15010310": "Rep Sol Stations Cells Inv", "15010311": "Rep Sol Stations Summary New Rep", "15010401": "Sol Injaz Readings File Vw", "15010402": "Sol Injaz Visits Dtls", "15010404": "Rep Sol Monthly Visits Rep", "15010450": "Rep Sol Injaz Readings File Vw", "16010100": "Companies System Param", "16010101": "Bid Bids Status", "16010102": "Bid Bids Types", "16010103": "Bid Guarantee Action Types", "16010104": "Bid Bids Action Types", "16010105": "Bid Guarantee Status", "16010106": "Bid Guarantee Types", "16010107": "Bid Organizations", "16010108": "Bid Document Types", "16010109": "Bid Contracts Types", "16010110": "Bid Contracts Status", "16010111": "Bid Contracts Action Types", "16010112": "Bid Departments", "16010113": "Bid Letter Types", "16010114": "Bid Employees", "16010201": "Bid Bids", "16010202": "Bid Bids Guarantees", "16010203": "Bid Guarantees Vw", "16010204": "Bid Contracts", "16010205": "Bid Contracts Guarantees", "16010206": "Bid Bids Organizations Open", "16010207": "Bid Users Departments", "16010208": "Bid Bids Info Change", "16010209": "Bid Bids Letters", "16010210": "Bid Bids Query", "17010100": "Companies System Param", "17010101": "Rnt Immovables Types", "17010102": "Rnt Document Types", "17010103": "Rnt Payment Years", "17010104": "Rnt Users Branches", "17010105": "Rnt Payment Status", "17010201": "Rnt Parties", "17010202": "Rnt Immovables", "17010203": "Rnt Immovables Contracts Vw", "17010204": "Rnt Immovables Log", "17010205": "Rnt Imm Contract Periods Vw", "17010206": "Rnt Parties Payments Trans", "17010207": "Rnt Parties Payments Vw", "17010208": "Rnt Prpare All Payments", "17010209": "Rnt Parties Div", "17010210": "Rnt Imm Contract Periods Vw Q", "17010301": "Rep Rnt Immovables Log", "17010302": "Rep Rnt Parties Payments", "17010303": "Rep Rnt Parties Payments Log", "18010100": "Companies System Param", "18010101": "Hhu Gps Users", "18010102": "Hhu Gps Task Types", "18010103": "Hhu Gps Task Results", "18010104": "Z Hhu Gps System Users", "18010201": "Hhu Gps Info Change", "18010202": "Hhu Gps Meters", "18010203": "Hhu Gps Meter Test Temp", "18010204": "Hhu Gps Payments Entry", "18010205": "Hhu Gps Service Notes", "18010206": "Hhu Gps Daily Services", "18010207": "Hhu Gps Tasks", "18010208": "Hhu Gps Assign Tasks", "19010101": "Companies System Param", "19010102": "Cc Categories", "19010103": "Cc Grades", "19010104": "Cc Categories Grades", "19010105": "Cc Employees", "19010106": "Cc Lookups", "19020101": "Cc Call Types", "19020102": "Cc Call Result", "19020103": "Cc Questionairs", "19020104": "Cc Departments", "19020105": "Cc Call Reasons", "19020201": "Cc <PERSON>", "19020202": "Cc Call Patches Employees", "19020203": "Cc Call Patches Supervisor", "19020204": "Cc Call Log", "19020301": "Rep C<PERSON> Call Summary Employee", "19020302": "Rep Cc Call Summary Branch", "19020303": "Rep Cc Call Patches Services Emp", "19020304": "Cc Call Log Rep", "19030101": "Cc Evaluation Scale", "19030102": "Cc Evaluation Scale Groups", "19030103": "Cc Evaluation Rating", "19030201": "Cc Evaluation Years", "19030202": "Cc Evaluation", "19030203": "Cc Evaluation Employees", "19030204": "Cc Evaluation Employees User", "19030301": "Rep Cc Evaluation Summary", "19030302": "Rep Cc Evaluation Employees", "20010101": "Companies System Param", "20010102": "Fix Items Groups", "20010103": "Fix Units", "20010104": "Fix Device Accessories", "20010105": "Fix Damage Types", "20010106": "Fix Items", "20010107": "Fix Customers", "20010108": "Fix Branches", "20010109": "Fix Employee Types", "20010110": "Fix Employees", "20010111": "Fix Status", "20010112": "Fix Suppliers", "20010201": "Fix Spare Parts Prices", "20010202": "Fix Spare Parts Stock", "20010203": "Fix Master", "20010204": "Fix Stock Transactions", "20010205": "Fix Order", "20010206": "Fix Master Call Center", "20010207": "Fix Items Sheet", "20010208": "Fix Customers New Serial", "20010209": "Fix New Customers", "20010301": "Rep Label Test", "20010302": "Rep Fix Stock Transactions", "20010303": "Rep Fix Transactions", "20010304": "Rep Fix Order Master Items Dtl", "20010305": "Rep Fix Master Payments", "21010100": "Companies System Param", "21010101": "Dam Damage Types", "21010102": "Dam Damage Status", "21010103": "Dam Attachment Types", "21010104": "Dam Action Codes", "21010105": "Dam Companies", "21010106": "Dam Departments", "21010107": "Dam Users", "21010201": "Dam Damages", "21010202": "Dam Damages Info Change", "21010301": "Rep Dam Damages", "21010302": "Rep Dam Damages Collections", "21010303": "Rep Dam Damages Finished", "22010100": "Companies System Param", "22010101": "Pro Task Types", "22010102": "Pro Employees", "22010103": "Pro Teams", "22010104": "Pro Project Types", "22010105": "Pro Organizations", "22010106": "Pro Action Codes", "22010107": "Pro Project Status", "22010108": "Pro Requirement Codes", "22010109": "Pro Attachment Types", "22010110": "Pro Holidays", "22010111": "Pro Branch Groups", "22010201": "Pro Projects", "22010202": "Pro Projects Tasks Query", "22010301": "Rep Pro Projects Team Sche Temp", "22010302": "Rep Pro Projects", "22020101": "Pro Tech Task Types", "22020102": "Pro Tech Action Codes", "22020103": "Pro Tech Branch Groups", "22020104": "Pro Tech Employees", "22020105": "Pro Tech Result Codes", "22020106": "Pro Tech Items Groups", "22020107": "Pro Tech Teams", "22020108": "Pro Tech Holidays", "22020109": "Pro Tech Tariff Types", "22020110": "Pro Tech Tariff Immovables", "22020201": "Pro Tech Users Branches", "22020202": "Pro Tech Users Task Types", "22020203": "Pro Tech Master", "22020204": "Pro Tech Master Result", "22020205": "Pro Tech Master Result Team", "22020206": "Pro Tech Master Result Agree", "22020207": "Pro Tech Master Result Review", "23010100": "Companies System Param", "23010101": "Mis Company Info", "23010201": "Mis Company Employees", "23010202": "Mis Company Emp Info Change", "23010203": "Mis Comp Monthly Payments", "23010204": "Mis Comp Month Pay Summary Vw", "23010301": "Rep Mis Comp Monthly Payments", "LC_000123": "Employee ID", "LC_000132": "Add", "LC_000076": "Forgot password", "LC_000089": "Send Verification Code Via1", "LC_000136": "Please use this code to verify your email address.", "LC_000071": "Favorites", "LC_000072": "Company Code", "LC_000080": "SMS", "010103": "Reports", "01010101": "Hhu Customer Types", "01010107": "Hhu Transaction Codes", "01010309": "Rep Hhu Readings By Status", "010202": "Transactions", "01020201": "Hhu Inspection", "01020310": "Rep <PERSON><PERSON> Reader Points", "060103": "Reports", "04010110": "Sh Holder Categories", "04010727": "Rep <PERSON>h Generates Summary", "04010705": "Rep <PERSON><PERSON> Holders Address List", "04010510": "Sh Cancel Doc By Org", "04010406": "Sh Dividing Profit Balance", "********": "Rep <PERSON><PERSON> Unentered Services", "********": "Rep Elc Session Applications", "********": "Elc Awarded License", "********": "Rep Elc Electricians List", "********": "Rep Elc Applications By Elec", "0403": "Integration With Financial Departments System", "040201": "Settings", "040303": "Reports", "********": "Sh Bod", "********": "Sh Increasing Process", "********": "Sh Cancel Documents", "********": "<PERSON><PERSON> <PERSON>", "********": "Sh Review Hol Documents", "********": "Sh Do Banks Lists Trans", "********": "Sh <PERSON> Mapping", "********": "Sh Profit Categories", "********": "<PERSON><PERSON> Dividing Check By Holder Open", "********": "Sh Change Document Type", "********": "Rep <PERSON><PERSON> Ins Meter Status Stat", "********": "Rep <PERSON><PERSON> Holders List Without", "********": "Rep Mis Payments Rep", "********": "<PERSON>d Lawyer Types", "********": "<PERSON><PERSON>rs", "********": "Hidden Forms", "000101": "Setup", "********": "Mis Application Execute Flag", "********": "Mis Applications Groups B E Vw", "********": "Hhu Ins Services Manual", "********": "<PERSON><PERSON>s", "********": "Elc Holidays", "********": "Mis Items Groups", "********": "Rep Sh Meeting Mandates", "********": "Mis Tariff Slices Summary Rep", "********": "Elc Session Applications Admin", "********": "Support Employees", "********": "Support Customers", "********": "Rep Elc Test Team Summary Team", "********": "Jud Cort Attachment Types", "********": "Jud Lawyer Credit", "********": "Elc Electrician Cards By User", "********": "Mis Vouchers Mis Ded Param", "********": "Elc Test Services Stat Vw", "********": "Elc Session Applications Vw", "********": "Jud Services Approved Cases Vw", "********": "<PERSON><PERSON>", "********": "Mis Appl Out Groups B Vw", "********": "Mis Appl Out Groups B E Vw", "********": "Rep Hhu Notice", "********": "Mis Meters Rep", "0511": "FPM System", "********": "Mis Fpm Accounts Groups", "********": "<PERSON><PERSON>", "********": "Ex Exam <PERSON>up", "080102": "Transactions", "********": "Rep Ex Exam Questions", "********": "Mis Aging Service Dtls", "********": "Mis Unposted Payments", "********": "Jud Lawyer Payments Query", "********": "Sh Assign Types", "********": "Mis Checks Collections Rep Vw", "********": "Jud <PERSON>s Change Status", "********": "Mis Sulta Requests Privs", "********": "Mis Services Empty Cards", "050701": "External Audit", "********": "Mis Greates Services Aging Vw", "********": "Mis Interest Rates", "********": "Elc Elec Services", "********": "Mis Prepaid Meter Types", "********": "Mis Greates Services", "********": "Mis Services Query", "********": "Jud Lawyer Payments Details Vw", "0001": "Setup Module System", "000103": "Reports", "0509": "Is Prepaid System", "0506": "Financial Department System", "040107": "Reports", "********": "<PERSON><PERSON> Agg Receiving Doc By Holder", "LC_000105": "Password Reset Code", "LC_000135": "Your account's email verification code is", "LC_000142": "Basic Details", "LC_000147": "No data", "LC_000045": "Name in English", "LC_000106": "Your account's password reset code is", "LC_000121": "Email or Employee Id or User Code", "LC_000153": "Failed to connect to server", "LC_000006": "Theme Settings", "LC_000073": "Password", "LC_000075": "Send Verification Code Via", "LC_000098": "Two-Step Verification Code (Two-Factor Authentication)", "LC_000152": "The page you're looking for doesn't exist or has been moved.", "0101": "HHU Sub System", "********": "<PERSON><PERSON>", "********": "Rep <PERSON>hu Notes", "********": "Hhu Calc Types", "********": "Countries", "010201": "Settings", "********": "<PERSON>hu Ins Prepaid Manual Entry", "********": "Rep Elc Applications", "********": "Elc Application Types", "********": "Rep <PERSON>h Profit <PERSON>", "********": "Rep Sh Org Profit Documents A", "********": "Rep Sh Shares Profit Iist", "********": "Rep Sh Meeting Invitations Lbl", "0901": "Judiciary Sub System", "********": "Elc Electricians", "********": "Elc Prepared Sessions", "********": "Sh Multi Side Trans", "********": "Rep <PERSON><PERSON>", "********": "Rep <PERSON><PERSON>ers Cert", "********": "Jud Payment Types", "********": "Sh Review Org Documents", "********": "Sh Suspension Org", "********": "Sh Profit Currency Change Log", "********": "Rep <PERSON><PERSON>s", "********": "Companies System Param", "********": "Mis Check Bank Data", "********": "Mis Review Ppc Reading", "********": "Companies System Param", "********": "<PERSON><PERSON>", "060201": "Settings", "********": "<PERSON><PERSON>", "********": "Systems", "********": "Sh Shares Trans Commission", "********": "<PERSON><PERSON> Ins Periodic Srv", "********": "Hhu Paths Checks", "********": "Hhu Paths Outstand Cat Sum Vw", "********": "Elc Extra License Types", "********": "Mis Payments Per Month Vw", "********": "Sh Committee Meetings Bk", "********": "Mis Centers", "********": "<PERSON><PERSON>", "********": "Mis Category Tariff", "********": "Mis Saving Campaign", "********": "Rep Recieved Document Rep", "0002": "Support System", "********": "Rep <PERSON><PERSON> Transfered", "********": "Sh Profit Aggregate Generate", "********": "Rep Summary Payments Rep", "********": "Elc Test Services", "********": "Jud Cort Case Types", "********": "<PERSON>h Agg Div Check By Holder Open", "********": "Mis Applications Outstandings", "********": "Rep <PERSON>h Doc Due Date Trans Agg", "01010223": "Hhu Gps Stat Vw", "05021004": "Mis Camps Outstandings", "05021016": "Mis Sbranch Outstandings Monthly", "05021023": "<PERSON><PERSON>", "05100102": "Mis Ifrs9 Tariff Types", "05025007": "Mis Sulta Compensation Param", "00010151": "Com Lookups", "08010102": "Ex Exam Levels", "08010103": "Ex Exam Questions", "0404": "Committees Meetings System", "04040103": "Sh Committees", "04040105": "Sh Committee Members", "04040301": "Rep Sh Committee Meetings", "05023013": "Mis Payments Rep2 T Vw", "05025004": "Mis Sulta Payments", "04010404": "Sh Cancel Unreceived Documents", "09010316": "Rep Jud Lawyer Payments", "06020305": "Rep Elc Test Services Groups Sb", "05023501": "Mis Returned Checks", "04020304": "Rep Sh Meeting Invitations 2", "050205": "Services", "09010233": "<PERSON><PERSON> Cases Details Paid Inv", "05029907": "Mis Do Untrans Prepaid Payments", "05020501": "Mis Agreements Consumptions", "05022019": "Mis Company Services Sales", "04010813": "Sh Agg Review Documents Update", "050501": "Settings", "04020104": "Sh Committee Members", "050230": "Collections", "0602": "Test and Violations system", "050101": "Settings", "050210": "Outstandings", "050801": "Transactions", "0501": "Setup Sub System", "09010211": "<PERSON><PERSON>s Is Court Vw", "LC_000070": "Show all", "LC_000139": "This e-mail has been sent to inform you that your password has been updated.", "LC_000046": "Fractions in Arabic", "LC_000050": "svsdvsv156200", "LC_000067": "Print", "LC_000114": "Second", "LC_000115": "Hours", "LC_000143": "Contact Details", "LC_000068": "There are no favorite items to display.", "LC_000154": "Please fill out the fields below to edit a currency.", "LC_000124": "Phone Number", "LC_000130": "Edit", "LC_000027": "Currency Data", "LC_000063": "Add New Currency", "LC_000074": "User", "LC_000083": "Send Verification Code", "LC_000099": "Your two-step verification (two-factor authentication) code for login is:", "04": "Share Holders System", "01": "HHU System", "06": "Electrician License System", "********": "<PERSON>hu <PERSON>s", "********": "Rep <PERSON><PERSON>s", "********": "Rep Hhu Invoices", "********": "<PERSON><PERSON>", "********": "<PERSON><PERSON> Devices Summary", "010203": "Reports", "********": "Rep <PERSON><PERSON> Ins Late Process", "********": "Rep Sh Bank Documents List", "********": "Rep Sh Review Org Profit", "********": "Rep <PERSON>h Profit <PERSON>lip", "********": "Sh Fin Trans Log", "********": "Sh Change Hol Documents Type", "********": "Sh Profit Setup", "********": "Rep Elc Applications Summary", "********": "Elc Application Final Approval", "********": "Rep Elc Electrician Info Change", "********": "Elc License Types", "040202": "Transactions", "040105": "Document Transactions", "********": "Sh Update Profit Currency", "********": "Rep <PERSON>h Holders Fs", "********": "Sh Review Org Profit", "********": "Sh Review Holder Profit", "********": "Sh Transaction Types", "********": "Sh Notes", "********": "Hhu Inspection Process", "********": "Hhu Inspection Process Followup", "********": "Com Id Types", "********": "Com Prefix Sufix", "********": "Sh Dividing Check By Holder Manual", "********": "Rep Ju<PERSON>s Summary Rep2", "********": "<PERSON><PERSON>", "********": "Jud Cases Empty Agreements", "********": "Mis Applications Rep", "********": "Rep Mis Camps Sales Rep <PERSON><PERSON>", "********": "Hhu Paths Payments", "********": "Mis Centers Groups", "********": "Mis Items", "********": "Mis Sulta Payments Centers", "********": "Mis Immovable Type", "********": "Rep Elc Test Summary Rep", "********": "<PERSON><PERSON>s Query Temp", "********": "Rep Mis Camps Sales Rep Amt2", "********": "Mis Camp Sales Qty Rep", "********": "Mis Accounts", "********": "Rep Elc Applications Dtls Paid", "********": "Sh Review Documents Update", "********": "Sh Agg Profit Doc Info Change", "********": "Sh Bank List Mapping", "********": "Hhu Service Category", "********": "Jud Cort Payment Types", "********": "Jud Services Query Saved", "********": "Jud Cases Actions Temp", "********": "Mis Tariff Groups", "********": "Mis Appl Out Groups S E Vw", "********": "Rep Payments Summary", "********": "Mis Sulta Outstandings Branch", "********": "<PERSON><PERSON>s", "********": "Mis Branches Tariff Out Vw", "********": "<PERSON><PERSON> Mun Outstandings Year", "********": "Mis Camps Accounting Rep Vw", "051001": "Settings", "********": "Jud Cases Reactivate Log", "********": "System Notifications Setup", "040401": "Settings", "040403": "Reports", "********": "Mis Outstandings Camps Rep", "********": "Mis Payments Rep2 C Vw", "********": "Mis Consumption Per Period", "********": "Mis Sulta Requests Single", "********": "Mis Post Dated Checks Rep", "********": "Currencies", "********": "Rep Elc Test Services Not Conn", "********": "Jud Lawyer Agreement Log", "********": "Rep Elc Test Services Daily", "********": "Rep Reports Log Vw", "050250": "Government Reconciliation", "********": "Mis Load Sulta Payments", "********": "Rep Mis Load Sulta Payments", "********": "Jud Case Attachment Types", "********": "<PERSON><PERSON>s Query", "060204": "Ticketing and Service Contract", "********": "Elc Test Services Actions", "********": "Rep <PERSON>h Holders Profit Details", "********": "Mis Prepaid Vending Stations", "********": "Mis Prepaid Initial Readings", "0899": "Web Exam System", "040102": "Holders", "051101": "Settings", "********": "Mis Credit Accounts Rep B Vw", "********": "Rep Elc Test Services Groups", "********": "Rep <PERSON><PERSON>s Lawyer Stages", "********": "Jud Cases Actions Sms", "********": "Rep Sh Members Payments Dtls", "********": "Mis Prepaid Voucher Temp", "LC_000056": "Active", "LC_000108": "Password Update Notification", "LC_000131": "Delete", "LC_000137": "Email update notification for your account", "LC_000149": "Back to Home", "LC_000084": "Enter Verification Code", "LC_000086": "Confirm New Password", "LC_000116": "Minutes", "LC_000151": "Oops! Page not found", "LC_000017": "Setting<PERSON> saved successfully!", "LC_000019": "Edit Profile", "LC_000020": "Update", "LC_000146": "Search…", "LC_000148": "You do not have permission to access this page. Please check your credentials or contact the administrator.", "LC_000155": "Please fill out the fields below to add a currency.", "LC_000111": "Do you have the verification code?", "LC_000125": "Update basic details", "LC_000005": "Settings", "LC_000077": "Forgot password?", "LC_000141": "Passwords do not match.", "00": "Basic System", "********": "Rep <PERSON>hu Reading Period", "********": "Hhu Vat Types", "********": "Hhu Service Types", "********": "Hhu Invoices Form", "********": "Hhu Service Details", "********": "Rep <PERSON><PERSON>", "********": "Hhu Inspectors", "********": "Hhu Inspection Trans", "060101": "Settings", "04010105": "Companies System Param", "04010711": "Rep Sh Org Profit Documents H", "04010709": "Rep Sh Hol Profit Documents H", "04010723": "Rep Sh Holders List Check Id", "01020211": "Hhu Ins Process Service Followup", "06010110": "Companies System Param", "06010201": "Elc Applications", "06010108": "Elc Application Status", "040101": "Settings", "04010505": "Sh Profit Doc Info Change", "04010307": "Sh Update Share Value", "04020102": "Sh Attendance Types", "09010222": "Jud Lawyer Payments", "04010606": "Sh Tax Exe Review", "04010101": "Sh Holder Types", "04010726": "Rep <PERSON>h Holders Review Data", "04010722": "Rep <PERSON>h Holders Profit List", "01020306": "Rep <PERSON><PERSON> Ins By Service", "01020304": "Rep <PERSON><PERSON>s By Reader", "********": "Sh Review Rev Div", "050301": "Settings", "050302": "Transactions", "********": "<PERSON><PERSON>s", "********": "Jud Case Status", "********": "Mis Generate Hr Data", "********": "Companies System Param", "********": "Hhu Invoice Notes", "********": "System Companies", "********": "Users", "********": "Mis Applications Groups S E Vw", "********": "Rep Hhu Tariff Types Regions", "********": "Elc Test Results", "********": "Mis Voucher Accts Cust Rep", "********": "Mis Voucher Accounts Rep", "********": "Mis Credit Accounts Cust Rep", "********": "Mis Billing Errors Log Vw", "********": "Mis Payments New Rep", "********": "Mis Centers Collection Types", "********": "<PERSON><PERSON>", "050204": "Applications", "050503": "Reports", "********": "Mis Sulta Areas", "********": "Mis Applications Groups B Vw", "********": "Mis Accounts Groups", "********": "Rep <PERSON>h Profit Summary Rep", "********": "Sh Load Bank Files Check", "********": "Elc Applications Query", "090201": "Settings", "********": "Elc Document Types", "********": "<PERSON><PERSON> Lawyer Data", "********": "<PERSON><PERSON><PERSON>ceive", "********": "Rep Elc Test Days Avg", "********": "Mis Greates Services Yearly Vw", "********": "Mis Sulta Services", "********": "Mis Sbranches Tariff Out Vw", "********": "Mis Branches Cust Type Out Vw", "********": "Mis Camps Curr Outstandings Vw", "********": "Mis Branches Accounts Vw", "********": "Support Follows", "********": "Sh Committee Members Change", "050903": "Reports", "********": "Mis Wc Tariff Types", "********": "Mis W Capital Param", "051102": "Transactions", "********": "Mis Fpm Deductions", "********": "Mis Branch Sulta Curr Outst Vw", "********": "Tablet Devices Users", "0801": "Exam System", "********": "Rep Ex <PERSON>ams Sheet", "********": "Mis Prepare Dues Followup Dtls", "********": "Sh Attachment Types", "********": "Sh Comm Payment Types", "********": "Sh Committee Meetings", "********": "Sh Members Payments", "********": "Mis Tariff Types", "********": "Mis Sulta Requests", "********": "System Notifications Variables", "********": "Mis Returned Checks B Vw", "********": "Mis Services Tax Error", "********": "Mis Post Dated Checks", "********": "Mis Vouchers Log", "********": "Mis Request Types", "********": "Mis Agreements Consumptions Rep", "040106": "Query", "********": "Elc Electrician Cards", "********": "Mis Ifrs9 Tariff Groups", "LC_000069": "Show Favorites", "LC_000144": "Verification Code", "LC_000044": "Name in Arabic", "LC_000112": "Hour", "LC_000110": "This code expires on", "LC_000009": "Dark Mode", "LC_000012": "Overview", "LC_000014": "Systems", "LC_000107": "Please use this code to reset your password.", "09": "Judiciary System", "01010112": "Hhu Status", "010101": "Settings", "010102": "Transactions", "01010303": "Rep Hhu Daily Summary", "01010308": "Rep <PERSON><PERSON>", "01020309": "Rep <PERSON>hu Ins Process By Inspector", "01020307": "Rep <PERSON>hu Ins Process Rep Srvs", "06010103": "Elc License Prefix", "06020203": "Elc Test Services Result", "04010608": "Sh Holders Summary", "04010718": "Rep Sh Review Org Documents", "04010717": "Rep Sh Review Holder Profit", "04010715": "Rep <PERSON>h Profit <PERSON><PERSON>ry", "04010714": "Rep <PERSON>h Doc Due Date Trans", "04010708": "Rep Sh Hol Profit Documents A", "04010232": "Sh Holders Review", "0502": "Billing System", "06010310": "Rep Elc Electrician Card", "06010309": "Rep Elc Electrician Lic Log", "06010307": "Rep Elc Electricians Summary", "06010102": "Elc Qualification Types", "0401": "Share Holders Sub System", "040103": "Holders Transactions", "040301": "Settings", "04010405": "Sh Profit Generate", "04010503": "<PERSON>h Receiving Doc By Holder", "04010601": "Sh Review Documents", "04030203": "Sh Fin Transfer Summary", "01020204": "Hhu Inspection Process Entrybyinspector", "04010201": "<PERSON><PERSON> Holders", "04010724": "Rep <PERSON><PERSON>ers Summary", "01010320": "Rep Hhu Invoices Diff", "090101": "Settings", "090102": "Transactions", "050303": "Reports", "********": "Jud Case Types", "********": "<PERSON><PERSON>", "********": "Rep <PERSON><PERSON>", "********": "Rep <PERSON><PERSON>s Actions Details", "********": "Rep <PERSON><PERSON>", "050402": "Transactions", "060203": "Reports", "060202": "Transactions", "********": "Rep Sh Shares Trans Commission", "********": "<PERSON>hu Paths Outstandings", "********": "Hhu Paths Payments Vw", "********": "Hhu Paths Payments Month Vw", "********": "Rep Ju<PERSON> Finished Cases Rep", "********": "Mis Credit Accounts Rep", "********": "Mis Great Invoices Rep", "********": "<PERSON><PERSON> New Updates", "********": "Jud Services Query Read", "********": "Mis Applications Groups Dtl Vw", "********": "Mis Appl Out Groups Dtl Vw", "0505": "Campaign System", "********": "Rep Elc Applications Dtls", "********": "Mis Camps", "********": "Jud Cort Insurance", "********": "Mis Greates Service Balance Vw", "********": "Support Extra Systems", "********": "Mis Immovable Tariff", "********": "Rep Sh Generates Bank Dtls", "********": "Rep Elc Test Services Exceptions", "********": "Rep Elc Test Team Summary", "********": "Rep Elc Test Services", "********": "Jud Cort Case Status", "********": "<PERSON><PERSON>", "********": "Elc Test Services Result Query", "********": "Jud Multi Cases Services Vw", "********": "Jud Services Query", "********": "Jud Services Query Saved T", "********": "<PERSON><PERSON>", "********": "Rep Summary Payments Chk Rep", "********": "Elc Electricians Documents", "********": "Mis Camps Outstandings Year", "********": "Mis Branch Outstandings Monthly", "********": "<PERSON><PERSON>", "********": "Mis Disconnected Services Rep", "051003": "Reports", "0003": "Tablets System", "********": "<PERSON><PERSON> Q", "********": "User Parameters", "********": "Hhu Extra Data", "********": "Rep Sh Committee Meetings Members", "********": "Sh Meeting Types", "********": "Hhu Paths Inv Outstandings", "********": "Jud Services Query Saved View", "********": "Mis Fpm Param", "05020517": "Mis Accredit Services", "05023503": "Mis Returned Checks Rep", "05020514": "Mis Services Tariff Error", "000102": "Transactions", "05020518": "Mis Qty Rep", "04040203": "Sh Comm Meetings Attachment Vw", "01010224": "Hhu Patches Daily Status Vw", "05060102": "Mis Interest Master", "05020506": "Mis Sbranches Agreements", "05080101": "Mis Greates Services Camp", "0510": "IFRS9 System", "0508": "Administrative Requirements System", "05020523": "Mis Changed Meters Summary Rep", "LC_000059": "No", "LC_000061": "Base Currency", "LC_000095": "Signing 3...", "LC_000109": "If you did not request this, please ignore this message or contact STDDP support at 022970344.", "LC_000079": "Reset Password", "LC_000081": "Email", "LC_000085": "New Password", "LC_000088": "Verify", "LC_000133": "Email Verification Code", "LC_000134": "Confirm Your Email Address", "LC_000015": "<PERSON><PERSON>", "LC_000018": "Profile", "LC_000058": "Yes", "LC_000001": "Language", "LC_000007": "Save Settings", "LC_000010": "<PERSON><PERSON>", "LC_000011": "Logout", "LC_000013": "Dashboard", "LC_000065": "Download as PDF", "LC_000082": "Send Verification Code Via", "LC_000103": "Please note:", "01010307": "Rep <PERSON><PERSON> <PERSON> Summary", "01010311": "<PERSON> <PERSON><PERSON>", "06010105": "Elc Electrician Types", "04030101": "<PERSON><PERSON> <PERSON>rency Mapping", "04010308": "Sh Subscribe For Shares", "04010716": "Rep Sh Review Holder Documents", "04030202": "Sh Fin Transfer", "04010504": "<PERSON><PERSON> <PERSON> By Holder", "04010402": "Sh Profit Approval", "06010203": "Elc Session Applications", "040203": "Reports", "040302": "Transactions", "04010507": "Sh Receiving Check", "04010301": "Sh Single Side Trans", "04020301": "Rep Sh Meeting Invitations", "04010104": "<PERSON><PERSON>", "04010509": "<PERSON>h Receiving Doc By Org", "01020305": "<PERSON> <PERSON><PERSON>s By Sbranch", "04010106": "Sh Certificate Setup", "09010101": "Jud Action Codes", "09010309": "Rep Jud <PERSON>s By Lawyer", "********": "Rep <PERSON><PERSON> Summary Rep", "********": "<PERSON><PERSON>", "********": "Elc Electricians Manual", "********": "Rep Elc Electrician Certificate", "********": "Templates", "********": "Sh Shares Trans Comm Query", "********": "Sh Profit Generate Setup", "********": "Hhu Paths Inv Outstand Sum Vw", "********": "Hhu Paths Disconnected", "********": "Sh Meeting Attendence Barcode", "********": "Sh Fees Types", "********": "Mis Appl Out Items B Vw", "********": "Mis Voucher Accounts Rep Tariff", "********": "Mis Voucher Accounts Rep Bs Vw", "********": "Mis Voucher Accounts Rep Bv Vw", "********": "Mis Voucher Accounts Rep B Vw", "********": "Mis Credit Accounts Rep Tariff", "********": "Mis Credit Accounts Rep G Vw", "********": "Mis Payments Param Aggregate", "********": "Mis Tariff Slices Types", "********": "Jud <PERSON> Stages", "050203": "Settings", "********": "Rep Sh Profit Div Year", "********": "Sh Distribute Treasury Stock", "********": "Mis Changed Meters Rep", "000203": "Reports", "********": "Sh Profit Aggregate Setup", "********": "<PERSON><PERSON> <PERSON>gg <PERSON>cel Doc By Holder", "********": "Sh Agg Change Hol Documents Type", "********": "Rep Elc Test Services Type New", "********": "Sh Load Bank Files", "********": "Sh Duplicate Id No Vw", "********": "Rep Mis Sulta Sales Rep Qty", "********": "Elc Test Services Undownloaded", "********": "Rep Elc Test Team Summary Monthly", "********": "Jud Cort <PERSON>s", "********": "Sh Agg Div Check By Holder Manual", "********": "<PERSON><PERSON> Sheet Master", "********": "Mis Application Activity Flag", "********": "<PERSON><PERSON>", "********": "Elc Facility Types Extras", "********": "<PERSON><PERSON> L", "********": "Rep <PERSON><PERSON> Estimated Readings", "********": "Rep Jud Cases Payments Rep", "********": "Mis Sbranch Outstandings Year", "********": "Mis Billing Outstandings Y", "********": "Mis Branches Category Out Vw", "050901": "Settings", "********": "Mis Fpm Accounts", "********": "<PERSON><PERSON> Dues Followup", "080103": "Reports", "********": "Rep Ex Exams Results", "********": "Elc Lookup Tables", "********": "Elc Test Groups", "********": "Mis Aging Branch Vw", "********": "Mis Sulta Requests Summary Vw", "********": "Rep Elc Electrician Extra Lic", "********": "Rep Mis Camps Sales Rep Amt", "********": "Mis Collections Param Rep", "********": "Rep Mis New Services Days Avg", "********": "Mis Returned Checks Info Vw", "********": "Rep Mis Interest Master", "********": "Mis Greates Services Vw", "050235": "Checks", "********": "Rep <PERSON><PERSON>g", "********": "Jud Lawyer Attachment Types", "********": "Mis Prepaid Users", "********": "Mis Prepaid Payments File H", "********": "Sh Check Divide Reverse Agg", "0902": "Cort System", "********": "Mis Prepaid Services Outstandings", "040402": "Transactions", "********": "Support Requests Details", "********": "Rep <PERSON><PERSON> Dis Services", "********": "Mis Payments Rep B G Vw", "********": "Mis Municipalities", "LC_000057": "inActive", "LC_000090": "Signing 1 .", "LC_000028": "Code", "LC_000048": "Options", "LC_000113": "Minute", "LC_000138": "Your account's mobile number verification code is", "LC_000096": "Signing 4...", "LC_000051": "156200", "LC_000102": "STDDP", "LC_000118": "This code expires on:", "LC_000003": "English", "LC_000004": "Select Language", "LC_000097": "Signing 5...", "LC_000062": "Currency Rates", "LC_000100": "Please use this code to confirm your identity.", "05": "Miscellaneous Management System", "********": "Rep <PERSON><PERSON>nd Services", "********": "Hhu Cycle Types", "********": "Hhu Periodical Services", "********": "Hhu Tariff Types", "********": "<PERSON><PERSON>", "********": "Rep <PERSON>hu Dis Services Branches", "********": "Rep Hhu Monthly Summary", "********": "Sh Receiving Bank Trans", "********": "Sh Change Org Documents Type", "********": "Elc Electrician Info Change", "********": "Elc Electrician Status", "********": "Elc Application Renew Approval", "040104": "Profit Transactions", "********": "Sh Single Side Activate", "********": "Sh <PERSON> <PERSON>", "********": "Rep <PERSON>h Holders List", "********": "Rep Sh Tax Exe Updates", "********": "Hhu Inspection Process Entrybyservice", "********": "Rep <PERSON><PERSON> Unread Services", "********": "Com Banks", "********": "Relations Type", "********": "Sh Review Del Bank Lists", "050202": "Reports", "********": "Jud <PERSON>s Info Change", "********": "Rep <PERSON><PERSON>s Actions Summary", "********": "<PERSON><PERSON> <PERSON><PERSON>", "********": "Hhu Invoices", "********": "Hhu Paths Payments Sa Vw", "********": "Elc Branch Groups", "********": "Mis Trans Groups", "********": "Sh Fin General Setup", "********": "Mis Applications Items B Vw", "********": "Mis Voucher Credit Param Vw", "********": "Mis Voucher Accounts Rep G Vw", "********": "Mis Payments Rep B B Vw", "********": "Mis Tariff Slices Rep", "050502": "Transactions", "000202": "Transactions", "********": "Support Requests", "********": "Support Daily Reports", "040108": "Aggregate Profit Transactions", "********": "Sh Agg Dividing Check By Holder", "********": "Elc Test Types", "********": "Rep <PERSON><PERSON> Received Profit Rep", "********": "Rep User Privilege", "********": "Rep Elc Test Services Dtls", "********": "Elc Test Services Result B Query", "********": "Rep Sh Bank Documents List Agg", "********": "Elc Electricians Query", "********": "Elc Test Exception Review", "********": "Rep Elc Test Services Groups Vw", "090202": "Transactions", "090203": "Reports", "********": "Jud Cort Action Codes", "********": "<PERSON><PERSON>", "********": "Sh Agg Review Documents", "********": "Rep <PERSON><PERSON>s Lawyer Stages Dtls", "********": "Sh Meeting Barcode", "********": "<PERSON><PERSON> Added", "********": "Mis Sulta Outstandings", "********": "Mis Sulta Outstandings Year", "********": "Mis Sulta Curr Outstandings Vw", "********": "<PERSON><PERSON> <PERSON>ers Summary", "051002": "Transactions", "********": "Mis Dues Follow Agr Dtls M Vw", "********": "Mis Dues Follow Summary", "********": "System User Parameters", "********": "Notification Types", "********": "Elc Message Drafts", "********": "Companies System Param", "********": "Ex Exam Types", "********": "Mis Gl Aggregate Param Rep", "********": "Sh Members", "********": "Mis Payments Rep2", "04010725": "Rep Sh Hol List By Category", "05023001": "Mis Prv Payments Vw", "08010201": "Ex Exam Registeration", "05022016": "Mis Outstandings Rep", "05021029": "Mis Greates Services Summary", "05022021": "Mis Load Sulta Payments Prev", "04020103": "Sh Committees", "01010323": "Rep <PERSON><PERSON> Repeated Services", "05020320": "Mis Changed Sub Branches", "09010325": "<PERSON><PERSON>s Scheduling Stat", "050601": "Interest Calculation", "********": "Mis Prepaid Initial Readings H", "000302": "Transactions", "********": "Mis Load Prepaid Payments", "0601": "Electricians Licensing System", "050240": "Deposits", "********": "Rep Jud Lawyer Payments Dtl", "********": "Rep Jud Cases Actions Details By User", "********": "Mis Services Tax Error Month", "LC_000060": "isActive?", "LC_000055": "Rate no of days", "LC_000122": "User Code", "LC_000140": "This e-mail has been sent to inform you that your account email has been updated from [#OLD_EMAIL] to [#NEW_EMAIL].", "LC_000047": "Fractions in English", "LC_000064": "<PERSON>", "LC_000066": "Download as Excel", "LC_000087": "Cancel", "LC_000016": "Close", "LC_000053": "Symbol", "LC_000054": "Factor", "LC_000117": "Seconds", "LC_000150": "Unauthorized", "LC_000002": "Arabic", "LC_000008": "Light Mode", "LC_000078": "Sign in", "LC_000101": "Best regards", "LC_000104": "This e-mail was sent from an auto-notification system that cannot accept incoming e-mail. Please do not reply to this message.", "LC_000145": "Last login:", "********": "Hhu Penalty Factors", "********": "Hhu Readers", "********": "Rep Hhu Payments Unsent", "0102": "Inspection System", "********": "Rep <PERSON><PERSON> Ins Trans Rep Statistic", "********": "Rep <PERSON>hu Ins By Sbranch Appl", "********": "Bank Branches Accounts", "060102": "Transactions", "********": "Elc Staff", "********": "Sh Review Doc Info Change", "********": "Rep Sh Bank Trans Documents", "********": "Sh Update Doc No", "********": "Sh Dividing Check By Holder", "********": "Sh Holders Trans", "********": "Rep Hhu Payments", "********": "Rep Sh Gen Document Summary", "********": "Sh Receiving Lists", "********": "Sh Check Divide Reverse", "********": "Sh Meetings", "********": "Sh Update Doc Date", "********": "Rep Sh Meeting Addresses", "********": "Rep <PERSON><PERSON> Holders Card", "********": "Hhu Inspection Points Approval", "********": "Tax Types", "********": "Com Contact Types", "0504": "PPC Readings System", "090103": "Reports", "050201": "Transactions", "********": "Rep <PERSON><PERSON> Templates", "********": "Mis Load Ppc Readings", "********": "Rep <PERSON>h Doc Due Date Trans Rev", "********": "Elc Test Points", "********": "Hhu Append Service", "********": "Mis Attachment Types", "********": "Companies", "********": "Mis Applications Groups S Vw", "********": "<PERSON><PERSON> <PERSON>sp Query", "********": "Hhu Paths Outstandings Sum Vw", "********": "<PERSON>hu Paths Outstandings Cat", "********": "Rep Mis Sulta Sales Rep Amt", "********": "Mis Transactions", "********": "Mis Audit Year", "********": "Mis Checks Collections Rep", "********": "Mis Payments Rep B Ct Vw", "********": "Mis Payments Accounts Rep", "********": "Mis Camp Cons Qty Rep", "********": "Hhu Holidays", "********": "Support Types", "********": "Rep <PERSON><PERSON> Holder <PERSON><PERSON>", "********": "Sh Agg Do Banks Lists Trans", "********": "<PERSON>d Lawyer Guarantees", "********": "Rep Elc Test Services Schedual", "********": "Rep <PERSON>h Holders Documents", "********": "Rep Elc Test Boards", "********": "Elc Open New Course", "********": "Elc Test Exception Types", "********": "Elc Test Services Result Agree", "********": "<PERSON><PERSON>", "********": "Rep Elc Test Services Temp Conn", "********": "Rep Elc Electrician Cards", "********": "Jud Cases Type Log", "********": "Rep <PERSON><PERSON> Updates", "********": "Rep <PERSON><PERSON>", "********": "Rep <PERSON><PERSON> Deleted Case", "********": "Rep Sh Holders List Alphapet", "********": "Elc Facility Types", "********": "Jud Services Query Saved Rev", "********": "Elc Electrician Deleted", "********": "Rep Jud Cases Payments Chk Rep", "********": "Mis Appl Out Groups S Vw", "********": "Mis Greates Services Sum Vw", "********": "Rep <PERSON><PERSON> Deleted Cases", "050902": "Transactions", "********": "Mis Wc Tariff Groups", "05020525": "Mis Not Invoiced Services Vw", "051103": "Reports", "05110101": "Mis Fpm Tariff Groups", "05110102": "Mis Fpm Tariff Types", "05110104": "Mis Fpm Deductions Groups", "000301": "Settings", "00030101": "Tablet Devices", "080101": "Settings", "05021025": "Mis Aging Tariff Vw", "05021026": "Mis Aging Tax Vw", "06010305": "Rep Elc Session Results", "05070104": "Mis Credit Notes Acc Param", "05100201": "Mis Ifrs9 Param", "05023016": "Mis Collections Stat Param", "05024001": "Mis Deposits Rep", "00010201": "Reports Log Vw", "0507": "External Audit System", "050220": "Sales", "05080102": "Mis Checks Param Rep", "04010312": "Sh Holders Send Sms", "09010114": "Jud Case Status Query", "06010217": "Elc Electricians Data Query", "050299": "Prepaid Integration", "0402": "Holders Meetings System", "000201": "Settings", "04010313": "Sh Fees Transactions", "09010314": "Rep <PERSON><PERSON>s Lawyer Stat", "05025006": "Mis Sulta Compensation Types"}