import React, { useState } from 'react';
import {
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Badge,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  ListItemText,
} from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';

const notifications = [
  {
    id: 1,
    title: 'New Message',
    description: 'You have received a new message from <PERSON>.',
    time: '2 mins ago',
    avatar: '/john-avatar.jpg',
  },
  {
    id: 2,
    title: 'System Alert',
    description: 'Your password will expire in 3 days.',
    time: '10 mins ago',
    avatar: '/system-icon.png',
  },
  {
    id: 3,
    title: 'New Comment',
    description: '<PERSON> commented on your post.',
    time: '1 hour ago',
    avatar: '/jane-avatar.jpg',
  },
];

export default function NotificationDropdown() {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box>
      {/* Notifications Button */}
      <IconButton
        onClick={handleClick}
        color="inherit"
        aria-label="show notifications"
        aria-controls="notifications-menu"
        aria-haspopup="true"
      >
        <Badge badgeContent={3} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>

      {/* Notifications Menu */}
      <Menu
        id="notifications-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            width: 350,
            maxHeight: 400,
          },
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            Notifications
          </Typography>
        </Box>
        <Divider />

        {/* Notification Items */}
        <List disablePadding>
          {notifications.map((notification) => (
            <ListItem
              key={notification.id}
              button
              alignItems="flex-start"
              onClick={handleClose} // Close menu on click
            >
              <ListItemAvatar>
                <Avatar src={notification.avatar} />
              </ListItemAvatar>
              <ListItemText
                primary={notification.title}
                secondary={
                  <>
                    <Typography
                      sx={{ display: 'inline' }}
                      component="span"
                      variant="body2"
                      color="text.primary"
                    >
                      {notification.description}
                    </Typography>
                    {' — '}
                    {notification.time}
                  </>
                }
              />
            </ListItem>
          ))}
        </List>

        <Divider />
        <MenuItem onClick={handleClose}>
          <Typography variant="body2" align="center" sx={{ width: '100%' }}>
            View All Notifications
          </Typography>
        </MenuItem>
      </Menu>
    </Box>
  );
}
