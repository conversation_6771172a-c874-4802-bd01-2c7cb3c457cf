import * as React from "react";
import Grid from "@mui/material/Grid2";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import Copyright from "../../../../../../../internals/components/Copyright";
import { Button, IconButton } from "@mui/material";
import { Add, Delete, Edit, FilterAlt, Search } from "@mui/icons-material";
import { DataGrid } from "@mui/x-data-grid";

export default function IsoDocumentsGrid() {

   const columns = [
    {
      field: "actions",
      headerName: "الخيارات",
      width: 120,
      sortable: false,
      renderCell: () => (
        <div style={{ display: "flex", gap: "6px" }}>
          <IconButton
            sx={{
              backgroundColor: "white",
              border: "1px solid #ddd",
              borderRadius: "8px",
              padding: "4px",
              "&:hover": { backgroundColor: "#f5f5f5" },
            }}
          >
            <Delete sx={{ color: "#f44336" }} />
          </IconButton>

          <IconButton
            sx={{
              backgroundColor: "white",
              border: "1px solid #ddd",
              borderRadius: "8px",
              padding: "4px",
              "&:hover": { backgroundColor: "#f5f5f5" },
            }}
          >
            <Edit sx={{ color: "#1976d2" }} />
          </IconButton>

          <IconButton
            sx={{
              backgroundColor: "white",
              border: "1px solid #ddd",
              borderRadius: "8px",
              padding: "4px",
              "&:hover": { backgroundColor: "#f5f5f5" },
            }}
          >
            <Search sx={{ color: "#fbc02d" }} />
          </IconButton>
        </div>
      ),
    },
    { field: "descCircle", headerName: "وصف الدائرة", width: 180 },
    { field: "circle", headerName: "الدائرة", width: 100 },
    { field: "descAdmin", headerName: "وصف الإدارة", width: 180 },
    { field: "admin", headerName: "الإدارة", width: 100 },
    { field: "enName", headerName: "الإسم بالإنجليزية", width: 160 },
    { field: "arName", headerName: "الإسم بالعربية", width: 160 },
    { field: "docDate", headerName: "تاريخ الوثيقة", width: 160 },
    { field: "docTypeDesc", headerName: "وصف نوع الوثيقة", width: 160 },
    { field: "docType", headerName: "نوع الوثيقة", width: 120 },
    { field: "id", headerName: "التسلسل", width: 80 },
  ];

  const rows = [
    {
      id: 1,
      descCircle: "الجودة والتدقيق ...",
      circle: "21",
      descAdmin: "شؤون التنظيم ...",
      admin: "I",
      enName: "QP2101",
      arName: "ضبط وثائق نظام ...",
      docDate: "2021-01-20",
      docTypeDesc: "إجراءات",
      docType: "01",
    },
    {
      id: 2,
      descCircle: "الجودة والتدقيق ...",
      circle: "21",
      descAdmin: "شؤون التنظيم ...",
      admin: "I",
      enName: "QP2102",
      arName: "ضبط سجلات نظام ...",
      docDate: "2021-01-08",
      docTypeDesc: "إجراءات",
      docType: "01",
    },
    {
      id: 3,
      descCircle: "الجودة والتدقيق ...",
      circle: "21",
      descAdmin: "شؤون التنظيم ...",
      admin: "I",
      enName: "QP/21/03",
      arName: "التخطيط",
      docDate: "2022-02-06",
      docTypeDesc: "إجراءات",
      docType: "01",
    },
  ];

  return (
    <Box sx={{ width: "100%", maxWidth: { sm: "100%", md: "1700px" } }}>
      {/* cards */}
      {/* <Typography component="h2" variant="h6" fontFamily='Segoe UI Symbol' sx={{ mb: 2 }}>
      IsoDocumentsGrid
      </Typography> */}
      <Grid
        container
        spacing={2}
        columns={12}
        sx={{ mb: (theme) => theme.spacing(2) }}
      >
        {/* {data.map((card, index) => (
          <Grid key={index} size={{ xs: 12, sm: 6, lg: 3 }}>
             <StatCard {...card} /> 
          </Grid>
        ))} */}
        <Grid size={{ xs: 12, lg: 3 }}>
          <Stack gap={2} direction={{ xs: "column", sm: "row", lg: "column" }}>
            {/* <Typography variant="body1">hello world</Typography> */}
            {/* <CustomizedTreeView />  */}
            {/* <ChartUserByCountry /> */}
          </Stack>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, lg: 3 }}>{/* <HighlightedCard /> */}</Grid>
        <Grid size={{ xs: 12, md: 6 }}>{/* <SessionsChart /> */}</Grid>
        <Grid size={{ xs: 12, md: 6 }}>{/* <PageViewsBarChart /> */}</Grid>
      </Grid>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {/* زر Filters على اليسار */}
        <Button
          variant="contained"
          startIcon={<FilterAlt />}
          sx={{
            backgroundColor: "#2e3666",
            textTransform: "none",
            borderRadius: "8px",
            padding: "6px 16px",
            fontSize: "14px",
            "&:hover": { backgroundColor: "#23294d" },
          }}
        >
          Filters
        </Button>

        {/* أزرار Add و Search على اليمين */}
        <div style={{ display: "flex", gap: "8px" }}>
          <Button
            variant="contained"
            sx={{
              backgroundColor: "#2e3666",
              minWidth: "40px",
              height: "40px",
              borderRadius: "8px",
              "&:hover": { backgroundColor: "#23294d" },
            }}
          >
            <Add />
          </Button>

          <Button
            variant="contained"
            sx={{
              backgroundColor: "#2e3666",
              minWidth: "40px",
              height: "40px",
              borderRadius: "8px",
              "&:hover": { backgroundColor: "#23294d" },
            }}
          >
            <Search />
          </Button>
        </div>
      </div>

       <div
      style={{
        height: 400,
        width: "100%",
        direction: "rtl", // للكتابة من اليمين
      }}
    >
      <DataGrid
        rows={rows}
        columns={columns}
        pageSize={5}
        disableSelectionOnClick
        sx={{
          border: "none",
          "& .MuiDataGrid-columnHeaders": {
            backgroundColor: "#2e3666",
            color: "white",
            fontWeight: "bold",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px",
          },
          "& .MuiDataGrid-columnHeader": {
            borderRight: "1px solid #2e3666",
          },
          "& .MuiDataGrid-cell": {
            borderColor: "#eee",
          },
          "& .MuiDataGrid-row": {
            backgroundColor: "white",
          },
        }}
      />
    </div>
      {/* <Typography component="h2" variant="h6" sx={{ mb: 2 }}>
        Details
      </Typography> */}
      <Grid container spacing={2} columns={12}>
        <Grid size={{ xs: 12, lg: 9 }}>{/* <CustomizedDataGrid /> */}</Grid>
      </Grid>
      <Copyright sx={{ my: 4 }} />
    </Box>
  );
}
