import * as React from 'react';
import Stack from '@mui/material/Stack';
import NotificationsRoundedIcon from '@mui/icons-material/NotificationsRounded';
import CustomDatePicker from './CustomDatePicker';
import NavbarBreadcrumbs from './NavbarBreadcrumbs';
import MenuButtonNotifications from './MenuButtonNotifications';
import ColorModeIconDropdown from '../../../shared-theme/ColorModeIconDropdown';
import LanguageIconDropdown from '../../../shared-theme/LanguageIconDropdown';
import Colors from '../../../Common/Colors';
import { useAuth } from '../../../AuthProvider';

import ProfileIconDropdown from '../../../shared-theme/ProfileIconDropdown';
import Search from './Search';
import Cookies from 'js-cookie';


export default function Header({ pageName }) {

  const { user } = useAuth();


  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;
  // const currentColors = Colors[user.compCode] || Colors.default;

  return (
    <Stack
      direction="row"
      sx={{
        display: { xs: 'none', md: 'flex' },
        width: '100%',
        alignItems: { xs: 'flex-start', md: 'center' },
        justifyContent: 'space-between',
        maxWidth: { sm: '100%', md: '1700px' },
        pt: 1.5,
      }}
      spacing={2}
    >
      <NavbarBreadcrumbs pageName={pageName} />
      <Stack direction="row" sx={{ gap: 1 }}>
        <Search />
        <CustomDatePicker />
        {/* <MenuButton showBadge aria-label="Open notifications">
          <NotificationsRoundedIcon />
        </MenuButton> */}
        <LanguageIconDropdown />
        <MenuButtonNotifications
          showBadge
          aria-label="Open notifications"
          notifications={[
            { type: 'info', message: 'You have a new message!', time: '2 mins ago' },
            { type: 'success', message: 'Backup completed successfully.', time: '1 hour ago' },
            { type: 'error', message: 'Server is down!', time: '3 hours ago' },
          ]}
        >
          <NotificationsRoundedIcon sx={{ fontSize: 20, color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary, }} />
        </MenuButtonNotifications>

        <ColorModeIconDropdown />
        <ProfileIconDropdown />
      </Stack>
    </Stack>
  );
}
