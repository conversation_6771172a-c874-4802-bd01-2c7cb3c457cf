import React from "react";
import { Button, Typography, Box } from "@mui/material";
import { Home as HomeIcon, LockOutlined as LockIcon } from "@mui/icons-material"; // أيقونات
import { keyframes } from "@emotion/react";
import Colors from "./Common/Colors";
import { useTranslation } from 'react-i18next';

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(-60px); }
  to { opacity: 1; transform: translateY(0); }
`;

const Unauthorized = () => {
  const { t, i18n } = useTranslation();

  React.useEffect(() => {
    const storedLanguage = sessionStorage.getItem('language') || 'en';
    i18n.changeLanguage(storedLanguage);
    document.documentElement.setAttribute('dir', storedLanguage === 'ar' ? 'rtl' : 'ltr');
  }, [i18n]);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        bgcolor: "background.default",
        color: "text.primary",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center",
        animation: `${fadeIn} 1s ease-out`,
        p: 3,
      }}
    >
      <LockIcon sx={{ fontSize: { xs: 80, md: 120 }, color: Colors.primary, mb: 2 }} />

      <Typography
        variant="h3"
        sx={{
          fontWeight: "bold",
          mb: 2,
          // fontFamily: "Cairo, sans-serif"
          fontFamily: 'Segoe UI Symbol',
        }}
      >
        {t("LC_000150")}
      </Typography>

      <Typography
        variant="body1"
        sx={{
          color: "text.secondary",
          mb: 4,
          // fontFamily: "Cairo, sans-serif",
          fontFamily: 'Segoe UI Symbol',
          maxWidth: "600px"
        }}
      >
        {t("LC_000148")}
      </Typography>

      <Button
        variant="contained"
        href="/"
        startIcon={i18n.language === 'ar' ? <HomeIcon sx={{ ml: 1, }} /> : null}
        endIcon={i18n.language === 'en' ? <HomeIcon /> : null}
        sx={{
          bgcolor: Colors.secondary,
          color: "white",
          px: 4,
          py: 1.5,
          fontSize: "1rem",
          // fontWeight: "bold",
          borderRadius: 8,
          boxShadow: 4,
          // fontFamily: "Cairo, sans-serif",
          fontFamily: 'Segoe UI Symbol',
          transition: "all 0.3s ease",
          "&:hover": {
            bgcolor: Colors.primary,
            transform: "scale(1.05)",
            boxShadow: 6,
          },
        }}
      >
        {t("LC_000149")}
      </Button>
    </Box>
  );
};

export default Unauthorized;

// import React from 'react';
// import { Button, Typography, Box } from "@mui/material";
// import Colors from "./Common/Colors";

// const Unauthorized = () => {
//   return (
//     <div style={{ textAlign: 'center', marginTop: '50px' }}>
//       <h1>Unauthorized</h1>
//       <p>You do not have permission to access this page.</p>
//       <Button
//         variant="contained"
//         href="/"
//         sx={{
//           bgcolor: Colors.secondary,
//           color: "white",
//           fontWeight: "bold",
//           boxShadow: 3,
//           fontFamily: "cairo",
//           transition: "transform 0.2s, background-color 0.3s",
//           "&:hover": {
//             bgcolor: Colors.primary,
//             transform: "scale(1.05)",
//           },
//         }}
//       >
//         Go Back to Home
//       </Button>
//     </div>
//   );
// };

// export default Unauthorized;
