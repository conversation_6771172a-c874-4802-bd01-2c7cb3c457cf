import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import routesConfig from "../../../routesConfig";
import { useAuth } from "../../../AuthProvider";
import StarIcon from "@mui/icons-material/Star";
import { IconButton } from "@mui/material";
import Colors from '../../../Common/Colors';
import { useTranslation } from 'react-i18next';
import Cookies from 'js-cookie';
import { useTheme } from '@mui/material/styles';

export default function CustomizedTreeView() {
  const [filteredPaths, setFilteredPaths] = useState([]);
  const { user, updateFavorites } = useAuth();
  const navigate = useNavigate();
  const favorites = user.favorites || [];
  // const currentColors = Colors[user.compCode] || Colors.default;
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;

  const { t, i18n } = useTranslation();
  const theme = useTheme();

  useEffect(() => {
    if (!Array.isArray(favorites)) return;

    const extractPaths = (routes) => {
      let paths = [];
      routes.forEach(system => {
        system.subSystems?.forEach(subSystem => {
          subSystem.subMenus?.forEach(menu => {
            menu.subObjects?.forEach(obj => {
              paths.push({
                id: obj.id,
                path: obj.path,
                component: obj.component,
                system: system.systemIcon
              });
            });
          });
        });
      });
      return paths;
    };

    const allPaths = extractPaths(routesConfig);
    const filtered = allPaths.filter(item => favorites.includes(item.id));

    setFilteredPaths(prevFilteredPaths => {
      return JSON.stringify(prevFilteredPaths) !== JSON.stringify(filtered) ? filtered : prevFilteredPaths;
    });

  }, [favorites, routesConfig]);


  // const removeFavorite = (itemId) => {
  //   const newFavorites = favorites.filter(id => id !== itemId);
  //   updateFavorites(newFavorites);
  // };

  return (
    <Card variant="outlined" sx={{ display: "flex", flexDirection: "column", gap: "8px", flexGrow: 1 }}>
      <CardContent>
        <Typography component="h2" variant="subtitle2" fontWeight={'bold'} >
          ⭐ {t('LC_000071')}
        </Typography>
        {filteredPaths.length === 0 ? (
          <Typography variant="body2" sx={{ color: "text.secondary", textAlign: "center", mt: 2 }}>
            {t('LC_000068')}
          </Typography>
        ) : (
          <List>
            {Object.entries(
              filteredPaths.reduce((grouped, item) => {
                const systemId = item.id.substring(0, 2);
                if (!grouped[systemId]) grouped[systemId] = [];
                grouped[systemId].push(item);
                return grouped;
              }, {})
            ).map(([systemId, items]) => {
              let systemLabel = systemId;
              const system = routesConfig.find(sys => sys.id === systemId);
              if (system) {
                systemLabel = t(system.id);
              }

              return (
                <React.Fragment key={systemId}>
                  <Typography
                    variant="caption"
                    sx={{
                      fontWeight: 'bold',
                      fontSize: '0.75rem',
                      mt: 2,
                      ml: 1,
                      color: theme.palette.text.secondary,
                      textAlign: i18n.dir() === "rtl" ? "right" : "left",
                      direction: i18n.dir(),
                    }}
                  >
                    {systemLabel}
                  </Typography>
                  {items.map(item => (
                    <ListItem
                      key={item.id}
                      sx={{
                        borderRadius: 2,
                        transition: "background 0.3s ease",
                        "&:hover": { backgroundColor: "#e0f7fa", cursor: "pointer", color: 'black' },
                      }}
                    >
                      <IconButton>
                        <StarIcon style={{ color: "gold" }} />
                      </IconButton>
                      <ListItemText
                        primary={t(item.id)}
                        primaryTypographyProps={{ fontFamily: theme.fontPrimary }}
                        sx={{
                          fontWeight: 500,
                          textAlign: i18n.dir() === "rtl" ? "right" : "left",
                          direction: i18n.dir(),
                          ml: i18n.dir() === "ltr" ? 1 : 0,
                          mr: i18n.dir() === "rtl" ? 1 : 0,
                        }}
                        onClick={() => navigate(item.path)}
                      />
                    </ListItem>
                  ))}
                </React.Fragment>
              );
            })}
          </List>

        )}
      </CardContent>
    </Card>
  );
}