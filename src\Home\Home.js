import * as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Divider from '@mui/material/Divider';
import AppTheme from '../shared-theme/AppTheme';
import AppAppBar from './Components/AppAppBar';
import Hero from './Components/Hero';
import LogoCollection from './Components/LogoCollection';
import Highlights from './Components/Highlights';
import Pricing from './Components/Pricing';
import Features from './Components/Features';
import Testimonials from './Components/Testimonials';
import FAQ from './Components/FAQ';
import Footer from './Components/Footer';

export default function Home(props) {
  return (
    <AppTheme {...props}>
      <CssBaseline enableColorScheme />
      <AppAppBar />
      <Hero />
      <div>
        <LogoCollection />
        {/* <Features />
        <Divider /> */}
        {/* <Testimonials />
        <Divider /> */}
        {/* <Highlights />
        <Divider /> */}

        {/* <Pricing />
        <Divider />
        <FAQ />
        <Divider />
        <Footer /> */}
      </div>
    </AppTheme>
  );
}