# شرح مل<PERSON>.js - مزود المصادقة والتحقق

## نظرة عامة
ملف `AuthProvider.js` هو المكون الأساسي لإدارة المصادقة والتحقق من الهوية في التطبيق. يستخدم React Context API لتوفير حالة المصادقة وبيانات المستخدم لجميع المكونات في التطبيق.

## الاستيرادات (Imports)

### مكتبات React الأساسية
- `React, { createContext, useContext, useState, useEffect }`: للتعامل مع Context وإدارة الحالة
- `axios`: لإجراء طلبات HTTP للخادم
- `CircularProgress`: مكون Material-UI لعرض مؤشر التحميل
- `Cookies`: مكتبة js-cookie للتعامل مع الكوكيز
- `hostURL`: رابط الخادم الأساسي من ملف APIs

## إنشاء Context

```javascript
const AuthContext = createContext(null);
```
إنشاء Context للمصادقة لمشاركة بيانات المستخدم عبر التطبيق.

## المكون الرئيسي AuthProvider

### 1. حالة المستخدم (User State)
```javascript
const [user, setUser] = useState({
  token: '',           // رمز المصادقة
  userCode: '',        // كود المستخدم
  permissions: [],     // صلاحيات المستخدم
  favorites: [],       // المفضلة
  userNameAr: '',      // الاسم بالعربية
  userNameEn: '',      // الاسم بالإنجليزية
  email: '',           // البريد الإلكتروني
  empID: '',           // رقم الموظف
  exNo: '',            // رقم الداخلي
  phoneNo: '',         // رقم الهاتف
  compCode: '',        // كود الشركة
});
```

### 2. متغيرات الحالة الأخرى
- `loading`: حالة التحميل (boolean)
- `isAuthenticated`: حالة المصادقة (يتم حسابها من وجود token)
- `token`: رمز المصادقة المحفوظ في الكوكيز

## الوظائف الرئيسية

### 1. وظيفة fetchUserData
```javascript
const fetchUserData = async () => {
  if (!token) {
    setLoading(false);
    return;
  }
  
  try {
    const response = await axios.get(`${hostURL}/getAuthProviderByUserCode`, {
      headers: { Authorization: `Bearer ${token}` },
    });

    if (response.data.resultCode === '0') {
      const userData = response.data.data;
      setUser({ token, ...userData });
      sessionStorage.setItem('userData', JSON.stringify({ token, ...userData }));
    } else {
      console.error('API Error:', response.data.resultMessageE);
      logout();
    }
  } catch (error) {
    console.error('Error fetching user data:', error);
    logout();
  }
  
  setLoading(false);
};
```

**الغرض**: جلب بيانات المستخدم من الخادم باستخدام token
**الخطوات**:
1. التحقق من وجود token
2. إرسال طلب GET للخادم مع Authorization header
3. في حالة النجاح: حفظ البيانات في الحالة و sessionStorage
4. في حالة الفشل: تسجيل خروج المستخدم

### 2. وظيفة updateFavorites
```javascript
const updateFavorites = (newFavorites) => {
  setUser((prevUser) => {
    const updatedUser = { ...prevUser, favorites: newFavorites };
    sessionStorage.setItem('userData', JSON.stringify(updatedUser));
    return updatedUser;
  });
};
```

**الغرض**: تحديث قائمة المفضلة للمستخدم
**الخطوات**:
1. تحديث حالة المستخدم مع المفضلة الجديدة
2. حفظ البيانات المحدثة في sessionStorage

### 3. وظيفة logout
```javascript
const logout = () => {
  // حذف جميع الكوكيز
  const allCookies = Cookies.get();
  Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
  
  // حذف بيانات المستخدم من sessionStorage
  sessionStorage.removeItem('userData');
  
  // إعادة تعيين حالة المستخدم للقيم الافتراضية
  setUser({
    token: '', userCode: '', permissions: [], favorites: [],
    userNameAr: '', userNameEn: '', email: '', empID: '',
    exNo: '', phoneNo: '', compCode: '',
  });
};
```

**الغرض**: تسجيل خروج المستخدم وتنظيف جميع البيانات المحفوظة

## useEffect Hook - منطق التهيئة

```javascript
useEffect(() => {
  const storedUser = sessionStorage.getItem('userData');
  
  if (storedUser) {
    // إذا كانت البيانات محفوظة في sessionStorage
    const parsedUser = JSON.parse(storedUser);
    setUser(parsedUser);
    setLoading(false);
  } else {
    // إذا لم تكن البيانات محفوظة، جلبها من الخادم
    fetchUserData();
  }
}, [token]);
```

**منطق العمل**:
1. التحقق من وجود بيانات محفوظة في sessionStorage
2. إذا وُجدت: استخدامها مباشرة
3. إذا لم توجد: جلب البيانات من الخادم

## عرض مؤشر التحميل

```javascript
if (loading) {
  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
      <CircularProgress />
    </div>
  );
}
```

عرض مؤشر تحميل في وسط الشاشة أثناء جلب بيانات المستخدم.

## توفير Context

```javascript
return (
  <AuthContext.Provider value={{ user, setUser, isAuthenticated, logout, updateFavorites }}>
    {children}
  </AuthContext.Provider>
);
```

توفير القيم التالية لجميع المكونات الفرعية:
- `user`: بيانات المستخدم الحالي
- `setUser`: وظيفة تحديث بيانات المستخدم
- `isAuthenticated`: حالة المصادقة
- `logout`: وظيفة تسجيل الخروج
- `updateFavorites`: وظيفة تحديث المفضلة

## Hook مخصص useAuth

```javascript
export const useAuth = () => useContext(AuthContext);
```

Hook مخصص لسهولة الوصول لـ AuthContext في أي مكون.

## الميزات الرئيسية

### 1. **إدارة الجلسة التلقائية**
- يتحقق من وجود token في الكوكيز عند بدء التطبيق
- يجلب بيانات المستخدم تلقائياً إذا كان token صالح

### 2. **التخزين المؤقت**
- يحفظ بيانات المستخدم في sessionStorage لتجنب طلبات غير ضرورية
- يستخدم البيانات المحفوظة عند إعادة تحميل الصفحة

### 3. **معالجة الأخطاء**
- يتعامل مع أخطاء الشبكة وأخطاء الخادم
- يسجل خروج المستخدم تلقائياً في حالة انتهاء صلاحية token

### 4. **تنظيف شامل عند الخروج**
- يحذف جميع الكوكيز
- يحذف البيانات من sessionStorage
- يعيد تعيين حالة المستخدم

## استخدام المكون

```javascript
// في App.js أو المكون الجذر
<AuthProvider>
  <App />
</AuthProvider>

// في أي مكون فرعي
const { user, isAuthenticated, logout } = useAuth();
```

هذا المكون يوفر أساساً قوياً لإدارة المصادقة في التطبيق مع دعم للتخزين المؤقت ومعالجة الأخطاء والتنظيف الشامل للبيانات.
