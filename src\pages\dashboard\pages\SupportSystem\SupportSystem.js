import React from 'react'
import { alpha } from '@mui/material/styles';
import AppTheme from '../../../../shared-theme/AppTheme'
import {Box, Stack } from '@mui/material'
import SideMenu from '../../components/SideMenu'
import AppNavbar from '../../components/AppNavbar'
import Header from '../../components/Header'
import CssBaseline from '@mui/material/CssBaseline';
import {
  chartsCustomizations,
  dataGridCustomizations,
  datePickersCustomizations,
  treeViewCustomizations,
} from '../../theme/customizations';
import SupportGrid from './components/SupportGrid';
import { useTranslation } from 'react-i18next';


const xThemeComponents = {
  ...chartsCustomizations,
  ...dataGridCustomizations,
  ...datePickersCustomizations,
  ...treeViewCustomizations,
};

export default function SupportSystem(props) {
   const { i18n,t } = useTranslation();

  return (
    <AppTheme {...props} themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      <Box sx={{ display: 'flex' }}>
        {/* <SideMenu />
        <AppNavbar /> */}
        {/* Main content */}
        <Box
          component="main"
          sx={(theme) => ({
            flexGrow: 1,
            backgroundColor: theme.vars
              ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
              : alpha(theme.palette.background.default, 1),
            overflow: 'auto',
          })}
        >
          <Stack
            spacing={2}
            sx={{
              alignItems: 'center',
              mx: 3,
              pb: 5,
              mt: { xs: 8, md: 0 },
            }}
          >
            <Header pageName={t("0002")}  />
            <SupportGrid pageName={t('0002')}/>
          </Stack>
        </Box>
      </Box>
    </AppTheme>
  );
}
