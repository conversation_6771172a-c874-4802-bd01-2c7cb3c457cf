import React, { useState, useEffect } from 'react';
import { alpha, styled } from '@mui/material/styles';
import MuiDrawer from '@mui/material/Drawer';
import { Box, Divider, IconButton, Avatar, Typography, Stack, useTheme } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import MenuOpenIcon from '@mui/icons-material/MenuOpen';
import MenuContent from './MenuContent';
import Colors from '../../../Common/Colors';
import { useTranslation } from 'react-i18next';
import { gray } from '../../../shared-theme/themePrimitives';
import Cookies from 'js-cookie';
import HistoryIcon from '@mui/icons-material/History';
import Tooltip from '@mui/material/Tooltip';
import axios from 'axios';
import { hostURL } from '../../../Common/APIs';


const drawerWidth = 370;
const collapsedWidth = 61;

const Drawer = styled(MuiDrawer)(({ theme, open, direction }) => ({
  width: open ? drawerWidth : collapsedWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  '& .MuiDrawer-paper': {
    width: open ? drawerWidth : collapsedWidth,
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    overflowX: 'hidden',
    position: 'fixed',
    height: '100vh',
    top: 0,
    ...(direction === 'rtl'
      ? {
        right: 0,
      }
      : {
        left: 0,
      }),
    borderLeft: `1px solid ${theme.palette.mode === 'dark'
      ? alpha(gray[700], 0.6)
      : alpha(gray[300], 0.4)
      }`,
    backgroundColor: theme.palette.background.paper,
  },
}));

const images = [
  { id: "10", src: '/images/STDDP_logo.png', alt: 'STDDP logo' },
  { id: "01", src: '/images/JDECO_logo.png', alt: 'JDECO logo' },
  { id: "20", src: '/images/JFIBER.png', alt: 'JDECO Fiber logo' },
  { id: "30", src: '/images/JLEASING.png', alt: 'JDECO Leasing logo' },
  { id: "40", src: '/images/JLEASING.png', alt: 'JDECO Leasing Westbank logo' },
];

export default function SideMenu() {
  const [open, setOpen] = useState(true);
  const { i18n, t } = useTranslation();
  const direction = i18n.language === 'ar' ? 'rtl' : 'ltr';
  const [resetMenus, setResetMenus] = useState(false);
  const [expanded, setExpanded] = useState([]);
  // const currentColors = Colors[user.compCode] || Colors.default;
  const [profilePic, setProfilePic] = useState("");
  const token = Cookies.get("authToken");

  useEffect(() => {
    const savedState = localStorage.getItem('drawerOpen');
    // console.log('Saved drawer state:', savedState);
    if (savedState !== null) {
      setOpen(savedState === 'true');
    }

    const savedExpanded = JSON.parse(localStorage.getItem('expandedMenus'));
    if (savedExpanded) {
      setExpanded(savedExpanded);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('expandedMenus', JSON.stringify(expanded));
  }, [expanded]);


  useEffect(() => {
    if (!open) {
      setExpanded([]);
    }
  }, [open]);


  const fetchUserProfile = async () => {
    try {
      const response = await axios.post(
        `${hostURL}/getUserProfileInfo`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data.resultCode === "0") {
        const base64String = response.data.data.profilePic;
        const base64Prefix = "data:image/jpeg;base64,";

        setProfilePic(`${base64Prefix}${base64String}`);
        localStorage.setItem('profilePic', `${base64Prefix}${base64String}`);
        window.dispatchEvent(new Event('profilePicChanged'));

      } else {
        console.error(" خطأ في جلب البيانات:", response.data.resultMessageE);
      }
    } catch (error) {
      console.error(" فشل جلب بيانات المستخدم:", error.response?.data || error.message);
    }
  };

  useEffect(() => {
    fetchUserProfile();
  }, []);
  
  const handleToggle = () => {
    const newState = !open;
    console.log('Before setOpen:', open, 'newState:', newState);
    setOpen(newState);
    localStorage.setItem('drawerOpen', newState);
    if (!newState) {
      console.log('Setting resetMenus to true');
      setResetMenus(true);
    } else {
      console.log('Setting resetMenus to false');
      setResetMenus(false);
    }
  };

  const handleOpenDrawer = () => {
    setOpen(true);
    localStorage.setItem('drawerOpen', 'true');
  };

  const onMenusResetHandled = () => {
    setResetMenus(false);
  };

  const theme = useTheme();

  // const userData = JSON.parse(sessionStorage.getItem('userData'));

  const userNameEn = Cookies.get('userNameEn') ? JSON.parse(Cookies.get('userNameEn')) : null;
  // const userCode = Cookies.get('userCode') ? JSON.parse(Cookies.get('userCode')) : null;
  const userNameAr = Cookies.get('userNameAr') ? JSON.parse(Cookies.get('userNameAr')) : null;
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const empID = Cookies.get('empID') ? JSON.parse(Cookies.get('empID')) : null;
  const email = Cookies.get('email') ? JSON.parse(Cookies.get('email')) : null;
  // const phoneNo = Cookies.get('phoneNo') ? JSON.parse(Cookies.get('phoneNo')) : null;
  const lastLoginTimeStamp = Cookies.get('lastLoginTimeStamp');
  // const compCode = userData.compCode;

  const currentColors = Colors[compCode] || Colors.default;

  const selectedImage = images.find((image) => image.id === compCode);
  // const selectedImage = images.find((image) => image.id === user.compCode);

  useEffect(() => {
    const handleProfilePicChange = () => {
      const updatedProfilePic = localStorage.getItem('profilePic');
      setProfilePic(updatedProfilePic);
    };

    window.addEventListener('profilePicChanged', handleProfilePicChange);

    return () => {
      window.removeEventListener('profilePicChanged', handleProfilePicChange);
    };
  }, []);


  return (
    <Drawer
      variant="permanent"
      open={open}
      direction={direction}
      sx={{
        display: { xs: 'none', md: 'block' },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: open ? 'space-between' : 'center',
          alignItems: 'center',
          px: 2,
          py: 1,
        }}
      >
        {open && (
          <Stack
            direction="row"
            sx={{ p: 2, gap: 1, alignItems: 'center', justifyContent: 'space-between' }}
          >
            {/* <Avatar alt="Amr Nassrallah" src="/amr.jpg" sx={{ width: 40, height: 40 }} /> */}
            <Avatar alt={userNameEn} src={profilePic} sx={{ width: 42, height: 42, border: (theme) => `2px solid ${theme.palette.mode === 'dark' ? '#fff' : currentColors.secondary}` }} />
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 500, fontFamily: theme.fontPrimary }}>
                {direction === 'rtl' ? userNameAr : userNameEn}
                {/* {t('LC_000015')} */}
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.secondary', fontFamily: theme.fontPrimary }}>
                {email} | {empID}
                {/* <EMAIL> */}
              </Typography>
            </Box>
          </Stack>
        )}
        <IconButton
          sx={{ color: theme.palette.mode === 'dark' ? 'white' : currentColors.secondary }}
          onClick={handleToggle}
        >
          {open ? <MenuOpenIcon /> : <MenuIcon />}
        </IconButton>
      </Box>
      <Divider />

      <Box sx={{ display: 'flex', justifyContent: 'center', py: 1 }}>
        {selectedImage ? (
          <img
            src={selectedImage.src}
            alt={selectedImage.alt}
            style={{
              width: '70%',
              maxWidth: '140px',
              transition: 'opacity 0.3s',
              margin: '5px',
            }}
          />
          //    <img
          //    src={selectedImage.src}
          //    alt={selectedImage.alt}
          //    style={{
          //      width: '80%',
          //      maxWidth: '150px',
          //      marginBottom: '0px',
          //      margin: '5px',
          //    }}
          //  />
        ) : (
          <span>Image not found</span>
        )}
      </Box>

      <Divider />
      <MenuContent
        isDrawerOpen={open}
        onOpenDrawer={handleOpenDrawer}
        resetMenus={resetMenus}
        onMenusResetHandled={onMenusResetHandled}
        expanded={expanded}
        setExpanded={setExpanded}
      />
      {/* <Divider/>
      <Box sx={{height:130, backgroundColor:''}}>
        {lastLoginTimeStamp}
      </Box> */}

      <Stack
        direction="row"
        sx={{
          p: 2,
          gap: 1,
          alignItems: 'center',
          borderTop: '1px solid',
          borderColor: 'divider',
        }}
      >
        {open ? (
          // <Box sx={{ mr: 'auto', }}>
          <Box dir={i18n.language === 'ar' ? 'rtl' : 'ltr'} sx={{ textAlign: i18n.language === 'ar' ? 'right' : 'left' }}>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              {t('LC_000145')}
            </Typography>
            <Typography
              variant=""
              sx={{
                fontWeight: 500,
                lineHeight: '16px',
                color: currentColors.secondary,
                pl: 1
              }}
            >
              {lastLoginTimeStamp}
            </Typography>
          </Box>
        ) : (
          <Tooltip title={`Last login: ${lastLoginTimeStamp}`} arrow>
            <HistoryIcon sx={{ color: currentColors.secondary, fontSize: 20, cursor: 'pointer' }} />
          </Tooltip>
        )}
      </Stack>

      {/* <Stack
        direction="row"
        sx={{
          p: 2,
          gap: 1,
          alignItems: 'center',
          borderTop: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Box sx={{ mr: 'auto' }}>
          <Typography variant="caption" sx={{ color: 'text.secondary', }}>
            Last login:
          </Typography>
          <Typography variant="" sx={{ fontWeight: 500, lineHeight: '16px', color: currentColors.secondary, pl: 1 }}>
            {lastLoginTimeStamp}
          </Typography>
        </Box>
      </Stack> */}

    </Drawer>
  );
}