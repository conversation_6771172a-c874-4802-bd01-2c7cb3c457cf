/**
 * @license
 * MIT License
 * Copyright (c) 2025 Amr <PERSON>
 */


import React, { createElement, useEffect, useState } from 'react';
import { BrowserRouter, Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { useAuth } from './AuthProvider';
import ProtectedRoute from './ProtectedRoute';
import Unauthorized from './Unauthorized';
import { CssBaseline, ThemeProvider, createTheme, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

import routesConfig from './routesConfig';
import NotFound from './NotFound';
import Dashboard from './pages/dashboard/Dashboard';
import Login from './pages/login/Login';
import SupportSystem from './pages/dashboard/pages/SupportSystem/SupportSystem';

import { COMPONENTS } from './imports';
import SideMenu from './pages/dashboard/components/SideMenu';
import AppTheme from './shared-theme/AppTheme';
import AppNavbar from './pages/dashboard/components/AppNavbar';
import Cookies from 'js-cookie';
import ForgotPassword from './pages/login/components/ForgotPassword';
import Profile from './pages/profile/profile';
import Home from './Home/Home';

const LoginRoute = () => {
  const { setUser } = useAuth();
  const token = Cookies.get('authToken');

  useEffect(() => {
    if (token) {
      setUser({ token });
    }
  }, [token, setUser]);

  if (token) {
    return <Navigate to="/dashboard" replace />;
  }

  return <Login />;
};


function App() {
  const token = Cookies.get('authToken');
  const { i18n } = useTranslation();
  const location = useLocation();
  const [direction, setDirection] = useState('ltr');

  // console.log("111: "+token);
  // console.log("222: "+isAuthenticated);
 const test = (!!token);

  useEffect(() => {
    if (i18n.language === 'ar') {
      document.documentElement.setAttribute('dir', 'rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
    }
  }, [i18n.language]);

  const theme = createTheme({
    direction: direction,
    palette: {
      mode: 'light',
    },
    fontPrimary : 'Segoe UI Symbol',
  });

  const hideSideMenuPaths = ['/login', '/ForgotPassword' , '/home', '/*'];
  const hideSideMenu = hideSideMenuPaths.includes(location.pathname);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppTheme>
        <Box sx={{ display: 'flex' }}>
          {!hideSideMenu && <ProtectedRoute><SideMenu /></ProtectedRoute>}
          {!hideSideMenu && <AppNavbar />}
          <Box
            component="main"
            sx={{
              flexGrow: 1,
              backgroundColor: (theme) => theme.palette.background.default,
            }}
          >
            <Routes>
              <Route path="/login" element={<LoginRoute />} />
              <Route path="/home" element={<Home/>} />
              <Route path="/forgotpassword" element={<ForgotPassword/>} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
              <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
              <Route path="/supportsystem" element={<ProtectedRoute><SupportSystem /></ProtectedRoute>} />
              {routesConfig.map((route) => {
                if (route.subSystems || route.subMenus || route.subObjects) {
                  return route.subSystems?.map((subSystem) =>
                    subSystem.subMenus?.map((menu) =>
                      menu.subObjects?.map((subObject) => (
                        <Route
                          key={subObject.id}
                          path={subObject.path}
                          element={
                            <ProtectedRoute requiredPermission={route.id}>
                              {createElement(COMPONENTS[subObject.component])}
                            </ProtectedRoute>
                          }
                        />
                      ))
                    )
                  );
                }

                return (
                  <Route
                    key={route.id}
                    path={route.path}
                    element={
                      <ProtectedRoute requiredPermission={route.id}>
                        {React.createElement(COMPONENTS[route.component])}
                      </ProtectedRoute>
                    }
                  />
                );
              })}
              <Route path="/unauthorized" element={<Unauthorized />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Box>
        </Box>
      </AppTheme>
    </ThemeProvider>
  );
}



function AppWrapper() {
  return (
    <BrowserRouter>
      <App />
    </BrowserRouter>
  );
}

export default AppWrapper;