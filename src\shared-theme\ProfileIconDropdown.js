import React, { useState } from 'react';
import {
  // Avatar,
  // Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Slide,
  TextField,
  Typography,
  styled,
  Grid,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useFormik } from 'formik';
import * as Yup from 'yup';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import Colors from '../Common/Colors';
import { ThemeProvider, createTheme } from '@mui/material/styles';
//import { useAuth } from '../AuthProvider';
import { hostURL } from '../Common/APIs';
import Cookies from 'js-cookie';


const StyledMenuItem = styled(MenuItem)({
  margin: '2px 0',
});

const Transition = React.forwardRef((props, ref) => (
  <Slide direction="up" ref={ref} {...props} />
));

const validationSchema = Yup.object({
  nameAr: Yup.string().required('Name in Arabic is required'),
  nameEn: Yup.string().required('Name in English is required'),
  empID: Yup.string().required('Employee ID is required'),
  userCode: Yup.string().required('User Code is required'),
  email: Yup.string().email('Invalid email address').required('Email is required'),
  phoneNo: Yup.string().required('Phone number is required'),
  telNo: Yup.string().required('Extension number is required'),
});

const theme = createTheme({
  typography: {
    fontFamily: 'Segoe UI Symbol, Arial, sans-serif',
  },
});


export default function ProfileIconDropdown() {
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  //const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false);

  //const { user, setUser } = useAuth();
  // const currentColors = Colors[user.compCode] || Colors.default;
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;

  const menuOpen = Boolean(menuAnchorEl);
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const handleMenuClick = (event) => setMenuAnchorEl(event.currentTarget);
  const handleMenuClose = () => setMenuAnchorEl(null);

  // const handleDialogOpen = () => {
  //   setDialogOpen(true);
  //   handleMenuClose();
  // };

  const handleProfileRedirect = () => {
    navigate('/profile');
    handleMenuClose();
  };

  
  const handleDialogClose = () => setDialogOpen(false);

  const handleSettingsDialogClose = () => setSettingsDialogOpen(false);
  const handleSettingsDialogOpen = () => {
    setSettingsDialogOpen(true);
    handleMenuClose();
  };

  // const handleResetPasswordDialogOpen = () => {
  //   setResetPasswordDialogOpen(true);
  //   handleMenuClose();
  // };
  // const handleResetPasswordDialogClose = () => setResetPasswordDialogOpen(false);

  const handleLogout = async () => {
    try {
      const token = Cookies.get('authToken');

      if (token) {
        const response = await fetch(`${hostURL}/doLogout`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          console.log('Token invalidated successfully.');
        } else {
          console.error('Failed to invalidate token:', await response.json());
        }
      }

      // Cookies.remove('authToken');
      const allCookies = Cookies.get();
      Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
      console.log("All cookies have been removed!");

      // sessionStorage.removeItem('userData');
      // setUser(null);
      navigate('/login');
      // window.location.reload();
    } catch (error) {
      console.error('An error occurred during logout:', error);
      // Cookies.remove('authToken');
      const allCookies = Cookies.get();
      Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
      console.log("All cookies have been removed!");

      navigate('/login');
      // window.location.reload();
    }
  };

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      empID: (Cookies.get('empID') ? JSON.parse(Cookies.get('empID')) : null) || '',
      userCode: (Cookies.get('userCode') ? JSON.parse(Cookies.get('userCode')) : null) || '',
      nameAr: (Cookies.get('userNameAr') ? JSON.parse(Cookies.get('userNameAr')) : null) || '',
      nameEn: Cookies.get('userNameEn') ? JSON.parse(Cookies.get('userNameEn')) : null || '',
      email: Cookies.get('email') ? JSON.parse(Cookies.get('email')) : null || '',
      phoneNo: Cookies.get('phoneNo') ? JSON.parse(Cookies.get('phoneNo')) : null || '',
      telNo: '6886',
    },
    validationSchema,
    onSubmit: (values) => {
      console.log('Form Data:', values);
      handleDialogClose();
    },
  });

  const isArabic = i18n.language === 'ar';

  const [isDarkTheme, setIsDarkTheme] = useState(false);

  const handleThemeChange = (event) => {
    setIsDarkTheme(event.target.checked);
    console.log('Theme preference:', event.target.checked ? 'Dark' : 'Light');
  };

  return (
    <>
      <IconButton
        onClick={handleMenuClick}
        disableRipple
        size="small"
        aria-controls={menuOpen ? 'profile-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={menuOpen ? 'true' : undefined}
      >
        <ManageAccountsIcon sx={{ color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary }} />
      </IconButton>

      <Menu
        anchorEl={menuAnchorEl}
        id="profile-menu"
        open={menuOpen}
        onClose={handleMenuClose}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      >
        <StyledMenuItem onClick={handleProfileRedirect}>{t('LC_000018')}</StyledMenuItem>

        <Divider />
        {/* <StyledMenuItem onClick={handleResetPasswordDialogOpen}>{t('LC_000079')}</StyledMenuItem> */}
        <StyledMenuItem onClick={() => navigate('/forgotpassword')}>
          {t('LC_000079')}
        </StyledMenuItem>
        <StyledMenuItem onClick={handleSettingsDialogOpen}>{t('LC_000005')}</StyledMenuItem>
        <Divider />
        <StyledMenuItem onClick={handleLogout}>
          <Typography>{t('LC_000011')}</Typography>
          <LogoutRoundedIcon fontSize="small" sx={{ ml: 'auto' }} />
        </StyledMenuItem>
      </Menu>

      {/* Profile Dialog */}
      {/* <ThemeProvider theme={theme}>
        <Dialog
          open={dialogOpen}
          TransitionComponent={Transition}
          onClose={handleDialogClose}
          aria-labelledby="profile-dialog-title"
          PaperProps={{
            sx: { width: '1000px', maxWidth: '1000px', height: '550px', maxHeight: '550px' },
          }}
        >
          <DialogTitle id="profile-dialog-title">{t("LC_000018")}</DialogTitle>
          <form onSubmit={formik.handleSubmit}>
            <DialogContent dividers sx={{ direction: isArabic ? 'rtl' : 'ltr' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Avatar alt="User Name" src="images/FrogFunny.jpg" sx={{ width: 64, height: 64 }} />
                <Typography variant="h6">{t("LC_000019")}</Typography>
              </Box>
              <Divider />
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 3 }}>
                {[
                  { id: 'empID', label: 'EmpID', disabled: true },
                  { id: 'userCode', label: 'UserCode', disabled: true },
                  { id: 'nameAr', label: 'NameAr' },
                  { id: 'nameEn', label: 'NameEn' },
                  { id: 'phoneNo', label: 'PhoneNo' },
                  { id: 'telNo', label: 'ExtensionNo' },
                  { id: 'email', label: 'Email' },
                ].map(({ id, label, disabled }) => (
                  <TextField
                    key={id}
                    id={id}
                    name={id}
                    label={t(label)}
                    variant="outlined"
                    value={formik.values[id]}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched[id] && Boolean(formik.errors[id])}
                    helperText={formik.touched[id] && formik.errors[id]}
                    sx={{
                      flex: '1 1 48%',
                      minWidth: '200px',
                      '& .MuiInputLabel-root': {
                        textAlign: isArabic ? 'right' : 'left',
                        color: 'gray',
                        '&.Mui-focused': {
                          color: currentColors.secondary,
                        },
                        '&.Mui-error': {
                          color: 'red',
                        },
                      },
                      '& .MuiInputBase-root': {
                        textAlign: isArabic ? 'right' : 'left',
                      },
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: 'gray',
                        },
                        '&:hover fieldset': {
                          borderColor: currentColors.secondary,
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: currentColors.secondary,
                        },
                        '&.Mui-error fieldset': {
                          borderColor: 'red',
                        },
                      },
                    }}
                    disabled={disabled}
                  />
                ))}
              </Box>
            </DialogContent>
            <DialogActions
              sx={{
                margin: '10px',
                display: 'flex',
                flexDirection: i18n.language === 'ar' ? 'row-reverse' : 'row',
              }}
            >
              <Button
                onClick={handleDialogClose}
                // variant="outlined"
                variant='text'
                sx={{
                  borderColor: currentColors.secondary,
                  color: currentColors.secondary,
                  marginRight: i18n.language === 'ar' ? '10px' : '0',
                }}
              >
                {t('LC_000016')}
              </Button>
              <Button
                type="submit"
                variant="contained"
                sx={{
                  background: currentColors.secondary,
                  '&:hover': { background: currentColors.primary },
                }}
              >
                {t('LC_000020')}
              </Button>
            </DialogActions>

          </form>
        </Dialog>
      </ThemeProvider> */}

      {/* Settings Dialog */}
      <ThemeProvider theme={theme}>
        <Dialog
          open={settingsDialogOpen}
          TransitionComponent={Transition}
          onClose={handleSettingsDialogClose}
          aria-labelledby="settings-dialog-title"
          PaperProps={{
            sx: {
              width: '600px',
              maxWidth: '100%',
              padding: 3,
            },
          }}
        >
          <DialogTitle id="settings-dialog-title">{t("LC_000005")}</DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={3}>

              <Grid item xs={12}>
                <Typography variant="h6" sx={{ paddingBottom: 2 }}>{t("LC_000001")}</Typography>
                <TextField
                  label={t("LC_000004")}
                  select
                  fullWidth
                  SelectProps={{
                    native: true,
                  }}
                  variant="outlined"
                  value={i18n.language}
                  onChange={(e) => {
                    i18n.changeLanguage(e.target.value);
                    sessionStorage.setItem("preferredLanguage", e.target.value);
                  }}
                >
                  <option value="en">{t('LC_000003')}</option>
                  <option value="ar">{t('LC_000002')}</option>
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6">{t("LC_000006")}</Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={isDarkTheme}
                      onChange={handleThemeChange}
                      color="primary"
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: currentColors.secondary, // اللون للمفعل
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: currentColors.secondary, // لون الخلفية للمفعل
                        },
                        // '& .MuiSwitch-switchBase': {
                        //   color: '#f44336', // اللون لغير المفعل
                        // },
                        // '& .MuiSwitch-switchBase + .MuiSwitch-track': {
                        //   backgroundColor: '#f44336', // لون الخلفية لغير المفعل
                        // },
                      }}
                    />
                  }
                  label={isDarkTheme ? t('LC_000009') : t('LC_000008')}
                />
              </Grid>

              <Grid item xs={12}>
                <Button
                  variant="contained"
                  // color="primary"
                  fullWidth
                  onClick={() => {
                    alert(t("LC_000017"));
                  }}
                  sx={{
                    background: currentColors.secondary,
                    '&:hover': { background: currentColors.primary },
                  }}
                >
                  {t("LC_000007")}
                </Button>
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions>
            <Button
              onClick={handleSettingsDialogClose}
              sx={{
                color: currentColors.secondary
              }}>
              {t("LC_000016")}
            </Button>
          </DialogActions>
        </Dialog>
      </ThemeProvider>

      {/* Reset Password Dialog */}
      {/* <ThemeProvider theme={theme}>
        <Dialog
          open={resetPasswordDialogOpen}
          TransitionComponent={Transition}
          onClose={handleResetPasswordDialogClose}
          aria-labelledby="reset-password-dialog-title"
          PaperProps={{
            sx: { width: '400px', maxWidth: '100%', padding: 3 },
          }}
        >
          <DialogTitle id="reset-password-dialog-title">{t("LC_000079")}</DialogTitle>
          <DialogContent dividers>
            <TextField
              fullWidth
              margin="dense"
              label={t('LC_000085')}
              type="password"
              variant="outlined"
            />
            <TextField
              fullWidth
              margin="dense"
              label={t('LC_000086')}
              type="password"
              variant="outlined"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleResetPasswordDialogClose}
              // variant="outlined"
              variant='text'
              sx={{
                borderColor: currentColors.secondary,
                color: currentColors.secondary,
                marginRight: i18n.language === 'ar' ? '10px' : '0',
              }}
            >
              {t('LC_000016')}
            </Button>
            <Button
              type="submit"
              variant="contained"
              sx={{
                background: currentColors.secondary,
              }}
            >
              {t('LC_000020')}
            </Button>
          </DialogActions>
        </Dialog>
      </ThemeProvider> */}


    </>
  );
}




// import React, { useState } from 'react';
// import {
//   Avatar,
//   Box,
//   Button,
//   Dialog,
//   DialogActions,
//   DialogContent,
//   DialogTitle,
//   Divider,
//   IconButton,
//   Menu,
//   MenuItem,
//   Slide,
//   TextField,
//   Typography,
//   styled,
//   Grid,
//   Switch,
//   FormControlLabel,
// } from '@mui/material';
// import { useTranslation } from 'react-i18next';
// import { useNavigate } from 'react-router-dom';

// import { useFormik } from 'formik';
// import * as Yup from 'yup';
// import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
// import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
// import Colors from '../Common/Colors';
// import { ThemeProvider, createTheme } from '@mui/material/styles';
// //import { useAuth } from '../AuthProvider';
// import { hostURL } from '../Common/APIs';
// import Cookies from 'js-cookie';


// const StyledMenuItem = styled(MenuItem)({
//   margin: '2px 0',
// });

// const Transition = React.forwardRef((props, ref) => (
//   <Slide direction="up" ref={ref} {...props} />
// ));

// const validationSchema = Yup.object({
//   nameAr: Yup.string().required('Name in Arabic is required'),
//   nameEn: Yup.string().required('Name in English is required'),
//   empID: Yup.string().required('Employee ID is required'),
//   userCode: Yup.string().required('User Code is required'),
//   email: Yup.string().email('Invalid email address').required('Email is required'),
//   phoneNo: Yup.string().required('Phone number is required'),
//   telNo: Yup.string().required('Extension number is required'),
// });

// const theme = createTheme({
//   typography: {
//     fontFamily: 'Segoe UI Symbol, Arial, sans-serif',
//   },
// });


// export default function ProfileIconDropdown() {
//   const [menuAnchorEl, setMenuAnchorEl] = useState(null);
//   const [dialogOpen, setDialogOpen] = useState(false);
//   const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
//   //const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false);

//   //const { user, setUser } = useAuth();
//   // const currentColors = Colors[user.compCode] || Colors.default;
//   const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
//   const currentColors = Colors[compCode] || Colors.default;

//   const menuOpen = Boolean(menuAnchorEl);
//   const { t, i18n } = useTranslation();
//   const navigate = useNavigate();

//   const handleMenuClick = (event) => setMenuAnchorEl(event.currentTarget);
//   const handleMenuClose = () => setMenuAnchorEl(null);

//   const handleDialogOpen = () => {
//     setDialogOpen(true);
//     handleMenuClose();
//   };
//   const handleDialogClose = () => setDialogOpen(false);

//   const handleSettingsDialogClose = () => setSettingsDialogOpen(false);
//   const handleSettingsDialogOpen = () => {
//     setSettingsDialogOpen(true);
//     handleMenuClose();
//   };

//   // const handleResetPasswordDialogOpen = () => {
//   //   setResetPasswordDialogOpen(true);
//   //   handleMenuClose();
//   // };
//   // const handleResetPasswordDialogClose = () => setResetPasswordDialogOpen(false);

//   const handleLogout = async () => {
//     try {
//       const token = Cookies.get('authToken');

//       if (token) {
//         const response = await fetch(`${hostURL}/doLogout`, {
//           method: 'GET',
//           headers: {
//             Authorization: `Bearer ${token}`,
//           },
//         });

//         if (response.ok) {
//           console.log('Token invalidated successfully.');
//         } else {
//           console.error('Failed to invalidate token:', await response.json());
//         }
//       }

//       // Cookies.remove('authToken');
//       const allCookies = Cookies.get();
//       Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
//       console.log("All cookies have been removed!");

//       // sessionStorage.removeItem('userData');
//       // setUser(null);
//       navigate('/login');
//       // window.location.reload();
//     } catch (error) {
//       console.error('An error occurred during logout:', error);
//       // Cookies.remove('authToken');
//       const allCookies = Cookies.get();
//       Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
//       console.log("All cookies have been removed!");

//       navigate('/login');
//       // window.location.reload();
//     }
//   };

//   const formik = useFormik({
//     enableReinitialize: true,
//     initialValues: {
//       empID: (Cookies.get('empID') ? JSON.parse(Cookies.get('empID')) : null) || '',
//       userCode: (Cookies.get('userCode') ? JSON.parse(Cookies.get('userCode')) : null) || '',
//       nameAr: (Cookies.get('userNameAr') ? JSON.parse(Cookies.get('userNameAr')) : null) || '',
//       nameEn: Cookies.get('userNameEn') ? JSON.parse(Cookies.get('userNameEn')) : null || '',
//       email: Cookies.get('email') ? JSON.parse(Cookies.get('email')) : null || '',
//       phoneNo: Cookies.get('phoneNo') ? JSON.parse(Cookies.get('phoneNo')) : null || '',
//       telNo: '6886',
//     },
//     validationSchema,
//     onSubmit: (values) => {
//       console.log('Form Data:', values);
//       handleDialogClose();
//     },
//   });

//   const isArabic = i18n.language === 'ar';

//   const [isDarkTheme, setIsDarkTheme] = useState(false);

//   const handleThemeChange = (event) => {
//     setIsDarkTheme(event.target.checked);
//     console.log('Theme preference:', event.target.checked ? 'Dark' : 'Light');
//   };

//   return (
//     <>
//       <IconButton
//         onClick={handleMenuClick}
//         disableRipple
//         size="small"
//         aria-controls={menuOpen ? 'profile-menu' : undefined}
//         aria-haspopup="true"
//         aria-expanded={menuOpen ? 'true' : undefined}
//       >
//         <ManageAccountsIcon sx={{ color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary }} />
//       </IconButton>

//       <Menu
//         anchorEl={menuAnchorEl}
//         id="profile-menu"
//         open={menuOpen}
//         onClose={handleMenuClose}
//         anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
//         transformOrigin={{ horizontal: 'right', vertical: 'top' }}
//       >
//         <StyledMenuItem onClick={handleDialogOpen}>{t('LC_000018')}</StyledMenuItem>
//         <Divider />
//         {/* <StyledMenuItem onClick={handleResetPasswordDialogOpen}>{t('LC_000079')}</StyledMenuItem> */}
//         <StyledMenuItem onClick={() => navigate('/forgotpassword')}>
//           {t('LC_000079')}
//         </StyledMenuItem>
//         <StyledMenuItem onClick={handleSettingsDialogOpen}>{t('LC_000005')}</StyledMenuItem>
//         <Divider />
//         <StyledMenuItem onClick={handleLogout}>
//           <Typography>{t('LC_000011')}</Typography>
//           <LogoutRoundedIcon fontSize="small" sx={{ ml: 'auto' }} />
//         </StyledMenuItem>
//       </Menu>

//       {/* Profile Dialog */}
//       <ThemeProvider theme={theme}>
//         <Dialog
//           open={dialogOpen}
//           TransitionComponent={Transition}
//           onClose={handleDialogClose}
//           aria-labelledby="profile-dialog-title"
//           PaperProps={{
//             sx: { width: '1000px', maxWidth: '1000px', height: '550px', maxHeight: '550px' },
//           }}
//         >
//           <DialogTitle id="profile-dialog-title">{t("LC_000018")}</DialogTitle>
//           <form onSubmit={formik.handleSubmit}>
//             <DialogContent dividers sx={{ direction: isArabic ? 'rtl' : 'ltr' }}>
//               <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
//                 <Avatar alt="User Name" src="images/FrogFunny.jpg" sx={{ width: 64, height: 64 }} />
//                 <Typography variant="h6">{t("LC_000019")}</Typography>
//               </Box>
//               <Divider />
//               <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 3 }}>
//                 {[
//                   { id: 'empID', label: 'EmpID', disabled: true },
//                   { id: 'userCode', label: 'UserCode', disabled: true },
//                   { id: 'nameAr', label: 'NameAr' },
//                   { id: 'nameEn', label: 'NameEn' },
//                   { id: 'phoneNo', label: 'PhoneNo' },
//                   { id: 'telNo', label: 'ExtensionNo' },
//                   { id: 'email', label: 'Email' },
//                 ].map(({ id, label, disabled }) => (
//                   <TextField
//                     key={id}
//                     id={id}
//                     name={id}
//                     label={t(label)}
//                     variant="outlined"
//                     value={formik.values[id]}
//                     onChange={formik.handleChange}
//                     onBlur={formik.handleBlur}
//                     error={formik.touched[id] && Boolean(formik.errors[id])}
//                     helperText={formik.touched[id] && formik.errors[id]}
//                     sx={{
//                       flex: '1 1 48%',
//                       minWidth: '200px',
//                       '& .MuiInputLabel-root': {
//                         textAlign: isArabic ? 'right' : 'left',
//                         color: 'gray',
//                         '&.Mui-focused': {
//                           color: currentColors.secondary,
//                         },
//                         '&.Mui-error': {
//                           color: 'red',
//                         },
//                       },
//                       '& .MuiInputBase-root': {
//                         textAlign: isArabic ? 'right' : 'left',
//                       },
//                       '& .MuiOutlinedInput-root': {
//                         '& fieldset': {
//                           borderColor: 'gray',
//                         },
//                         '&:hover fieldset': {
//                           borderColor: currentColors.secondary,
//                         },
//                         '&.Mui-focused fieldset': {
//                           borderColor: currentColors.secondary,
//                         },
//                         '&.Mui-error fieldset': {
//                           borderColor: 'red',
//                         },
//                       },
//                     }}
//                     disabled={disabled}
//                   />
//                 ))}
//               </Box>
//             </DialogContent>
//             <DialogActions
//               sx={{
//                 margin: '10px',
//                 display: 'flex',
//                 flexDirection: i18n.language === 'ar' ? 'row-reverse' : 'row',
//               }}
//             >
//               <Button
//                 onClick={handleDialogClose}
//                 // variant="outlined"
//                 variant='text'
//                 sx={{
//                   borderColor: currentColors.secondary,
//                   color: currentColors.secondary,
//                   marginRight: i18n.language === 'ar' ? '10px' : '0',
//                 }}
//               >
//                 {t('LC_000016')}
//               </Button>
//               <Button
//                 type="submit"
//                 variant="contained"
//                 sx={{
//                   background: currentColors.secondary,
//                   '&:hover': { background: currentColors.primary },
//                 }}
//               >
//                 {t('LC_000020')}
//               </Button>
//             </DialogActions>

//           </form>
//         </Dialog>
//       </ThemeProvider>

//       {/* Settings Dialog */}
//       <ThemeProvider theme={theme}>
//         <Dialog
//           open={settingsDialogOpen}
//           TransitionComponent={Transition}
//           onClose={handleSettingsDialogClose}
//           aria-labelledby="settings-dialog-title"
//           PaperProps={{
//             sx: {
//               width: '600px',
//               maxWidth: '100%',
//               padding: 3,
//             },
//           }}
//         >
//           <DialogTitle id="settings-dialog-title">{t("LC_000005")}</DialogTitle>
//           <DialogContent dividers>
//             <Grid container spacing={3}>

//               <Grid item xs={12}>
//                 <Typography variant="h6" sx={{ paddingBottom: 2 }}>{t("LC_000001")}</Typography>
//                 <TextField
//                   label={t("LC_000004")}
//                   select
//                   fullWidth
//                   SelectProps={{
//                     native: true,
//                   }}
//                   variant="outlined"
//                   value={i18n.language}
//                   onChange={(e) => {
//                     i18n.changeLanguage(e.target.value);
//                     sessionStorage.setItem("preferredLanguage", e.target.value);
//                   }}
//                 >
//                   <option value="en">{t('LC_000003')}</option>
//                   <option value="ar">{t('LC_000002')}</option>
//                 </TextField>
//               </Grid>

//               <Grid item xs={12}>
//                 <Typography variant="h6">{t("LC_000006")}</Typography>
//                 <FormControlLabel
//                   control={
//                     <Switch
//                       checked={isDarkTheme}
//                       onChange={handleThemeChange}
//                       color="primary"
//                       sx={{
//                         '& .MuiSwitch-switchBase.Mui-checked': {
//                           color: currentColors.secondary, // اللون للمفعل
//                         },
//                         '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
//                           backgroundColor: currentColors.secondary, // لون الخلفية للمفعل
//                         },
//                         // '& .MuiSwitch-switchBase': {
//                         //   color: '#f44336', // اللون لغير المفعل
//                         // },
//                         // '& .MuiSwitch-switchBase + .MuiSwitch-track': {
//                         //   backgroundColor: '#f44336', // لون الخلفية لغير المفعل
//                         // },
//                       }}
//                     />
//                   }
//                   label={isDarkTheme ? t('LC_000009') : t('LC_000008')}
//                 />
//               </Grid>

//               <Grid item xs={12}>
//                 <Button
//                   variant="contained"
//                   // color="primary"
//                   fullWidth
//                   onClick={() => {
//                     alert(t("LC_000017"));
//                   }}
//                   sx={{
//                     background: currentColors.secondary
//                   }}
//                 >
//                   {t("LC_000007")}
//                 </Button>
//               </Grid>
//             </Grid>
//           </DialogContent>

//           <DialogActions>
//             <Button
//               onClick={handleSettingsDialogClose}
//               sx={{
//                 color: currentColors.secondary
//               }}>
//               {t("LC_000016")}
//             </Button>
//           </DialogActions>
//         </Dialog>
//       </ThemeProvider>

//       {/* Reset Password Dialog */}
//       {/* <ThemeProvider theme={theme}>
//         <Dialog
//           open={resetPasswordDialogOpen}
//           TransitionComponent={Transition}
//           onClose={handleResetPasswordDialogClose}
//           aria-labelledby="reset-password-dialog-title"
//           PaperProps={{
//             sx: { width: '400px', maxWidth: '100%', padding: 3 },
//           }}
//         >
//           <DialogTitle id="reset-password-dialog-title">{t("LC_000079")}</DialogTitle>
//           <DialogContent dividers>
//             <TextField
//               fullWidth
//               margin="dense"
//               label={t('LC_000085')}
//               type="password"
//               variant="outlined"
//             />
//             <TextField
//               fullWidth
//               margin="dense"
//               label={t('LC_000086')}
//               type="password"
//               variant="outlined"
//             />
//           </DialogContent>
//           <DialogActions>
//             <Button onClick={handleResetPasswordDialogClose}
//               // variant="outlined"
//               variant='text'
//               sx={{
//                 borderColor: currentColors.secondary,
//                 color: currentColors.secondary,
//                 marginRight: i18n.language === 'ar' ? '10px' : '0',
//               }}
//             >
//               {t('LC_000016')}
//             </Button>
//             <Button
//               type="submit"
//               variant="contained"
//               sx={{
//                 background: currentColors.secondary,
//               }}
//             >
//               {t('LC_000020')}
//             </Button>
//           </DialogActions>
//         </Dialog>
//       </ThemeProvider> */}


//     </>
//   );
// }

