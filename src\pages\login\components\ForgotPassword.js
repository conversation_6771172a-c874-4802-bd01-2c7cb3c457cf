import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Card,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
  FormControlLabel,
  TextField,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  CssBaseline,
  Stack,IconButton
} from "@mui/material";
import { styled } from "@mui/material/styles";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { useTranslation } from 'react-i18next';
import LanguageIconSignIn from "./LanguageIconSignIn";
import { hostURL } from '../../../Common/APIs';
import Header from '../../dashboard/components/Header';
import AppTheme from '../../../shared-theme/AppTheme';
import {
  chartsCustomizations,
  dataGridCustomizations,
  datePickersCustomizations,
  treeViewCustomizations,
} from '../../../pages/dashboard/theme/customizations';
import Cookies from "js-cookie";
import Colors from '../../../Common/Colors';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';


const xThemeComponents = {
  ...chartsCustomizations,
  ...dataGridCustomizations,
  ...datePickersCustomizations,
  ...treeViewCustomizations,
};

const StyledCard = styled(Card)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  width: "100%",
  padding: theme.spacing(4),
  gap: theme.spacing(2),
  boxShadow:
    "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
  [theme.breakpoints.up("sm")]: {
    width: "450px",
  },
  [theme.breakpoints.down("sm")]: {
    width: "90vw",
  },
}));

export default function ForgotPassword(props) {
  const [userName, setUserName] = useState("");
  const [sendMethod, setSendMethod] = useState("email");
  const [verificationCode, setVerificationCode] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [forgotPassToken, setForgotPassToken] = useState("");
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const isLoggedIn = !!Cookies.get("authToken");
  const user = Cookies.get("userCode") ? JSON.parse(Cookies.get("userCode")) : "";
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;

  useEffect(() => {
    const storedLanguage = sessionStorage.getItem('language');
    i18n.changeLanguage(storedLanguage);
    document.documentElement.setAttribute('dir', storedLanguage === 'ar' ? 'rtl' : 'ltr');
  }, [i18n]);

  useEffect(() => {
    if (isLoggedIn && user) {
      setUserName(user);
    }
  }, [isLoggedIn, user]);


  const validatePassword = (password) => {
    const minLength = /.{8,}/;
    const hasUpperCase = /[A-Z]/;
    const hasLowerCase = /[a-z]/;
    const hasNumber = /\d/;
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/;

    if (!minLength.test(password)) return "Password must be at least 8 characters.";
    if (!hasUpperCase.test(password)) return "Password must contain at least one uppercase letter.";
    if (!hasLowerCase.test(password)) return "Password must contain at least one lowercase letter.";
    if (!hasNumber.test(password)) return "Password must contain at least one number.";
    if (!hasSpecialChar.test(password)) return "Password must contain at least one special character.";

    return "";
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setErrorMessage("");
    setLoading(true);

    try {
      const response = await axios.post(
        `${hostURL}/processResetPassword`,
        {
          userName,
          sendMethod,
          thirdPartyAppId: "STDDP-WEB",
          lang: sessionStorage.getItem('language'),
        }
      );

      if (response.data.resultCode === "0") {
        setForgotPassToken(response.data.data.token);
        setOpenDialog(true);
      } else {
        const lang = i18n.language || 'en';
        const message = lang === 'ar' ? response.data.resultMessageA : response.data.resultMessageE;
        setErrorMessage(message || "Error occurred, please try again.");
        // setErrorMessage(
        //   response.data.resultMessageE || "Error occurred, please try again."
        // );
      }
    } catch (error) {
      setErrorMessage("Failed to send request. Please check your connection.");
      console.error("API error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    setErrorMessage("");
    setLoading(true);

    // التحقق من تعقيد كلمة المرور قبل إرسال الطلب
    const passwordError = validatePassword(newPassword);
    if (passwordError) {
      setErrorMessage(passwordError);
      setLoading(false);
      return;
    }

    if (newPassword !== confirmNewPassword) {
      setErrorMessage(t('LC_000141'));
      setLoading(false);
      return;
    }

    try {
      const response = await axios.post(
        `${hostURL}/doResetPassword`,
        {
          token: forgotPassToken,
          verificationCode,
          newPassword,
          confirmNewPassword,
          lang: "en",
        }
      );

      if (response.data.resultCode === "0") {
        alert("Password reset successful. Please login with your new password.");
        setOpenDialog(false);

        setTimeout(() => {
          navigate("/login");
        }, 2000);

        if (isLoggedIn) {
          handleLogout();
          navigate('/login');
        }
      } else {
        // setErrorMessage(response.data.resultMessageE || "Verification failed.");
        const lang = i18n.language || 'en';
        const message = lang === 'ar' ? response.data.resultMessageA : response.data.resultMessageE;
        setErrorMessage(message || "Error occurred, please try again.");

      }
    } catch (error) {
      setErrorMessage("Failed to verify code. Please try again.");
      console.error("API error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      const token = Cookies.get('authToken');

      if (token) {
        const response = await fetch(`${hostURL}/doLogout`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          console.log('Token invalidated successfully.');
        } else {
          console.error('Failed to invalidate token:', await response.json());
        }
      }

      // Cookies.remove('authToken');
      const allCookies = Cookies.get();
      Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
      console.log("All cookies have been removed!");

      // sessionStorage.removeItem('userData');
      // setUser(null);
      navigate('/login');
      // window.location.reload();
    } catch (error) {
      console.error('An error occurred during logout:', error);
      // Cookies.remove('authToken');
      const allCookies = Cookies.get();
      Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
      console.log("All cookies have been removed!");

      navigate('/login');
      // window.location.reload();
    }
  };


  return (
    <AppTheme {...props} themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      <Stack
        spacing={2}
        sx={{
          alignItems: 'center',
          mx: 3,
          pb: 5,
          mt: { xs: 8, md: 0 },
        }}
      >
        {isLoggedIn && <Header pageName="LC_000079" />}
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "85vh",
            //  backgroundColor: theme.vars
            //              ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
            //              : alpha(theme.palette.background.default, 1),
          }}
        >
          {!isLoggedIn && <LanguageIconSignIn
            sx={{
              position: 'fixed',
              top: '2rem',
              right: '1rem',
              zIndex: 1000,
            }}
          />}

          {!isLoggedIn && (
            <IconButton
              onClick={() => navigate("/login")}
              sx={{

                position: 'fixed',
                top: '1rem',
                left: '1rem',
                zIndex: 1000,
                color: currentColors.secondary,
                borderColor: currentColors.secondary,
                minWidth: '40px',
                border: '1px solid',
                '&:hover': {
                  backgroundColor: currentColors.secondary,
                  color: '#fff',
                },
              }}
            >
              <ArrowBackIcon />
            </IconButton>
          )}


          <StyledCard variant="outlined">
            <Typography component="h1" variant="h5" sx={{ textAlign: "center" }}>
              {t('LC_000079')}
            </Typography>

            <Box
              component="form"
              onSubmit={handleSubmit}
              sx={{ display: "flex", flexDirection: "column", gap: 2 }}
            >
              <FormControl>
                <FormLabel>{t('LC_000075')}</FormLabel>
                <TextField
                  id="userName"
                  placeholder={t('LC_000121')}
                  value={isLoggedIn ? user : userName}
                  onChange={(e) => setUserName(e.target.value)}
                  required={!isLoggedIn}
                  disabled={isLoggedIn}
                />
              </FormControl>

              <FormControl component="fieldset">
                <FormLabel>{t('LC_000082')}</FormLabel>
                <RadioGroup
                  row
                  value={sendMethod}
                  onChange={(e) => setSendMethod(e.target.value)}
                >
                  <FormControlLabel value="email" control={<Radio />} label={t('LC_000081')} />
                  <FormControlLabel value="sms" control={<Radio />} label={t('LC_000080')} />
                </RadioGroup>
              </FormControl>

              {errorMessage && (
                <Typography color="error" sx={{ textAlign: "center" }}>
                  {errorMessage}
                </Typography>
              )}

              <Button type="submit" variant="" sx={{ background: currentColors.secondary, color: 'white', '&:hover': { background: currentColors.primary }, }}>
                {loading ? <CircularProgress size={24} /> : t('LC_000083')}
              </Button>
            </Box>
            {!openDialog && (
              <Button
                onClick={() => setOpenDialog(true)}
              >
                {t('LC_000111')}
              </Button>
            )}
          </StyledCard>

          <Dialog open={openDialog} onClose={() => setOpenDialog(false)} sx={{ '& .MuiDialog-paper': { padding: '16px', borderRadius: 2, boxShadow: 3 } }}>
            <DialogTitle sx={{ fontWeight: 'bold', textAlign: 'center', fontSize: '1.25rem' }}>{t('LC_000084')}</DialogTitle>
            <DialogContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>

              <TextField
                fullWidth
                id="verificationCode"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder={t('LC_000084')}
                variant="outlined"
                sx={{
                  paddingTop: 2,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root.Mui-focused': {
                    borderColor: 'transparent',
                  },
                }}
              />
              <TextField
                fullWidth
                type="password"
                placeholder={t('LC_000085')}
                value={newPassword}
                onChange={(e) => { setNewPassword(e.target.value); setErrorMessage(""); }}
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': { borderRadius: 2, },
                  '& .MuiOutlinedInput-root.Mui-focused': { borderColor: 'transparent', },
                }}
              />
              <TextField
                fullWidth
                type="password"
                placeholder={t('LC_000086')}
                value={confirmNewPassword}
                onChange={(e) => setConfirmNewPassword(e.target.value)}
                variant="outlined"
                error={!!errorMessage} // تغيير لون الحقل عند وجود خطأ
                helperText={errorMessage} // إظهار رسالة الخطأ تحت الحقل
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  },
                  '& .MuiOutlinedInput-root.Mui-focused': {
                    borderColor: 'transparent',
                  },
                }}
              />
            </DialogContent>

            <DialogActions sx={{ justifyContent: 'space-between' }}>
              <Button onClick={() => setOpenDialog(false)} color="primary" sx={{ fontWeight: 'bold' }}>
                {t('LC_000087')}
              </Button>
              <Button onClick={handleVerifyCode} color="secondary" disabled={loading} sx={{ fontWeight: 'bold' }}>
                {loading ? <CircularProgress size={24} /> : t('LC_000088')}
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </Stack>
    </AppTheme>
  );
}
