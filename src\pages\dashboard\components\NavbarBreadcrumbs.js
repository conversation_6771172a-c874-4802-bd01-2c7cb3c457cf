import * as React from 'react';
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Breadcrumbs, { breadcrumbsClasses } from '@mui/material/Breadcrumbs';
import NavigateNextRoundedIcon from '@mui/icons-material/NavigateNextRounded';
import NavigateBeforeRoundedIcon from '@mui/icons-material/NavigateBeforeRounded';
import { useTranslation } from 'react-i18next';



const StyledBreadcrumbs = styled(Breadcrumbs)(({ theme }) => ({
  margin: theme.spacing(1, 0),
  [`& .${breadcrumbsClasses.separator}`]: {
    color: (theme.vars || theme).palette.action.disabled,
    margin: 1,
  },
  [`& .${breadcrumbsClasses.ol}`]: {
    alignItems: 'center',
  },
}));

export default function NavbarBreadcrumbs({pageName}) {
    const { t, i18n } = useTranslation();
  
    const separator = i18n.language === 'ar' ? (
      <NavigateBeforeRoundedIcon fontSize="small" />
    ) : (
      <NavigateNextRoundedIcon fontSize="small" />
    );

  return (
    <StyledBreadcrumbs
      aria-label="breadcrumb"
      separator={separator}
    >
      <Typography variant="body1">{t("LC_000013")}</Typography>
      <Typography variant="body1" sx={{ color: 'text.primary', fontWeight: 600 }}>
        {t(pageName)}
      </Typography>
    </StyledBreadcrumbs>
  );
}
