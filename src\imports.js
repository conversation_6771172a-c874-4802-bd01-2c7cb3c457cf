//BasicSystem/SetupModuleSystem
import RepReportsLogVw from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Reports/RepReportsLogVw';
import RepUserPrivilege from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Reports/RepUserPrivilege';
import BankBranchesAccounts from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/BankBranchesAccounts';
import ComBanks from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/ComBanks';
import ComContactTypes from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/ComContactTypes';
import ComIdTypes from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/ComIdTypes';
import ComLookups from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/ComLookups';
import Companies from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/Companies';
import ComPrefixSufix from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/ComPrefixSufix';
import Countries from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/Countries';
import Currencies from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/Currencies';
import HiddenForms from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/HiddenForms';
import NotificationTypes from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/NotificationTypes';
import RelationsType from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/RelationsType';
import SystemCompanies from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/SystemCompanies';
import SystemNotificationsSetup from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/SystemNotificationsSetup';
import SystemNotificationsVariables from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/SystemNotificationsVariables';
import Systems from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/Systems';
import SystemUserParameters from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/SystemUserParameters';
import TaxTypes from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/TaxTypes';
import Templates from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/Templates';
import UserParameters from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/UserParameters';
import Users from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Setup/Users';
import ReportsLogVw from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SetupModuleSystem/Transactions/ReportsLogVw';
//BasicSystem/SupportSystem/Settings
import SupportEmployees from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Settings/SupportEmployees';
import SupportCustomers from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Settings/SupportCustomers';
import SupportTypes from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Settings/SupportTypes';
import SupportExtraSystems from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Settings/SupportExtraSystems';
//BasicSystem/SupportSystem/Transactions
import SupportRequests from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Transactions/SupportRequests';
import SupportDailyReports from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Transactions/SupportDailyReports';
import SupportFollows from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Transactions/SupportFollows';
//BasicSystem/SupportSystem/Reports
import SupportRequestsDetails from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/SupportSystem/Reports/SupportRequestsDetails';
//BasicSystem/TabletsSystem/Settings
import TabletDevices  from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/TabletsSystem/Settings/TabletDevices';
import TabletSims from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/TabletsSystem/Settings/TabletSims';
//BasicSystem/TabletsSystem/Transactions
import TabletDevicesUsers from './pages/dashboard/pages/Systems/BasicSystem/SubSystems/TabletsSystem/Transactions/TabletDevicesUsers';


//HHUSystem/HHUSystem/Reports
import RepHhuAppendServices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuAppendServices';
import RepHhuReadings from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuReadings';
import RepHhuInvoices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuInvoices';
import RepHhuReadingPeriod from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuReadingPeriod';
import RepHhuDailySummary from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuDailySummary';
import RepHhuNotes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuNotes';
import RepHhuPeriodSummary from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuPeriodSummary';
import RepHhuDisServicesBranches from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuDisServicesBranches';
import RepHhuConServices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuConServices';
import RepHhuPaymentsUnsent from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuPaymentsUnsent';
import RepHhuMonthlySummary from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuMonthlySummary';
import RepHhuOddCases from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuOddCases';
import RepHhuDisServices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuDisServices';
import RepHhuErrors from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuErrors';
import RepHhuReadingsByStatus from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuReadingsByStatus';
import RepHhuUnenteredServices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuUnenteredServices';
import RepHhuPayments from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuPayments';
import RepHhuUnreadServices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuUnreadServices';
import RepHhuInvoicesDiff from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuInvoicesDiff';
import RepHhuTariffTypesRegions from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuTariffTypesRegions';
import RepHhuEstimatedReadings from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuEstimatedReadings';
import RepHhuNotice from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuNotice';
import RepHhuRepeatedServices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Reports/RepHhuRepeatedServices';
//HHUSystem/HHUSystem/Settings
import HhuStatus from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuStatus';
import HhuCustomerTypes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuCustomerTypes';
import HhuVatTypes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuVatTypes';
import HhuServiceTypes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuServiceTypes';
import HhuCycleTypes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuCycleTypes';
import HhuMeterStatus from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuMeterStatus';
import HhuPeriodicalServices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuPeriodicalServices';
import HhuTransactionCodes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuTransactionCodes';
import HhuTariffTypes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuTariffTypes';
import HhuPenaltyFactors from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuPenaltyFactors';
import HhuDevices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuDevices';
import HhuReaders from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuReaders';
import HhuCalcTypes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuCalcTypes';
import CompaniesSystemParamHHUSystem from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/CompaniesSystemParam';
import HhuInvoiceNotes from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuInvoiceNotes';
import HhuHolidays from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuHolidays';
import HhuServiceCategory from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuServiceCategory';
import HhuExtraData from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Settings/HhuExtraData';
//HHUSystem/HHUSystem/Transactions
import HhuReadings from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuReadings';
import HhuInvoicesForm from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuInvoicesForm';
import HhuServiceDetails from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuServiceDetails';
import HhuPatches from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuPatches';
import HhuDevicesSummary from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuDevicesSummary';
import HhuLog from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuLog';
import HhuErrorsLog from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuErrorsLog';
import HhuAppendService from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuAppendService';
import HhuInvoices from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuInvoices';
import HhuGpsStatVw from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuGpsStatVw';
import HhuPatchesDailyStatusVw from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/HHUSystem/Transactions/HhuPatchesDailyStatusVw';
//HHUSystem/InspectionSystem/Reports
import RepHhuInsByReader from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsByReader';
import RepHhuInsBySbranch from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsBySbranch';
import RepHhuInsBySbranchAppl from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsBySbranchAppl';
import RepHhuInsByService from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsByService';
import RepHhuInsLateProcess from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsLateProcess';
import RepHhuInsMeterStatusStat from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsMeterStatusStat';
import RepHhuInsProcessByInspector from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsProcessByInspector';
import RepHhuInsProcessRepSrvs from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsProcessRepSrvs';
import RepHhuInsTransRepStatistic from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuInsTransRepStatistic';
import RepHhuReaderPoints from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Reports/RepHhuReaderPoints';
//HHUSystem/InspectionSystem/Settings
import HhuInspectionTrans from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Settings/HhuInspectionTrans';
import HhuInspectors from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Settings/HhuInspectors';
//HHUSystem/InspectionSystem/Transactions
import HhuInspection from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Transactions/HhuInspection';
import HhuInspectionPointsApproval from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Transactions/HhuInspectionPointsApproval';
import HhuInspectionProcess from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Transactions/HhuInspectionProcess';
import HhuInspectionProcessEntrybyinspector from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Transactions/HhuInspectionProcessEntrybyinspector';
import HhuInspectionProcessEntrybyservice from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Transactions/HhuInspectionProcessEntrybyservice';
import HhuInspectionProcessFollowup from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Transactions/HhuInspectionProcessFollowup';
import HhuInsPrepaidManualEntry from './pages/dashboard/pages/Systems/HHUSystem/SubSystems/InspectionSystem/Transactions/HhuInsPrepaidManualEntry';
//ISOSystems/FollowupDailyProceduresSystem/FollowupProcedures
import IsoDocumentsProcesses from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/FollowupDailyProceduresSystem/FollowupProcedures/IsoDocumentsProcesses';
import IsoDocumentsProcessesLog from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/FollowupDailyProceduresSystem/FollowupProcedures/IsoDocumentsProcessesLog';
import IsoDocumentsProcessesLogReview from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/FollowupDailyProceduresSystem/FollowupProcedures/IsoDocumentsProcessesLogReview';
//ISOSystems/QualityManagementSystem/AuditPlan
import IsoAuditPlan from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/IsoAuditPlan';
import IsoAuditPlanAuditSeqVw from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/IsoAuditPlanAuditSeqVw';
import IsoAuditPlanNotesReview from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/IsoAuditPlanNotesReview';
import IsoAuditPlanSchedule from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/IsoAuditPlanSchedule';
import IsoAuditPlanScheduleEdit from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/IsoAuditPlanScheduleEdit';
import IsoAuditYear from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/IsoAuditYear';
import IsoCorrectiveActions from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/IsoCorrectiveActions';
import RepIsoAuditorSummaryRep from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditorSummaryRep';
import RepIsoAuditPlan from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlan';
import RepIsoAuditPlanComparison from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlanComparison';
import RepIsoAuditPlanComparisonH from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlanComparisonH';
import RepIsoAuditPlanNotes from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlanNotes';
import RepIsoAuditPlanNotesMgr from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlanNotesMgr';
import RepIsoAuditPlanNotesRev from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlanNotesRev';
import RepIsoAuditPlanSchedule from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlanSchedule';
import RepIsoAuditPlanSummary from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoAuditPlanSummary';
import RepIsoCorrectiveActions from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoCorrectiveActions';
import RepIsoPlanKeysMatrix from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/AuditPlan/RepIsoPlanKeysMatrix';
//ISOSystems/QualityManagementSystem/Reports
import RepIsoUpdatedDocuments from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoUpdatedDocuments';
import RepIsoNewDocuments from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoNewDocuments';
import RepIsoDelDocuments from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoDelDocuments';
import RepIsoRequests from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoRequests';
import RepIsoMainMenu from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoMainMenu';
import RepIsoTemplatesEmployees from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoTemplatesEmployees';
import RepIsoTemplatesEmployeesE from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoTemplatesEmployeesE';
import RepIsoEmployeesDiv from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoEmployeesDiv';
import RepIsoDocumentRecords from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Reports/RepIsoDocumentRecords';
//ISOSystems/QualityManagementSystem/Settings
import IsoDocumentTypes from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoDocumentTypes';
import IsoOrgTreeDiv from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoOrgTreeDiv';
import IsoOrgTreeDep from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoOrgTreeDep';
import IsoCorrectiveActionSources from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoCorrectiveActionSources';
import IsoUnconformityReasons from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoUnconformityReasons';
import IsoPlanKeys from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoPlanKeys';
import IsoHolidays from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoHolidays'; 
import IsoTemplates from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoTemplates'; 
import IsoReportFormat from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoReportFormat';
import IsoEmployees from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/IsoEmployees';
import CompaniesSystemParamISOSystem from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Settings/CompaniesSystemParam';
//ISOSystems/QualityManagementSystem/Transactions
import IsoDocumentsTemp from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Transactions/IsoDocumentsTemp';
import IsoDocumentsH from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Transactions/IsoDocumentsH';
import IsoDocuments from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Transactions/IsoDocuments';
import IsoTemplatesEmployees from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Transactions/IsoTemplatesEmployees';
import IsoDocumentsEmployees from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Transactions/IsoDocumentsEmployees';
import IsoRequests from './pages/dashboard/pages/Systems/ISOSystems/SubSystems/QualityManagementSystem/Transactions/IsoRequests';



export const COMPONENTS = {
//BasicSystem/SetupModuleSystem
    //Reports
    RepReportsLogVw, RepUserPrivilege, 
    // Setup
    BankBranchesAccounts, ComBanks, ComContactTypes, ComIdTypes, ComLookups, Companies, ComPrefixSufix, Countries, Currencies, HiddenForms, 
    NotificationTypes, RelationsType, SystemCompanies, SystemNotificationsSetup, SystemNotificationsVariables, Systems,
    SystemUserParameters, TaxTypes, Templates, UserParameters, Users,
    // Transactions
    ReportsLogVw, 
//BasicSystem/SupportSystem
    //Settings
    SupportEmployees,SupportCustomers,SupportTypes,SupportExtraSystems,
    //Transactions
    SupportRequests,SupportDailyReports,SupportFollows,
    //Reports
    SupportRequestsDetails,
//BasicSystem/TabletsSystem
    //Settings
    TabletDevices,TabletSims,
    //Transactions
    TabletDevicesUsers,


//HHUSystem/HHUSystem
    // Reports
    RepHhuAppendServices, RepHhuReadings, RepHhuInvoices, RepHhuReadingPeriod, RepHhuDailySummary, RepHhuNotes, RepHhuPeriodSummary, RepHhuDisServicesBranches,  
    RepHhuConServices, RepHhuPaymentsUnsent, RepHhuMonthlySummary, RepHhuOddCases, RepHhuDisServices, RepHhuErrors, RepHhuReadingsByStatus, RepHhuUnenteredServices, RepHhuPayments,
    RepHhuUnreadServices, RepHhuInvoicesDiff, RepHhuTariffTypesRegions, RepHhuEstimatedReadings, RepHhuNotice, RepHhuRepeatedServices,
    // Settings
    HhuStatus, HhuCustomerTypes, HhuVatTypes, HhuServiceTypes, HhuCycleTypes, HhuMeterStatus, HhuPeriodicalServices, HhuTransactionCodes, 
    HhuTariffTypes, HhuPenaltyFactors, HhuServiceTypes, HhuDevices, HhuReaders, HhuCalcTypes, CompaniesSystemParamHHUSystem, HhuInvoiceNotes, HhuHolidays, HhuServiceCategory, HhuExtraData,
    // Transactions
    HhuReadings, HhuInvoicesForm, HhuServiceDetails, HhuPatches, HhuDevicesSummary, HhuLog, HhuErrorsLog, HhuAppendService, HhuInvoices, HhuGpsStatVw, HhuPatchesDailyStatusVw, 
//HHUSystem/InspectionSystem
    //Reports
    RepHhuInsByReader, RepHhuInsBySbranch, RepHhuInsBySbranchAppl, RepHhuInsByService, RepHhuInsLateProcess,RepHhuInsMeterStatusStat,RepHhuInsProcessByInspector,RepHhuInsProcessRepSrvs,
    RepHhuInsTransRepStatistic,RepHhuReaderPoints,
    //Settings
    HhuInspectionTrans,HhuInspectors,
    //Transactions
    HhuInspection,HhuInspectionPointsApproval,HhuInspectionProcess,HhuInspectionProcessEntrybyinspector,HhuInspectionProcessEntrybyservice,HhuInspectionProcessFollowup,HhuInsPrepaidManualEntry,


//ISOSystems/FollowupDailyProceduresSystem/FollowupProcedures
    IsoDocumentsProcesses, IsoDocumentsProcessesLog, IsoDocumentsProcessesLogReview,
//ISOSystems/QualityManagementSystem
    //AuditPlan
    IsoAuditPlan, IsoAuditPlanAuditSeqVw, IsoAuditPlanNotesReview, IsoAuditPlanSchedule, IsoAuditPlanScheduleEdit, IsoAuditYear, IsoCorrectiveActions, RepIsoAuditorSummaryRep,
    RepIsoAuditPlan, RepIsoAuditPlanComparison, RepIsoAuditPlanComparisonH, RepIsoAuditPlanNotes, RepIsoAuditPlanNotesMgr, RepIsoAuditPlanNotesRev,
    RepIsoAuditPlanSchedule, RepIsoAuditPlanSummary, RepIsoCorrectiveActions, RepIsoPlanKeysMatrix,
    //Reports
    RepIsoUpdatedDocuments, RepIsoNewDocuments, RepIsoDelDocuments, RepIsoRequests, RepIsoMainMenu, RepIsoTemplatesEmployees, RepIsoTemplatesEmployeesE, RepIsoEmployeesDiv,RepIsoDocumentRecords,
    //Settings
    IsoDocumentTypes, IsoOrgTreeDiv, IsoOrgTreeDep, IsoCorrectiveActionSources, IsoUnconformityReasons, IsoPlanKeys, IsoHolidays, IsoTemplates, IsoReportFormat, IsoEmployees, CompaniesSystemParamISOSystem,
    //Transactions
    IsoDocumentsTemp, IsoDocumentsH, IsoDocuments, IsoTemplatesEmployees, IsoDocumentsEmployees, IsoRequests,
};