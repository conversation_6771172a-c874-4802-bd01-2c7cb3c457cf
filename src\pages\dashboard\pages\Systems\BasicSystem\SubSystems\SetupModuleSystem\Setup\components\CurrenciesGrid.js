import React, { useEffect, useState } from 'react';
import axios from 'axios';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Checkbox,
  Chip,
  TablePagination,
  CircularProgress 
} from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon, Info as InfoIcon } from '@mui/icons-material';
import Colors from '../../../../../../../../../Common/Colors';
import { hostURL } from '../../../../../../../../../Common/APIs';
import  AlertSnackbar  from '../../../../../../../../../Common/AlertSnackbar';
import { useAuth } from '../../../../../../../../../AuthProvider';
import { useTranslation } from 'react-i18next';
import PrintIcon from '@mui/icons-material/Print';
import DownloadIcon from '@mui/icons-material/Download';
import AddIcon from '@mui/icons-material/Add';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import Cookies from 'js-cookie';
import { useTheme } from '@mui/material/styles';

export default function CurrenciesGrid() {
  const [currencies, setCurrencies] = useState([]);
  const [currencyRates, setCurrencyRates] = useState({});
  const [currencySymbolToCode, setCurrencySymbolToCode] = useState({});
  const [open, setOpen] = useState(false);
  const [editingCurrency, setEditingCurrency] = useState(null);
  const [newCurrency, setNewCurrency] = useState({
    currencyCode: '',
    symbol: '',
    nameAr: '',
    fractionAr: '',
    nameEn: '',
    fractionEn: '',
    multiplier: '',
    rateNoOfDays: '',
    isActive: '',
    isBaseCurrency: false,
  });

  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('info');
  const { user, setUser } = useAuth();
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;
  const { t, i18n } = useTranslation();
  const token = Cookies.get('authToken');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [openDetailsDialog, setOpenDetailsDialog] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 600);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currencyToDelete, setCurrencyToDelete] = useState(null);
  const [loading, setLoading] = useState({ print: false, excel: false, pdf: false });
  const theme = useTheme();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 600);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const fetchCurrencies = async () => {
      try {
        const response = await axios.get(`${hostURL}/getCurrencies`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const { resultCode, data } = response.data;

        if (resultCode === '0') {
          const formattedData = data.map((item) => ({
            id: item.currencyCode,
            symbol: item.currencySymbol,
            nameEn: item.nameE,
            fractionEn: item.fractionNameE,
            nameAr: item.nameA,
            fractionAr: item.fractionNameA,
            multiplier: item.currencyFactor,
            rateNoOfDays: item.rateNoOfDays,
            isActive: item.isActive,
            isBaseCurrency: item.isBaseCurrency,
          }));
          setCurrencies(formattedData);

          const mapping = {};
          formattedData.forEach((currency) => {
            mapping[currency.symbol] = currency.symbol;
          });
          setCurrencySymbolToCode(mapping);
        } else {
          console.error('Failed to fetch currencies:', response.data);
        }
      } catch (error) {
        console.error('Error fetching currencies:', error);
      }
    };

    fetchCurrencies();
  }, [user.compCode]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const paginatedCurrencies = currencies.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };
  const showSnackbar = (message, severity = 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };
  const handleDialogOpen = (currency = null) => {
    if (currency) {
      setEditingCurrency(currency);
      setNewCurrency({
        currencyCode: currency.id,
        symbol: currency.symbol,
        nameAr: currency.nameAr,
        fractionAr: currency.fractionAr,
        nameEn: currency.nameEn,
        fractionEn: currency.fractionEn,
        multiplier: currency.multiplier,
        rateNoOfDays: currency.rateNoOfDays,
        isActive: currency.isActive === 'Y',
        isBaseCurrency: currency.isBaseCurrency === 'Y',
      });
    } else {
      setEditingCurrency(null);
      setNewCurrency({
        currencyCode: '',
        symbol: '',
        nameAr: '',
        fractionAr: '',
        nameEn: '',
        fractionEn: '',
        multiplier: '',
        rateNoOfDays: '',
        isActive: true,
        isBaseCurrency: false,
      });
    }
    setOpen(true);
  };
  const handleDialogClose = () => {
    setOpen(false);
  };
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewCurrency((prev) => ({ ...prev, [name]: value }));
  };
  const validateForm = () => {
    const requiredFields = [
      'currencyCode',
      'symbol',
      'nameAr',
      'nameEn',
      'fractionAr',
      'fractionEn',
      'multiplier',
      'rateNoOfDays',
    ];

    for (const field of requiredFields) {
      if (!newCurrency[field]) {
        showSnackbar('الرجاء ملء جميع الحقول الإجبارية', 'error');
        return false;
      }
    }
    return true;
  };
  const handleAddOrEditCurrency = async () => {
    if (!validateForm()) return;

    try {
      const url = editingCurrency
        ? `${hostURL}/updateCurrency`
        : `${hostURL}/addCurrency`;

      const method = 'post';

      const response = await axios[method](
        url,
        {
          currencyCode: newCurrency.currencyCode,
          currencySymbol: newCurrency.symbol,
          nameA: newCurrency.nameAr,
          nameE: newCurrency.nameEn,
          fractionNameA: newCurrency.fractionAr,
          fractionNameE: newCurrency.fractionEn,
          currencyFactor: newCurrency.multiplier || 1,
          rateNoOfDays: newCurrency.rateNoOfDays,
          isBaseCurrency: newCurrency.isBaseCurrency ? 'Y' : 'N',
          isActive: newCurrency.isActive ? 'Y' : 'N',
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const { resultCode, resultMessageE, resultMessageA } = response.data;

      if (resultCode === '0') {
        const updatedCurrency = {
          id: newCurrency.currencyCode,
          symbol: newCurrency.symbol,
          nameAr: newCurrency.nameAr,
          fractionAr: newCurrency.fractionAr,
          nameEn: newCurrency.nameEn,
          fractionEn: newCurrency.fractionEn,
          multiplier: newCurrency.multiplier,
          rateNoOfDays: newCurrency.rateNoOfDays,
          isActive: newCurrency.isActive ? 'Y' : 'N',
          isBaseCurrency: newCurrency.isBaseCurrency ? 'Y' : 'N',
        };

        if (editingCurrency) {
          setCurrencies((prevCurrencies) =>
            prevCurrencies.map((currency) =>
              currency.id === updatedCurrency.id ? updatedCurrency : currency
            )
          );
        } else {
          setCurrencies((prevCurrencies) => [...prevCurrencies, updatedCurrency]);
        }

        showSnackbar(
          editingCurrency ? 'تم تعديل العملة بنجاح!' : 'تمت إضافة العملة بنجاح!',
          'success'
        );
        setOpen(false);
      } else {
        showSnackbar(resultMessageA, 'error');
        console.error(resultMessageE);
      }
    } catch (error) {
      console.error('Error adding or editing currency:', error);
      showSnackbar('تعذر الاتصال بالخادم. يرجى المحاولة لاحقًا.', 'error');
    }
  };
  const handleDeleteCurrency = async (currencyCode) => {
    setDeleteDialogOpen(true);
    setCurrencyToDelete(currencyCode);
  };
  const confirmDelete = async () => {
    try {
      const response = await axios.post(
        `${hostURL}/deleteCurrency`,
        { currencyCode: currencyToDelete },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const { resultCode, resultMessageE, resultMessageA } = response.data;
      if (resultCode === '0') {
        showSnackbar('تم حذف العملة بنجاح!', 'success');
        setCurrencies((prevCurrencies) =>
          prevCurrencies.filter((currency) => currency.id !== currencyToDelete)
        );
      } else {
        showSnackbar(resultMessageA, 'error');
      }
    } catch (error) {
      console.error('Error deleting currency:', error);
      showSnackbar('تعذر الاتصال بالخادم. يرجى المحاولة لاحقًا.', 'error');
    }
    setDeleteDialogOpen(false);
  };


  // const handleDownloadPdf = () => {
  //   const link = document.createElement('a');
  //   link.href = ${hostURL}/exportCurrenciesFile;
  //   link.setAttribute('download', ${compCode}currencies.pdf);
  //   fetch(link.href, {
  //     method: 'POST',
  //     headers: {
  //       'Authorization': Bearer ${token},
  //       'Content-Type': 'application/json',
  //     },
  //     body: JSON.stringify({
  //       fileType: 'pdf',
  //       lang: i18n.language,
  //     }),
  //   })
  //     .then((response) => {
  //       if (!response.ok) {
  //         throw new Error('فشل في تنزيل الملف');
  //       }
  //       return response.blob();
  //     })
  //     .then((blob) => {
  //       const url = window.URL.createObjectURL(blob);
  //       link.href = url;
  //       link.click();
  //       window.URL.revokeObjectURL(url);
  //     })
  //     .catch((error) => {
  //       console.error('حدث خطأ أثناء تنزيل الملف:', error);
  //       alert('حدث خطأ أثناء تنزيل الملف. يرجى المحاولة مرة أخرى.');
  //     });
  // };


  const handleDownloadPdf = () => {
    setLoading(prev => ({ ...prev, pdf: true }));

    fetch(`${hostURL}/exportCurrenciesFile`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileType: 'pdf',
        lang: i18n.language,
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("فشل في تنزيل الملف");
        }
  
        const contentDisposition = response.headers.get("Content-Disposition");
        let fileName = "currencies.pdf"; 
        // console.log("contentDisposition: "+ contentDisposition);
        if (contentDisposition) {
          const parts = contentDisposition.split(";");
          // console.log("parts: "+parts);
          const fileNamePart = parts.find(part => part.includes("filename="));
          // console.log("fileNamePart: "+ fileNamePart);
          if (fileNamePart) {
            const matches = fileNamePart.match(/filename="?([^"]+)"?/);
            // console.log("matches: "+ matches);
            if (matches && matches[1]) {
              fileName = matches[1].trim(); 
            }
            // console.log("fileName: "+fileName);

          }
        }
        setLoading(prev => ({ ...prev, pdf: false }));

        return response.blob().then((blob) => ({ blob, fileName }));
      })
      .then(({ blob, fileName }) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      })
      .catch((error) => {
        console.error("حدث خطأ أثناء تنزيل الملف:", error);
        alert("حدث خطأ أثناء تنزيل الملف. يرجى المحاولة مرة أخرى.");
      });
  };
  const handleDownloadExcel = () => {
    setLoading(prev => ({ ...prev, excel: true }));
    fetch(`${hostURL}/exportCurrenciesFile`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileType: 'excel', // تحديد نوع الملف
        lang: i18n.language,
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("فشل في تنزيل الملف");
        }
  
        const contentDisposition = response.headers.get("Content-Disposition");
        let fileName = "currencies.xlsx"; 
  
        if (contentDisposition) {
      
          const parts = contentDisposition.split(";");
          const fileNamePart = parts.find(part => part.includes("filename="));
  
          if (fileNamePart) {
            const matches = fileNamePart.match(/filename="?([^"]+)"?/);
            if (matches && matches[1]) {
              fileName = matches[1].trim(); 
            }
          }
        }
        setLoading(prev => ({ ...prev, excel: false }));
        return response.blob().then((blob) => {
          if (blob.size === 0) {
            throw new Error("الملف الذي تم تنزيله فارغ");
          }
          return { blob, fileName };
        });
      })
      .then(({ blob, fileName }) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      })
      .catch((error) => {
        console.error("حدث خطأ أثناء تنزيل الملف:", error);
        alert("حدث خطأ أثناء تنزيل الملف. يرجى المحاولة مرة أخرى.");
      });
  };
  const handlePrint = async () => {
    try {

      setLoading(prev => ({ ...prev, print: true }));
      const response = await fetch(
        `${hostURL}/exportCurrenciesFile`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fileType: 'pdf',
            lang: i18n.language,
          }),
        }
      );
      setLoading(prev => ({ ...prev, print: false }));

      if (!response.ok) {
        throw new Error('فشل في إنشاء ملف PDF');
      }

      const blob = await response.blob();
      const pdfUrl = URL.createObjectURL(blob);

      const pdfWindow = window.open(pdfUrl);

      pdfWindow.onload = () => {
        pdfWindow.print();
      };
    } catch (error) {
      console.error('حدث خطأ أثناء إنشاء ملف PDF:', error);
      alert('حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.');
    }
  };

  return (
    <Box sx={{ width: '90%', maxWidth: '1700px', mx: 'auto' }}>
      <Typography component="h2" variant="h6" sx={{ mb: 2 , fontFamily: theme.fontPrimary,}}>
        {t('LC_000027')}
      </Typography>

      <Box sx={{ display: 'flex', gap: 1, marginBottom: 2, flexDirection: 'row-reverse' }}>
        <IconButton
          title={t('LC_000067')}
          sx={{
            backgroundColor: currentColors.secondary,
            color: 'white',
            '&:hover': { color: currentColors.secondary },
          }}
          onClick={handlePrint}
        >
          {loading.print ? <CircularProgress size={24} sx={{ color: (theme) => theme.palette.mode === "dark" ? "white" : currentColors.primary,}} /> : <PrintIcon />}
        </IconButton>
        <IconButton
          title={t('LC_000066')}
          sx={{
            backgroundColor: currentColors.secondary,
            color: 'white',
            '&:hover': { color: currentColors.secondary },
          }}
          onClick={handleDownloadExcel}
        >
          {loading.excel ? <CircularProgress size={24} sx={{ color: (theme) => theme.palette.mode === "dark" ? "white" : currentColors.primary, }} /> : <DownloadIcon />}
        </IconButton>
        <IconButton
          title={t('LC_000065')}
          sx={{
            backgroundColor: currentColors.secondary,
            color: 'white',
            '&:hover': { color: currentColors.secondary },
          }}
          onClick={handleDownloadPdf}
        >
          {loading.pdf ? <CircularProgress size={24} sx={{ color: (theme) => theme.palette.mode === "dark" ? "white" : currentColors.primary, }} /> : <PictureAsPdfIcon />}
        </IconButton>
        <IconButton
          title={t('LC_000063')}
          sx={{
            backgroundColor: currentColors.secondary,
            color: 'white',
            '&:hover': { color: currentColors.secondary },
          }}
          onClick={() => handleDialogOpen()}
        >
          <AddIcon />
        </IconButton>
      </Box>

      <TableContainer component={Paper} elevation={4} sx={{ mb: 4 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: currentColors.secondary }}>
              <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background , fontFamily: theme.fontPrimary,}}>
                {t('LC_000028')}
              </TableCell>
              <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background , fontFamily: theme.fontPrimary,}}>
                {t('LC_000053')}
              </TableCell>
              {!isMobile && (
                <>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background , fontFamily: theme.fontPrimary,}}>
                    {t('LC_000045')}
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background, fontFamily: theme.fontPrimary, }}>
                    {t('LC_000047')}
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background,fontFamily: theme.fontPrimary,}}>
                    {t('LC_000044')}
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background , fontFamily: theme.fontPrimary, }}>
                    {t('LC_000046')}
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background , fontFamily: theme.fontPrimary, }}>
                    {t('LC_000054')}
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background, fontFamily: theme.fontPrimary, }}>
                    {t('LC_000055')}
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background , fontFamily: theme.fontPrimary,}}>
                    {t('LC_000060')}
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background , fontFamily: theme.fontPrimary,}}>
                    {t('LC_000061')}
                  </TableCell>
                </>
              )}
              <TableCell align="center" sx={{ fontWeight: 'bold', color: currentColors.background, fontFamily: theme.fontPrimary, }}>
                {t('LC_000048')}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
          {paginatedCurrencies.length > 0 ? (
            paginatedCurrencies.map((currency) => (
              <TableRow
                key={currency.id}
                sx={{
                  borderBottom: (theme) =>
                    `2px solid ${theme.palette.mode === 'dark' ? 'white' : 'rgba(0, 0, 0, 0.2)'}`,
                }}
              >
                <TableCell align="center" sx={{fontFamily: theme.fontPrimary,}}>{currency.id}</TableCell>
                <TableCell>{currency.symbol}</TableCell>
                {!isMobile && (
                  <>
                    <TableCell align="center" sx={{fontFamily: theme.fontPrimary,}}>{currency.nameEn}</TableCell>
                    <TableCell align="center" sx={{fontFamily: theme.fontPrimary,}}>{currency.fractionEn}</TableCell>
                    <TableCell align="center" sx={{fontFamily: theme.fontPrimary,}}>{currency.nameAr}</TableCell>
                    <TableCell align="center" sx={{fontFamily: theme.fontPrimary,}}>{currency.fractionAr}</TableCell>
                    <TableCell align="center" sx={{fontFamily: theme.fontPrimary,}}>{currency.multiplier}</TableCell>
                    <TableCell align="center">{currency.rateNoOfDays}</TableCell>
                    <TableCell align="center">
                      <Chip
                        label={currency.isActive === 'Y' ? t('LC_000056') : t('LC_000057')}
                        color={currency.isActive === 'Y' ? 'success' : 'error'}
                        variant="outlined"
                        sx={{
                          fontWeight: 'bold',
                          fontSize: '0.875rem',
                          fontFamily: theme.fontPrimary,
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={currency.isBaseCurrency === 'Y' ? t('LC_000058') : t('LC_000059')}
                        color={currency.isBaseCurrency === 'Y' ? 'success' : 'default'}
                        variant="outlined"
                        sx={{
                          fontWeight: 'bold',
                          fontSize: '0.875rem',
                          fontFamily: theme.fontPrimary,
                        }}
                      />
                    </TableCell>
                  </>
                )}
                <TableCell align="center">
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {isMobile && (
                      <IconButton
                        title={t('Show Details')}
                        onClick={() => {
                          setSelectedCurrency(currency);
                          setOpenDetailsDialog(true);
                        }}
                      >
                        <InfoIcon />
                      </IconButton>
                    )}
                    <IconButton
                      title={t('LC_000130')}
                      onClick={() => handleDialogOpen(currency)}
                      sx={{
                        color: (theme) =>
                          theme.palette.mode === 'dark' ? 'white' : currentColors.secondary,
                      }}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      title={t('LC_000131')}
                      onClick={() => handleDeleteCurrency(currency.id)}
                      sx={{
                        color: (theme) =>
                          theme.palette.mode === 'dark' ? 'white' : currentColors.primary,
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))
          ):(
            <TableRow>
              <TableCell colSpan={12} align="center">
                {t("LC_000147")}
              </TableCell>
            </TableRow>
          )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={currencies.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />

      {/* Dialog for currency details */}
      <Dialog open={openDetailsDialog} onClose={() => setOpenDetailsDialog(false)}>
        <DialogTitle sx={{fontFamily: theme.fontPrimary,}}>{t('Currency Details')}</DialogTitle>
        <DialogContent>
          {selectedCurrency && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 ,  }}>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000028')}:</strong> {selectedCurrency.id}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000053')}:</strong> {selectedCurrency.symbol}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000045')}:</strong> {selectedCurrency.nameEn}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000047')}:</strong> {selectedCurrency.fractionEn}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000044')}:</strong> {selectedCurrency.nameAr}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000046')}:</strong> {selectedCurrency.fractionAr}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000054')}:</strong> {selectedCurrency.multiplier}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000055')}:</strong> {selectedCurrency.rateNoOfDays}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000060')}:</strong> {selectedCurrency.isActive === 'Y' ? t('Active') : t('inActive')}
              </Typography>
              <Typography variant="body1" sx={{fontFamily: theme.fontPrimary,}}>
                <strong>{t('LC_000061')}:</strong> {selectedCurrency.isBaseCurrency === 'Y' ? t('Yes') : t('No')}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDetailsDialog(false)} color="primary">
            {t('Close')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog for adding/editing currency */}
      <Dialog open={open} onClose={handleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ textAlign: 'center', fontWeight: 'bold', color: currentColors.primary }}>
          {editingCurrency ? t('LC_000064') : t('LC_000063')}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 3, textAlign: 'center', color: currentColors.gray }}>
            {/* {t('Please fill out the fields below to add or edit a currency.')} */}
            {editingCurrency ? t('LC_000154') : t('LC_000155') }
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000028')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                name="currencyCode"
                value={newCurrency.currencyCode}
                onChange={handleInputChange}
                variant="outlined"
                required
                InputProps={{
                  readOnly: editingCurrency !== null,
                  style: {
                    ...(editingCurrency && {backgroundColor:  '#f0f0f0' , color: '#888'})
                  },
                  //      style: {
                  //   backgroundColor: editingCurrency ? '#f0f0f0' : 'white',
                  //   color: editingCurrency ? '#888' : 'black',
                  // }, 
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: editingCurrency ? '#ddd' : '#3f51b5',
                    },
                    '&:hover fieldset': {
                      borderColor: editingCurrency ? '#ddd' : '#3f51b5',
                    },
                  },
                }}
              />
            </Box>

            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000053')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                name="symbol"
                value={newCurrency.symbol}
                onChange={handleInputChange}
                variant="outlined"
                required
              />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000054')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                name="multiplier"
                select
                value={newCurrency.multiplier}
                onChange={handleInputChange}
                variant="outlined"
                required
                SelectProps={{
                  native: true,
                }}
              >
                <option value="" disabled>
                  {t('LC_000054')}
                </option>
                <option value={1}>1</option>
                <option value={10}>10</option>
                <option value={100}>100</option>
                <option value={1000}>1000</option>
                <option value={1000000}>1000000</option>
              </TextField>
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000055')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                name="rateNoOfDays"
                value={newCurrency.rateNoOfDays}
                onChange={handleInputChange}
                required
                variant="outlined"
              />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000044')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                name="nameAr"
                value={newCurrency.nameAr}
                onChange={handleInputChange}
                variant="outlined"
                required
              />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000046')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                required
                name="fractionAr"
                value={newCurrency.fractionAr}
                onChange={handleInputChange}
                variant="outlined"
              />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000045')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                name="nameEn"
                value={newCurrency.nameEn}
                onChange={handleInputChange}
                variant="outlined"
                required
              />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000047')} <span style={{ color: 'red' }}>*</span>
              </Typography>
              <TextField
                fullWidth
                name="fractionEn"
                required
                value={newCurrency.fractionEn}
                onChange={handleInputChange}
                variant="outlined"
              />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000060')}
              </Typography>
              <Checkbox
                name="isActive"
                checked={newCurrency.isActive || false}
                onChange={(e) => setNewCurrency({ ...newCurrency, isActive: e.target.checked })}
                color="primary"
              />
            </Box>
            <Box>
              <Typography variant="caption" sx={{ color: currentColors.gray }}>
                {t('LC_000061')}
              </Typography>
              <Checkbox
                name="isBaseCurrency"
                checked={newCurrency.isBaseCurrency || false}
                onChange={(e) =>
                  setNewCurrency({ ...newCurrency, isBaseCurrency: e.target.checked })
                }
                color="primary"
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', gap: 2 }}>
          <Button onClick={handleDialogClose} variant="outlined" color="error">
            {t('Cancel')}
          </Button>
          <Button
            onClick={handleAddOrEditCurrency}
            sx={{
              backgroundColor: currentColors.secondary,
              color: 'white',
              '&:hover': {
                backgroundColor: currentColors.primary,
              },
            }}
          >
            {editingCurrency ? t('LC_000130') : t('LC_000132')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog for delete confirmation */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>{t('Confirm Delete')}</DialogTitle>
        <DialogContent>
          <Typography>{t('Are you sure you want to delete this currency?')}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} color="primary">
            {t('LC_000087')}
          </Button>
          <Button onClick={confirmDelete} color="error">
            {t('LC_000131')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      {/* <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity}>
          {snackbarMessage}
        </Alert>
      </Snackbar> */}
      <AlertSnackbar
        open={snackbarOpen}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
        severity={snackbarSeverity}
      />
    </Box>
  );
}

// import React, { useEffect, useState } from 'react';
// import axios from 'axios';
// import {
//   Box,
//   Typography,
//   Table,
//   TableBody,
//   TableCell,
//   TableContainer,
//   TableHead,
//   TableRow,
//   Paper,
//   IconButton,
//   Button,
//   Dialog,
//   DialogTitle,
//   DialogContent,
//   DialogActions,
//   TextField,
//   Checkbox,
//   Chip,
//   Snackbar,
//   Alert,
//   TablePagination
// } from '@mui/material';
// import { Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
// import Colors from '../../../../../../../../../Common/Colors';
// import { hostURL } from '../../../../../../../../../Common/APIs';
// import { useAuth } from '../../../../../../../../../AuthProvider';
// import { useTranslation } from 'react-i18next';
// import PrintIcon from '@mui/icons-material/Print';
// import DownloadIcon from '@mui/icons-material/Download';
// import AddIcon from '@mui/icons-material/Add';
// import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
// import StarBorderIcon from '@mui/icons-material/StarBorder';
// import StarIcon from '@mui/icons-material/Star';
// import Cookies from 'js-cookie';

// export default function CurrenciesGrid() {
//   const [currencies, setCurrencies] = useState([]);
//   const [currencyRates, setCurrencyRates] = useState({});
//   const [currencySymbolToCode, setCurrencySymbolToCode] = useState({});
//   const [open, setOpen] = useState(false);
//   const [editingCurrency, setEditingCurrency] = useState(null);
//   const [newCurrency, setNewCurrency] = useState({
//     currencyCode: '',
//     symbol: '',
//     nameAr: '',
//     fractionAr: '',
//     nameEn: '',
//     fractionEn: '',
//     multiplier: '',
//     rateNoOfDays: '',
//     isActive: '',
//     isBaseCurrency: false,
//   });

//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('info');

//   const { user, setUser } = useAuth();
//   // const currentColors = Colors[user.compCode] || Colors.default;
//   const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
//   const currentColors = Colors[compCode] || Colors.default;
  
//   const { t, i18n } = useTranslation();

//   // const isAuthenticatedToken = user.token;
//   const token = Cookies.get('authToken');

//   const [page, setPage] = useState(0);
//   const [rowsPerPage, setRowsPerPage] = useState(5);

//   useEffect(() => {
//     const fetchCurrencies = async () => {
//       try {
//         const response = await axios.get(`${hostURL}/getCurrencies`, {
//           headers: {
//             Authorization: `Bearer ${token}`,
//           },
//         });

//         const { resultCode, data } = response.data;

//         if (resultCode === '0') {
//           const formattedData = data.map((item) => ({
//             id: item.currencyCode,
//             symbol: item.currencySymbol,
//             nameEn: item.nameE,
//             fractionEn: item.fractionNameE,
//             nameAr: item.nameA,
//             fractionAr: item.fractionNameA,
//             multiplier: item.currencyFactor,
//             rateNoOfDays: item.rateNoOfDays,
//             isActive: item.isActive,
//             isBaseCurrency: item.isBaseCurrency,
//           }));
//           setCurrencies(formattedData);

//           const mapping = {};
//           formattedData.forEach((currency) => {
//             mapping[currency.symbol] = currency.symbol;
//           });
//           setCurrencySymbolToCode(mapping);
//         } else {
//           console.error('Failed to fetch currencies:', response.data);
//         }
//       } catch (error) {
//         console.error('Error fetching currencies:', error);
//       }
//     };

//     fetchCurrencies();
//   }, [user.compCode]);

  

//   const filteredCurrencyRates = Object.entries(currencyRates)
//     .filter(([currencyCode]) =>
//       currencies.some((currency) => currencySymbolToCode[currency.symbol] === currencyCode)
//     )
//     .map(([currencyCode, rate]) => {
//       const currency = currencies.find(
//         (currency) => currencySymbolToCode[currency.symbol] === currencyCode
//       );
//       return {
//         symbol: currency.symbol,
//         rate,
//       };
//     });
//     const handleChangePage = (event, newPage) => {
//       setPage(newPage);
//     };
//     const handleChangeRowsPerPage = (event) => {
//       setRowsPerPage(parseInt(event.target.value, 10));
//       setPage(0);
//     };
//     const paginatedCurrencies = currencies.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const showSnackbar = (message, severity = 'info') => {
//     setSnackbarMessage(message);
//     setSnackbarSeverity(severity);
//     setSnackbarOpen(true);
//   };

//   const handleDialogOpen = (currency = null) => {
//     if (currency) {
//       setEditingCurrency(currency);
//       setNewCurrency({
//         currencyCode: currency.id,
//         symbol: currency.symbol,
//         nameAr: currency.nameAr,
//         fractionAr: currency.fractionAr,
//         nameEn: currency.nameEn,
//         fractionEn: currency.fractionEn,
//         multiplier: currency.multiplier,
//         rateNoOfDays: currency.rateNoOfDays,
//         isActive: currency.isActive === 'Y',
//         isBaseCurrency: currency.isBaseCurrency === 'Y',
//       });
//     } else {
//       setEditingCurrency(null);
//       setNewCurrency({
//         currencyCode: '',
//         symbol: '',
//         nameAr: '',
//         fractionAr: '',
//         nameEn: '',
//         fractionEn: '',
//         multiplier: '',
//         rateNoOfDays: '',
//         isActive: true,
//         isBaseCurrency: false,
//       });
//     }
//     setOpen(true);
//   };

//   const handleDialogClose = () => {
//     setOpen(false);
//   };

//   const handleInputChange = (e) => {
//     const { name, value } = e.target;
//     setNewCurrency((prev) => ({ ...prev, [name]: value }));
//   };

//   const validateForm = () => {
//     const requiredFields = [
//       'currencyCode',
//       'symbol',
//       'nameAr',
//       'nameEn',
//       'fractionAr',
//       'fractionEn',
//       'multiplier',
//       'rateNoOfDays',
//     ];

//     for (const field of requiredFields) {
//       if (!newCurrency[field]) {
//         showSnackbar('الرجاء ملء جميع الحقول الإجبارية', 'error');
//         return false;
//       }
//     }
//     return true;
//   };

//   const handleAddOrEditCurrency = async () => {
//     if (!validateForm()) return;

//     try {
//       const url = editingCurrency
//         ? `${hostURL}/updateCurrency`
//         : `${hostURL}/addCurrency`;

//       const method = 'post';

//       const response = await axios[method](
//         url,
//         {
//           currencyCode: newCurrency.currencyCode,
//           currencySymbol: newCurrency.symbol,
//           nameA: newCurrency.nameAr,
//           nameE: newCurrency.nameEn,
//           fractionNameA: newCurrency.fractionAr,
//           fractionNameE: newCurrency.fractionEn,
//           currencyFactor: newCurrency.multiplier || 1,
//           rateNoOfDays: newCurrency.rateNoOfDays,
//           isBaseCurrency: newCurrency.isBaseCurrency ? 'Y' : 'N',
//           isActive: newCurrency.isActive ? 'Y' : 'N',
//         },
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//           },
//         }
//       );

//       const { resultCode, resultMessageE, resultMessageA } = response.data;

//       if (resultCode === '0') {
//         const updatedCurrency = {
//           id: newCurrency.currencyCode,
//           symbol: newCurrency.symbol,
//           nameAr: newCurrency.nameAr,
//           fractionAr: newCurrency.fractionAr,
//           nameEn: newCurrency.nameEn,
//           fractionEn: newCurrency.fractionEn,
//           multiplier: newCurrency.multiplier,
//           rateNoOfDays: newCurrency.rateNoOfDays,
//           isActive: newCurrency.isActive ? 'Y' : 'N',
//           isBaseCurrency: newCurrency.isBaseCurrency ? 'Y' : 'N',
//         };

//         if (editingCurrency) {
//           setCurrencies((prevCurrencies) =>
//             prevCurrencies.map((currency) =>
//               currency.id === updatedCurrency.id ? updatedCurrency : currency
//             )
//           );
//         } else {
//           setCurrencies((prevCurrencies) => [...prevCurrencies, updatedCurrency]);
//         }

//         showSnackbar(
//           editingCurrency ? 'تم تعديل العملة بنجاح!' : 'تمت إضافة العملة بنجاح!',
//           'success'
//         );
//         setOpen(false);
//       } else {
//         showSnackbar(resultMessageA, 'error');
//         console.error(resultMessageE);
//       }
//     } catch (error) {
//       console.error('Error adding or editing currency:', error);
//       showSnackbar('تعذر الاتصال بالخادم. يرجى المحاولة لاحقًا.', 'error');
//     }
//   };

//   const handleDeleteCurrency = async (currencyCode) => {
//     setDeleteDialogOpen(true);
//     setCurrencyToDelete(currencyCode);
//   };

//   const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
//   const [currencyToDelete, setCurrencyToDelete] = useState(null);

//   const confirmDelete = async () => {
//     try {
//       const response = await axios.post(
//         `${hostURL}/deleteCurrency`,
//         { currencyCode: currencyToDelete },
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//           },
//         }
//       );
//       const { resultCode, resultMessageE, resultMessageA } = response.data;
//       if (resultCode === '0') {
//         showSnackbar('تم حذف العملة بنجاح!', 'success');
//         setCurrencies((prevCurrencies) =>
//           prevCurrencies.filter((currency) => currency.id !== currencyToDelete)
//         );
//       } else {
//         showSnackbar(resultMessageA, 'error');
//       }
//     } catch (error) {
//       console.error('Error deleting currency:', error);
//       showSnackbar('تعذر الاتصال بالخادم. يرجى المحاولة لاحقًا.', 'error');
//     }
//     setDeleteDialogOpen(false);
//   };


//   const handleDownloadPdf = () => {
//     const link = document.createElement('a');
//     link.href = `${hostURL}/exportCurrenciesFile`;
//     link.setAttribute('download', `${user.compCode}currencies.pdf`);
//     fetch(link.href, {
//       method: 'POST',
//       headers: {
//         'Authorization': `Bearer ${token}`,
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         fileType: 'pdf',
//         lang: i18n.language,
//       }),
//     })
//       .then((response) => {
//         if (!response.ok) {
//           throw new Error('فشل في تنزيل الملف');
//         }
//         return response.blob();
//       })
//       .then((blob) => {
//         const url = window.URL.createObjectURL(blob);
//         link.href = url;
//         link.click();
//         window.URL.revokeObjectURL(url);
//       })
//       .catch((error) => {
//         console.error('حدث خطأ أثناء تنزيل الملف:', error);
//         alert('حدث خطأ أثناء تنزيل الملف. يرجى المحاولة مرة أخرى.');
//       });
//   };

//   const handleDownloadExcel = () => {
//     const link = document.createElement('a');
//     link.href = `${hostURL}/exportCurrenciesFile`;
//     link.setAttribute('download', `${user.compCode}currencies.xlsx`);
//     fetch(link.href, {
//       method: 'POST',
//       headers: {
//         'Authorization': `Bearer ${token}`,
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         fileType: 'excel',
//         lang: i18n.language,
//       }),
//     })
//       .then((response) => {
//         if (!response.ok) {
//           throw new Error('فشل في تنزيل الملف');
//         }
//         return response.blob();
//       })
//       .then((blob) => {
//         const url = window.URL.createObjectURL(blob);
//         link.href = url;
//         link.click();
//         window.URL.revokeObjectURL(url);
//       })
//       .catch((error) => {
//         console.error('حدث خطأ أثناء تنزيل الملف:', error);
//         alert('حدث خطأ أثناء تنزيل الملف. يرجى المحاولة مرة أخرى.');
//       });
//   };


//   const handlePrint = async () => {
//     try {
//       const response = await fetch(
//         `${hostURL}/exportCurrenciesFile`,
//         {
//           method: 'POST',
//           headers: {
//             'Authorization': `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//           body: JSON.stringify({
//             fileType: 'pdf',
//             lang: i18n.language,
//           }),
//         }
//       );

//       if (!response.ok) {
//         throw new Error('فشل في إنشاء ملف PDF');
//       }

//       const blob = await response.blob();
//       const pdfUrl = URL.createObjectURL(blob);

//       const pdfWindow = window.open(pdfUrl);

//       pdfWindow.onload = () => {
//         pdfWindow.print();
//       };
//     } catch (error) {
//       console.error('حدث خطأ أثناء إنشاء ملف PDF:', error);
//       alert('حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.');
//     }
//   };

//   // const handlePrint = () => {
//   //   window.print();
//   // };

//   return (
//     <Box sx={{ width: '90%', maxWidth: '1700px', mx: 'auto' }}>
//       <Typography component="h2" variant="h6" sx={{ mb: 2 }}>
//         {t('LC_000027')}
//         {/* <IconButton
//           onClick={(e) => {
//             e.stopPropagation();

//           }}
//           sx={{ color: 'gold', ml:2 }}
//         >
//           <StarIcon />
//         </IconButton> */}
//       </Typography>


//       <Box sx={{ display: 'flex', gap: 1, marginBottom: 2, flexDirection: 'row-reverse' }}>
//         <IconButton
//           title={t('LC_000067')}
//           sx={{
//             backgroundColor: currentColors.secondary,
//             color: 'white',
//             '&:hover': { color: currentColors.secondary },
//           }}
//           onClick={handlePrint}
//         >
//           <PrintIcon />
//         </IconButton>
//         <IconButton
//           title={t('LC_000066')}
//           sx={{
//             backgroundColor: currentColors.secondary,
//             color: 'white',
//             '&:hover': { color: currentColors.secondary },
//           }}
//           onClick={handleDownloadExcel}
//         >
//           <DownloadIcon />
//         </IconButton>

//         <IconButton
//           title={t('LC_000065')}
//           sx={{
//             backgroundColor: currentColors.secondary,
//             color: 'white',
//             '&:hover': { color: currentColors.secondary },
//           }}
//           onClick={handleDownloadPdf}
//         >
//           <PictureAsPdfIcon />
//         </IconButton>

//         <IconButton
//           title={t('LC_000063')}
//           sx={{
//             backgroundColor: currentColors.secondary,
//             color: 'white',
//             '&:hover': { color: currentColors.secondary },
//           }}
//           onClick={() => handleDialogOpen()}
//         >
//           <AddIcon />
//         </IconButton>
//       </Box>
//       <TableContainer component={Paper} elevation={4} sx={{ mb: 4 }}>
//         <Table>
//         <TableHead>
//             <TableRow sx={{ backgroundColor: currentColors.secondary }}>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000028')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000053')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000045')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000047')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000044')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000046')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000054')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000055')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000060')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000061')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('LC_000048')}
//               </TableCell>
//             </TableRow>
//           </TableHead>
//           <TableBody>
//             {paginatedCurrencies.map((currency) => (
//               <TableRow
//                 key={currency.id}
//                 sx={{
//                   '&:hover': { backgroundColor: (theme) => theme.palette.mode === 'dark' ? currentColors.secondary : currentColors.grayLight },
//                   borderBottom: '2px solid',
//                   borderColor: currentColors.borderColor || '#ccc',
//                 }}
//               >
//                 <TableCell>{currency.id}</TableCell>
//                 <TableCell>{currency.symbol}</TableCell>
//                 <TableCell>{currency.nameEn}</TableCell>
//                 <TableCell>{currency.fractionEn}</TableCell>
//                 <TableCell>{currency.nameAr}</TableCell>
//                 <TableCell>{currency.fractionAr}</TableCell>
//                 <TableCell>{currency.multiplier}</TableCell>
//                 <TableCell>{currency.rateNoOfDays}</TableCell>
//                 <TableCell>
//                   <Chip
//                     label={currency.isActive === 'Y' ? t('Active') : t('inActive')}
//                     color={currency.isActive === 'Y' ? 'success' : 'error'}
//                     variant="outlined"
//                     sx={{
//                       fontWeight: 'bold',
//                       fontSize: '0.875rem',
//                     }}
//                   />
//                 </TableCell>
//                 <TableCell>
//                   <Chip
//                     label={currency.isBaseCurrency === 'Y' ? t('Yes') : t('No')}
//                     color={currency.isBaseCurrency === 'Y' ? 'success' : 'default'}
//                     variant="outlined"
//                     sx={{
//                       fontWeight: 'bold',
//                       fontSize: '0.875rem',
//                     }}
//                   />
//                 </TableCell>

//                 <TableCell>
//                   <Box sx={{ display: 'flex', gap: 1 }}>
//                     <IconButton
//                       title={t('Edit')}
//                       onClick={() => handleDialogOpen(currency)}
//                       sx={{
//                         color: (theme) =>
//                           theme.palette.mode === 'dark' ? 'white' : currentColors.secondary,
//                       }}
//                     >
//                       <EditIcon />
//                     </IconButton>
//                     <IconButton
//                       title={t('Delete')}
//                       onClick={() => handleDeleteCurrency(currency.id)}
//                       sx={{
//                         color: (theme) =>
//                           theme.palette.mode === 'dark' ? 'white' : currentColors.primary,
//                       }}
//                     >
//                       <DeleteIcon />
//                     </IconButton>
//                   </Box>
//                 </TableCell>
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </TableContainer>

//       <TablePagination
//         rowsPerPageOptions={[5, 10, 25]}
//         component="div"
//         count={currencies.length}
//         rowsPerPage={rowsPerPage}
//         page={page}
//         onPageChange={handleChangePage}
//         onRowsPerPageChange={handleChangeRowsPerPage}
//       />

//       {/* <Typography component="h2" variant="h6" sx={{ mb: 2 }}>
//         {t('LC_000062')}
//       </Typography>
//       <TableContainer component={Paper} elevation={4}>
//         <Table>
//           <TableHead>
//             <TableRow sx={{ backgroundColor: currentColors.secondary }}>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('Currency Symbol')}
//               </TableCell>
//               <TableCell sx={{ fontWeight: 'bold', color: currentColors.background }}>
//                 {t('Rate (USD)')}
//               </TableCell>
//             </TableRow>
//           </TableHead>
//           <TableBody>
//             {filteredCurrencyRates.map(({ symbol, rate }) => (
//               <TableRow
//                 key={symbol}
//                 sx={{
//                   '&:hover': { backgroundColor: currentColors.grayLight },
//                   borderBottom: '2px solid',
//                   borderColor: currentColors.borderColor || '#ccc',
//                 }}
//               >
//                 <TableCell>{symbol}</TableCell>
//                 <TableCell>{rate}</TableCell>
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </TableContainer> */}

//       <Dialog open={open} onClose={handleDialogClose} maxWidth="sm" fullWidth>
//         <DialogTitle
//           sx={{ textAlign: 'center', fontWeight: 'bold', color: currentColors.primary }}
//         >
//           {editingCurrency ? t('LC_000064') : t('LC_000063')}
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" sx={{ mb: 3, textAlign: 'center', color: currentColors.gray }}>
//             {t('Please fill out the fields below to add or edit a currency.')}
//           </Typography>
//           <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000028')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 name="currencyCode"
//                 value={newCurrency.currencyCode}
//                 onChange={handleInputChange}
//                 variant="outlined"
//                 required
//                 InputProps={{
//                   readOnly: editingCurrency !== null,
//                   style: {
//                     backgroundColor: editingCurrency ? '#f0f0f0' : 'white',
//                     color: editingCurrency ? '#888' : 'black',
//                   },
//                 }}
//                 sx={{
//                   '& .MuiOutlinedInput-root': {
//                     '& fieldset': {
//                       borderColor: editingCurrency ? '#ddd' : '#3f51b5',
//                     },
//                     '&:hover fieldset': {
//                       borderColor: editingCurrency ? '#ddd' : '#3f51b5',
//                     },
//                   },
//                 }}
//               />
//             </Box>

//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000053')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 name="symbol"
//                 value={newCurrency.symbol}
//                 onChange={handleInputChange}
//                 variant="outlined"
//                 required
//               />
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000054')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 name="multiplier"
//                 select
//                 value={newCurrency.multiplier}
//                 onChange={handleInputChange}
//                 variant="outlined"
//                 required
//                 SelectProps={{
//                   native: true,
//                 }}
//               >
//                 <option value="" disabled>
//                   {t('LC_000054')}
//                 </option>
//                 <option value={1}>1</option>
//                 <option value={10}>10</option>
//                 <option value={100}>100</option>
//                 <option value={1000}>1000</option>
//                 <option value={1000000}>1000000</option>
//               </TextField>
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000055')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 name="rateNoOfDays"
//                 value={newCurrency.rateNoOfDays}
//                 onChange={handleInputChange}
//                 required
//                 variant="outlined"
//               />
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000044')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 name="nameAr"
//                 value={newCurrency.nameAr}
//                 onChange={handleInputChange}
//                 variant="outlined"
//                 required
//               />
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000046')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 required
//                 name="fractionAr"
//                 value={newCurrency.fractionAr}
//                 onChange={handleInputChange}
//                 variant="outlined"
//               />
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000045')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 name="nameEn"
//                 value={newCurrency.nameEn}
//                 onChange={handleInputChange}
//                 variant="outlined"
//                 required
//               />
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000047')} <span style={{ color: 'red' }}>*</span>
//               </Typography>
//               <TextField
//                 fullWidth
//                 name="fractionEn"
//                 required
//                 value={newCurrency.fractionEn}
//                 onChange={handleInputChange}
//                 variant="outlined"
//               />
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000060')}
//               </Typography>
//               <Checkbox
//                 name="isActive"
//                 checked={newCurrency.isActive || false}
//                 onChange={(e) => setNewCurrency({ ...newCurrency, isActive: e.target.checked })}
//                 color="primary"
//               />
//             </Box>
//             <Box>
//               <Typography variant="caption" sx={{ color: currentColors.gray }}>
//                 {t('LC_000061')}
//               </Typography>
//               <Checkbox
//                 name="isBaseCurrency"
//                 checked={newCurrency.isBaseCurrency || false}
//                 onChange={(e) =>
//                   setNewCurrency({ ...newCurrency, isBaseCurrency: e.target.checked })
//                 }
//                 color="primary"
//               />
//             </Box>
//           </Box>
//         </DialogContent>
//         <DialogActions sx={{ justifyContent: 'center', gap: 2 }}>
//           <Button onClick={handleDialogClose} variant="outlined" color="error">
//             {t('Cancel')}
//           </Button>
//           <Button
//             onClick={handleAddOrEditCurrency}
//             sx={{
//               backgroundColor: currentColors.secondary,
//               color: 'white',
//               '&:hover': {
//                 backgroundColor: currentColors.primary,
//               },
//             }}
//           >
//             {editingCurrency ? t('Edit') : t('Add')}
//           </Button>
//         </DialogActions>
//       </Dialog>

//       <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
//         <DialogTitle>{t('Confirm Delete')}</DialogTitle>
//         <DialogContent>
//           <Typography>{t('Are you sure you want to delete this currency?')}</Typography>
//         </DialogContent>
//         <DialogActions>
//           <Button onClick={() => setDeleteDialogOpen(false)} color="primary">
//             {t('Cancel')}
//           </Button>
//           <Button onClick={confirmDelete} color="error">
//             {t('Delete')}
//           </Button>
//         </DialogActions>
//       </Dialog>

//       <Snackbar
//         open={snackbarOpen}
//         autoHideDuration={3000}
//         onClose={handleSnackbarClose}
//         anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
//       >
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </Box>
//   );
// }
