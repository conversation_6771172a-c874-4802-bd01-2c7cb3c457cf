import React, { useState, useEffect, useCallback } from 'react';
import {
    Avatar,
    Box,
    Button,
    Divider,
    Grid,
    Paper,
    TextField,
    Typography,
    IconButton,
    CssBaseline,
    Stack,
    InputAdornment,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions, Snackbar, Alert, MenuItem, Select, FormControl, CircularProgress
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import EditIcon from '@mui/icons-material/Edit';
import { useFormik } from 'formik';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Colors from '../../Common/Colors';
import Cookies from 'js-cookie';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import {
    chartsCustomizations,
    dataGridCustomizations,
    datePickersCustomizations,
    treeViewCustomizations,
} from '../../pages/dashboard/theme/customizations';
import AppTheme from '../../shared-theme/AppTheme';
import Header from '../dashboard/components/Header';
import { useTheme } from '@mui/material/styles';
import axios from 'axios';
import { hostURL } from '../../Common/APIs';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AlertSnackbar from '../../Common/AlertSnackbar';

const xThemeComponents = {
    ...chartsCustomizations,
    ...dataGridCustomizations,
    ...datePickersCustomizations,
    ...treeViewCustomizations,
};

export default function Profile(props) {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const isLoggedIn = !!Cookies.get("authToken");
    const token = Cookies.get("authToken");
    const theme = useTheme();

    const [openDialog, setOpenDialog] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');
    const [profilePic, setProfilePic] = useState("");

    const [countryCo, setCountryCo] = useState("");


    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState("");
    const [snackbarSeverity, setSnackbarSeverity] = useState("success");

    const [mode, setMode] = useState("");

    const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
    const currentColors = Colors[compCode] || Colors.default;
    const userNameEn = Cookies.get('userNameEn') ? JSON.parse(Cookies.get('userNameEn')) : null;

    const [phonePrefix, setPhonePrefix] = useState("");
    const [phonePrefixes, setPhonePrefixes] = useState([]);


    const [editLoading, setEditLoading] = useState(false);
    const [phoneLoading, setPhoneLoading] = useState(false);


    const [fieldErrors, setFieldErrors] = useState({ email: '', phoneNo: '' });

    // console.log(phonePrefix);
    // console.log(phonePrefixes);

    useEffect(() => {
        const fetchPhonePrefixes = async () => {
            try {
                const response = await axios.get(`${hostURL}/getCountryPhonePrefixes`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });
                //     phonePrefix: `+${item.phonePrefix}`,

                if (response.data.resultCode === "0") {
                    const transformedData = response.data.data.map((item) => ({
                        countryCode: item.countryCode,
                        phonePrefix: item.phonePrefix,
                        shortName: item.shortName,
                        icon: item.icon
                    }));

                    setPhonePrefixes(transformedData);
                    setPhonePrefix(transformedData[0]?.countryCode || "");
                }
            } catch (error) {
                console.error("Error fetching phone prefixes:", error);
            }
        };

        fetchPhonePrefixes();
    }, []);
    const formik = useFormik({
        enableReinitialize: true,
        initialValues: {
            empID: "",
            userCode: "",
            nameAr: "",
            nameEn: "",
            email: "",
            phoneNo: "",
            telNo: "",
            countryCo: "",
        },
        onSubmit: (values) => {
            console.log("Form Data:", values);
        },
    });

    useEffect(() => {
        if (phonePrefixes.length > 0 && countryCo) {
            const found = phonePrefixes.find(item => item.countryCode === countryCo);
            if (found) {
                setPhonePrefix(found.countryCode);
            }
        }
    }, [countryCo, phonePrefixes]);

    //const selectedPhonePrefix = phonePrefixes.find(item => item.countryCode === phonePrefix)?.phonePrefix;

    const fetchUserProfile = async () => {
        try {
            const response = await axios.post(
                `${hostURL}/getUserProfileInfo`,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            if (response.data.resultCode === "0") {
                const base64String = response.data.data.profilePic;
                const base64Prefix = "data:image/jpeg;base64,";

                formik.setValues({
                    empID: response.data.data.userEmployeeId || "",
                    userCode: response.data.data.userCode || "",
                    nameAr: response.data.data.userNameA || "",
                    nameEn: response.data.data.userNameE || "",
                    email: response.data.data.userEmail || "",
                    phoneNo: response.data.data.mobileNo || "",
                    telNo: response.data.data.intExtNo || "",
                    countryCo: response.data.data.countryCode || "",
                });
                setCountryCo(response.data.data.countryCode);
                //console.log("Sdasd: :" +response.data.data.countryCode);
                setProfilePic(`${base64Prefix}${base64String}`);
                localStorage.setItem('profilePic', `${base64Prefix}${base64String}`);
                window.dispatchEvent(new Event('profilePicChanged'));

            } else {
                console.error(" خطأ في جلب البيانات:", response.data.resultMessageE);
            }
        } catch (error) {
            console.error(" فشل جلب بيانات المستخدم:", error.response?.data || error.message);
        }
    };
    useEffect(() => {
        // console.log("Sdasd1591:" +countryCo);
        if (isLoggedIn) fetchUserProfile();
    }, [isLoggedIn]);

    const handleImageUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = async () => {
            const base64Image = reader.result.split(",")[1];

            // console.log(" البيانات المرسلة إلى الـ API:", { profilePic: base64Image });

            try {
                const response = await axios.post(
                    `${hostURL}/updateUserProfilePicture`,
                    { profilePic: base64Image },
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            "Content-Type": "application/json",
                        },
                    }
                );
                // console.log("استجابة السيرفر:", response.data);
                if (response.data.resultCode === "0") {
                    fetchUserProfile();
                }
            } catch (error) {
                console.error(" فشل رفع الصورة:", error.response?.data || error.message);
            }
        };
        reader.onerror = (error) => console.error(" خطأ في قراءة الملف:", error);
    };
    const handleUpdateProfile = async () => {
        try {
            const requestBody = {
                nameA: formik.values.nameAr,
                nameE: formik.values.nameEn,
                intExtNo: formik.values.telNo,
                // profilePic,
                lang: i18n.language,
            };
            const response = await axios.post(
                `${hostURL}/updateUserProfile`,
                {
                    userNameA: formik.values.nameAr,
                    userNameE: formik.values.nameEn,
                    intExtNo: formik.values.telNo,
                    // profilePic,
                    lang: i18n.language
                },
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        "Content-Type": "application/json"
                    }
                }
            );
            console.log('Request Body:', requestBody);
            console.log("تم تحديث الملف الشخصي:", response.data);

            setSnackbarMessage("تم تحديث الملف الشخصي بنجاح!");
            setSnackbarSeverity("success");
            setOpenSnackbar(true);
        } catch (error) {
            console.error("فشل التحديث:", error.response?.data || error.message);
            setSnackbarMessage(error.response?.data?.message || "حدث خطأ أثناء التحديث!");
            setSnackbarSeverity("error");
            setOpenSnackbar(true);
        }
    };
    // const validateEmail = async (email) => {
    //     if (!email) return false;
    //     try {
    //         const response = await axios.post(`${hostURL}/validateUserEmail`, { value: email, lang: i18n.language }, { headers: { Authorization: `Bearer ${token}` } });

    //         if (response.data.resultCode === "0") {
    //             Cookies.set('emailChangeToken', response.data.data.emailChangeToken, { expires: 1, secure: false, sameSite: 'Strict' });
    //             return true;
    //         }
    //     } catch (error) {
    //         console.error("Validation Error:", error);
    //     }
    //     return false;
    // };


    const validateEmail = async (email) => {
        if (!email) {
            setFieldErrors(prev => ({ ...prev, email: i18n.language === 'ar' ? t("الرجاء إدخال البريد الإلكتروني") : t("Please enter your email") }));
            return false;
        }
        try {
            const response = await axios.post(
                `${hostURL}/validateUserEmail`,
                { value: email, lang: i18n.language },
                { headers: { Authorization: `Bearer ${token}` } }
            );

            if (response.data.resultCode === "0") {
                Cookies.set('emailChangeToken', response.data.data.emailChangeToken, { expires: 1 });
                setFieldErrors(prev => ({ ...prev, email: '' }));
                return true;
            } else {
                const errorMsg = i18n.language === 'ar' ? response.data.resultMessageA : response.data.resultMessageE;
                setFieldErrors(prev => ({ ...prev, email: errorMsg || t("فشل التحقق من البريد") }));
            }
        } catch (error) {
            setFieldErrors(prev => ({ ...prev, email: t("LC_000153") }));
        }
        return false;
    };

    const handleVerifyCode = async () => {
        try {
            let response;
            if (mode === "email") {
                response = await doChangeEmail(verificationCode);
            } else if (mode === "phoneNo") {
                response = await doChangeUserMobileNo(verificationCode);
            }

            if (response.success) {
                setSnackbarMessage(t("Verification successful!"));
                setSnackbarSeverity("success");
                setOpenDialog(false);
            } else {
                setSnackbarMessage(response.message || t("Verification failed!"));
                setSnackbarSeverity("error");
            }
        } catch (error) {
            setSnackbarMessage(t("An error occurred. Please try again."));
            setSnackbarSeverity("error");
        } finally {
            setOpenSnackbar(true);
        }
    };
    const doChangeEmail = async () => {
        try {
            const emailChangeToken = Cookies.get("emailChangeToken");
            if (!emailChangeToken) {
                setSnackbarMessage("حدث خطأ: لم يتم العثور على التوكن.");
                setSnackbarSeverity("error");
                setOpenSnackbar(true);
                return;
            }

            const response = await axios.post(
                `${hostURL}/doChangeUserEmail`,
                {
                    verificationCode,
                    emailChangeToken,
                    lang: i18n.language
                },
                { headers: { Authorization: `Bearer ${token}` } }
            );

            if (response.data.resultCode === "0") {
                setSnackbarMessage("تم تحديث البريد الإلكتروني بنجاح.");
                setSnackbarSeverity("success");
                setOpenSnackbar(true);
                setOpenDialog(false);
            } else {
                setSnackbarMessage(response.data.resultMessageA || "فشل في تحديث البريد الإلكتروني.");
                setSnackbarSeverity("error");
                setOpenSnackbar(true);
            }
        } catch (error) {
            setSnackbarMessage(error.response?.data?.resultMessageA || "حدث خطأ أثناء التحقق.");
            setSnackbarSeverity("error");
            setOpenSnackbar(true);
        }
    };
    // const validateUserMobileNo = async (phoneNo, countryCode) => {
    //     if (!phoneNo || !countryCode) return false;
    //     try {
    //         const response = await axios.post(
    //             `${hostURL}/validateUserMobileNo`,
    //             { value: phoneNo, countryCode, lang: i18n.language },
    //             { headers: { Authorization: `Bearer ${token}` } }
    //         );

    //         if (response.data.resultCode === "0") {
    //             Cookies.set('mobileNoChangeToken', response.data.data.mobileNoChangeToken, { expires: 1, secure: false, sameSite: 'Strict' });
    //             return true;
    //         }
    //     } catch (error) {
    //         console.error("Validation Error:", error);
    //     }
    //     return false;
    // };

    const validateUserMobileNo = async (phoneNo, countryCode) => {
        if (!phoneNo || !countryCode) {
            setFieldErrors(prev => ({ ...prev, phoneNo: i18n.language === 'ar' ? t("يرجى إدخال رقم الهاتف") : t("Please enter your phone number") }));
            return false;
        }
        try {
            const response = await axios.post(
                `${hostURL}/validateUserMobileNo`,
                { value: phoneNo, countryCode, lang: i18n.language },
                { headers: { Authorization: `Bearer ${token}` } }
            );

            if (response.data.resultCode === "0") {
                Cookies.set('mobileNoChangeToken', response.data.data.mobileNoChangeToken, { expires: 1 });
                setFieldErrors(prev => ({ ...prev, phoneNo: '' }));
                return true;
            } else {
                const errorMsg = i18n.language === 'ar' ? response.data.resultMessageA : response.data.resultMessageE;
                setFieldErrors(prev => ({ ...prev, phoneNo: errorMsg || t("فشل التحقق من الهاتف") }));
            }
        } catch (error) {
            setFieldErrors(prev => ({ ...prev, phoneNo: t("LC_000153") }));
        }
        return false;
    };


    const doChangeUserMobileNo = async () => {
        try {
            const mobileNoChangeToken = Cookies.get("mobileNoChangeToken");
            if (!mobileNoChangeToken) {
                setSnackbarMessage("حدث خطأ: لم يتم العثور على التوكن.");
                setSnackbarSeverity("error");
                setOpenSnackbar(true);
                return;
            }

            const response = await axios.post(
                `${hostURL}/doChangeUserMobileNo`,
                {
                    verificationCode,
                    mobileNoChangeToken,
                    lang: i18n.language
                },
                { headers: { Authorization: `Bearer ${token}` } }
            );

            if (response.data.resultCode === "0") {
                setSnackbarMessage("تم تحديث رقم الهاتف بنجاح.");
                setSnackbarSeverity("success");
                setOpenSnackbar(true);
                setOpenDialog(false);
            } else {
                setSnackbarMessage(response.data.resultMessageA || "فشل في تحديث رقم الهاتف.");
                setSnackbarSeverity("error");
                setOpenSnackbar(true);
            }
        } catch (error) {
            setSnackbarMessage(error.response?.data?.resultMessageA || "حدث خطأ أثناء التحقق.");
            setSnackbarSeverity("error");
            setOpenSnackbar(true);
        }
    };

    return (
        <AppTheme {...props} themeComponents={xThemeComponents}>
            <CssBaseline enableColorScheme />
            <Stack
                spacing={2}
                sx={{
                    alignItems: 'center',
                    mx: 3,
                    pb: 5,
                    mt: { xs: 8, md: 0 },
                }}
            >
                {isLoggedIn && <Header pageName="LC_000018" />}

                <Paper
                    elevation={3}
                    sx={{
                        maxWidth: 1000,
                        margin: 'auto',
                        padding: 4,
                        borderRadius: 3,
                        boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.2)',
                        backgroundColor: theme.palette.background.paper,
                        color: theme.palette.text.primary,
                        // mt:10
                    }}
                >
                    {/* gap: 2, */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, gap: 0.5, }}>
                        <IconButton onClick={() => navigate(-1)} sx={{ color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary }}>
                            {i18n.language === 'ar' ? <ArrowForwardIcon /> : <ArrowBackIcon />}
                        </IconButton>
                        <Typography variant="h5" sx={{ flexGrow: 1, color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary, fontFamily: theme.fontPrimary }}>
                            {t("LC_000018")}
                        </Typography>
                    </Box>

                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <Avatar
                            alt={userNameEn}
                            src={profilePic}
                            sx={{ width: 85, height: 85, margin: 'auto', border: (theme) => `3px solid ${theme.palette.mode === 'dark' ? '#fff' : currentColors.secondary}` }}
                        />
                        <Typography variant="h6" sx={{ mt: 1, fontFamily: theme.fontPrimary }}>
                            {t("LC_000019")}
                        </Typography>
                        <input
                            type="file"
                            accept="image/*"
                            style={{ display: "none" }}
                            id="upload-photo"
                            onChange={handleImageUpload}
                        />
                        <label htmlFor="upload-photo">
                            <IconButton component="span"
                                sx={{
                                    borderRadius: "50%",
                                    padding: 1,
                                    "&:hover": { "& svg": { color: currentColors.primary } },
                                }}>
                                <CameraAltIcon sx={{ color: theme.palette.mode === "dark" ? "white" : currentColors.secondary }} />
                            </IconButton>
                        </label>
                    </Box>

                    <Divider sx={{ my: 3 }} />

                    <form onSubmit={formik.handleSubmit}>
                        <Typography
                            variant="h6"
                            sx={(theme) => ({
                                mb: 2,
                                color: theme.palette.mode === 'dark' ? 'white' : currentColors.secondary,
                                fontFamily: theme.fontPrimary
                            })}
                        >
                            {t("LC_000142")}
                        </Typography>


                        <Grid container spacing={2}>
                            {[
                                { id: 'empID', label: t('LC_000123'), disabled: true },
                                { id: 'userCode', label: t('LC_000122'), disabled: true },
                                { id: 'nameAr', label: t('LC_000044') },
                                { id: 'nameEn', label: t('LC_000045') },
                                { id: 'telNo', label: 'ExtensionNo' },
                            ].map(({ id, label, disabled }) => (
                                <Grid item xs={12} sm={6} key={id}>
                                    <TextField
                                        fullWidth
                                        id={id}
                                        name={id}
                                        label={t(label)}
                                        variant="standard"
                                        value={formik.values[id]}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        error={formik.touched[id] && Boolean(formik.errors[id])}
                                        helperText={formik.touched[id] && formik.errors[id]}
                                        disabled={disabled}
                                        InputLabelProps={{
                                            sx: {
                                                textAlign: i18n.language === 'ar' ? 'right' : 'left',
                                                width: '1000%',
                                                fontFamily: theme.fontPrimary,

                                            },
                                        }}
                                        InputProps={{
                                            sx: {
                                                fontFamily: theme.fontPrimary,
                                            },
                                        }}
                                    />
                                </Grid>
                            ))}
                        </Grid>

                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                            <Button
                                type="submit"
                                variant=""
                                onClick={handleUpdateProfile}
                                sx={{
                                    background: currentColors.secondary,
                                    fontFamily: theme.fontPrimary,
                                    color: 'white',
                                    '&:hover': { background: currentColors.primary },
                                }}
                            >
                                {t('LC_000125')}
                            </Button>
                            <AlertSnackbar
                                open={openSnackbar}
                                onClose={() => setOpenSnackbar(false)}
                                message={snackbarMessage}
                                severity={snackbarSeverity}
                            />
                        </Box>


                        <Divider sx={{ my: 3 }} />

                        <Typography variant="h6" sx={{ mt: 4, mb: 2, color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary, fontFamily: theme.fontPrimary }}>
                            {t("LC_000143")}
                        </Typography>
                        <Grid container spacing={2}>
                            {[
                                { id: "phoneNo", label: t("LC_000124"), isPhone: true },
                                { id: "email", label: t("LC_000081"), isPhone: false },
                            ].map(({ id, label, isPhone }) => (
                                <Grid item xs={12} sm={6} key={id}>
                                    {isPhone ? (<Grid container spacing={1} alignItems="center" >
                                        {/* قائمة اختيار مقدمة الهاتف */}
                                        <Grid item xs={4} >
                                            <FormControl fullWidth variant="standard" >
                                                <Select
                                                    value={phonePrefix}
                                                    onChange={(e) => setPhonePrefix(e.target.value)}
                                                    displayEmpty
                                                    renderValue={(selected) => {
                                                        const selectedItem = phonePrefixes.find((item) => item.countryCode === selected);
                                                        return (
                                                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                                {selectedItem && <img src={selectedItem.icon} alt="" width="24" style={{ borderRadius: "4px" }} />}
                                                                <Typography variant="body1">
                                                                    {selectedItem ? selectedItem.phonePrefix : t("Select")}
                                                                </Typography>
                                                            </Box>
                                                        );
                                                    }}
                                                    MenuProps={{
                                                        PaperProps: {
                                                            sx: {
                                                                maxHeight: 250,
                                                                boxShadow: 3,
                                                                borderRadius: 2,
                                                                "& .MuiMenuItem-root": {
                                                                    paddingY: 1,
                                                                    fontSize: "1rem",
                                                                },
                                                            },
                                                        },
                                                    }}
                                                >
                                                    {phonePrefixes.map(({ countryCode, phonePrefix, shortName, icon }) => (
                                                        <MenuItem key={countryCode} value={countryCode}>
                                                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                                <img src={icon} alt={shortName} width="24" style={{ borderRadius: "4px" }} />
                                                                <Typography variant="body1">{phonePrefix} ({shortName})</Typography>
                                                            </Box>
                                                        </MenuItem>
                                                    ))}
                                                </Select>

                                            </FormControl>
                                        </Grid>

                                        {/* إدخال رقم الهاتف بدون المقدمة */}
                                        <Grid item xs={8}>
                                            <TextField
                                                fullWidth
                                                id={id}
                                                name={id}
                                                label={t(label)}
                                                variant="standard"
                                                value={formik.values[id]}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                // error={formik.touched[id] && Boolean(formik.errors[id])}
                                                // helperText={formik.touched[id] && formik.errors[id]}
                                                helperText={
                                                    formik.touched[id] && formik.errors[id]
                                                        ? formik.errors[id]
                                                        : fieldErrors[id]
                                                }
                                                error={Boolean(formik.touched[id] && formik.errors[id]) || Boolean(fieldErrors[id])}
                                                InputLabelProps={{
                                                    sx: {
                                                        textAlign: i18n.language === 'ar' ? 'right' : 'left',
                                                        width: '1000%',
                                                        fontFamily: theme.fontPrimary,
                                                    },
                                                }}
                                                InputProps={{
                                                    sx: {
                                                        fontFamily: theme.fontPrimary,
                                                    },
                                                    endAdornment: (
                                                        <InputAdornment position="end">
                                                            {phoneLoading ? (
                                                                <CircularProgress size={20} sx={{
                                                                    mb: 1, color: (theme) =>
                                                                        theme.palette.mode === "dark" ? "white" : currentColors.primary,
                                                                }} />
                                                            ) : (
                                                                <EditIcon
                                                                    onClick={async () => {
                                                                        if (id === "email") {
                                                                            const isValid = await validateEmail(formik.values.email);
                                                                            if (isValid) {
                                                                                setMode("email");
                                                                                setOpenDialog(true);
                                                                            }
                                                                        } else if (id === "phoneNo") {
                                                                            setPhoneLoading(true);
                                                                            const isValid = await validateUserMobileNo(formik.values.phoneNo, phonePrefix);
                                                                            setPhoneLoading(false);
                                                                            if (isValid) {
                                                                                setMode("phoneNo");
                                                                                setOpenDialog(true);
                                                                            }
                                                                        }
                                                                    }}
                                                                    sx={{
                                                                        color: (theme) =>
                                                                            theme.palette.mode === "dark" ? "white" : currentColors.secondary,
                                                                        mb: 1,
                                                                        cursor: "pointer",
                                                                        "&:hover": { color: currentColors.primary },
                                                                    }}
                                                                />
                                                            )}
                                                        </InputAdornment>
                                                    )
                                                }}
                                            />
                                        </Grid>
                                    </Grid>

                                    ) : (
                                        <TextField
                                            fullWidth
                                            id={id}
                                            name={id}
                                            label={t(label)}
                                            variant="standard"
                                            value={formik.values[id]}
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            // error={formik.touched[id] && Boolean(formik.errors[id])}
                                            // helperText={formik.touched[id] && formik.errors[id]}
                                            helperText={
                                                formik.touched[id] && formik.errors[id]
                                                    ? formik.errors[id]
                                                    : fieldErrors[id]
                                            }
                                            error={Boolean(formik.touched[id] && formik.errors[id]) || Boolean(fieldErrors[id])}
                                            InputLabelProps={{
                                                sx: {
                                                    textAlign: i18n.language === 'ar' ? 'right' : 'left',
                                                    width: '1000%',
                                                    fontFamily: theme.fontPrimary,
                                                },
                                            }}
                                            InputProps={{
                                                sx: {
                                                    fontFamily: theme.fontPrimary,
                                                },
                                                endAdornment: (
                                                    <InputAdornment position="end">
                                                        {editLoading ? (
                                                            <CircularProgress size={20} sx={{
                                                                mb: 1, color: (theme) =>
                                                                    theme.palette.mode === "dark" ? "white" : currentColors.primary,
                                                            }} />
                                                        ) : (
                                                            <EditIcon
                                                                onClick={async () => {
                                                                    if (id === "email") {
                                                                        setEditLoading(true);
                                                                        const isValid = await validateEmail(formik.values.email);
                                                                        setEditLoading(false);
                                                                        if (isValid) {
                                                                            setMode("email");
                                                                            setOpenDialog(true);
                                                                        }
                                                                    }
                                                                }}
                                                                sx={{
                                                                    color: (theme) =>
                                                                        theme.palette.mode === "dark" ? "white" : currentColors.secondary,
                                                                    mb: 1,
                                                                    cursor: "pointer",
                                                                    "&:hover": { color: currentColors.primary },
                                                                }}
                                                            />
                                                        )}
                                                    </InputAdornment>
                                                )
                                            }}
                                        />
                                    )}
                                </Grid>
                            ))}
                        </Grid>

                    </form>
                    <Divider sx={{ my: 3 }} />
                </Paper>
            </Stack>


            <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
                <DialogTitle>{t("LC_000084")}</DialogTitle>
                <DialogContent>
                    <TextField
                        fullWidth
                        label={t("LC_000144")}
                        variant="standard"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenDialog(false)}>{t("LC_000087")}</Button>
                    <Button onClick={handleVerifyCode} color="primary">{t("LC_000088")}</Button>
                </DialogActions>
                <AlertSnackbar
                    open={openSnackbar}
                    onClose={() => setOpenSnackbar(false)}
                    message={snackbarMessage}
                    severity={snackbarSeverity}
                />
            </Dialog>


        </AppTheme>
    );
}

