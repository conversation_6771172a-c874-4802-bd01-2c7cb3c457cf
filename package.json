{"name": "materialui2", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.3.0", "@mui/styled-engine-sc": "^6.2.0", "@mui/system": "^6.2.0", "@mui/x-charts": "^7.23.1", "@mui/x-data-grid": "^7.23.1", "@mui/x-date-pickers": "^7.23.1", "@mui/x-tree-view": "^7.23.0", "@react-spring/web": "^9.7.5", "axios": "^1.7.9", "cra-template": "1.2.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "formik-mui": "^5.0.0-alpha.1", "js-cookie": "^3.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-i18next": "^15.2.0", "react-router-dom": "^7.0.2", "react-scripts": "5.0.1", "styled-components": "^6.1.13", "stylis-plugin-rtl": "^2.1.1", "typescript": "^4.9.5", "web-vitals": "^4.2.4", "yup": "^1.6.1"}, "scripts": {"start": "set PORT=5177 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}