import * as React from 'react';
import { styled } from '@mui/material/styles';
import Menu from '@mui/material/Menu';
import MuiMenuItem from '@mui/material/MenuItem';
import { paperClasses } from '@mui/material/Paper';
import { listClasses } from '@mui/material/List';
import { IconButton } from '@mui/material';
import TranslateIcon from '@mui/icons-material/Translate';
import { useTranslation } from 'react-i18next';  
import Colors from '../Common/Colors';
import { useAuth } from '../AuthProvider';
import Cookies from 'js-cookie';
import { useState } from 'react';
import { useEffect } from 'react';

const MenuItem = styled(MuiMenuItem)({
  margin: '2px 0',
});

export default function LanguageIconDropdown() {
  const { t, i18n } = useTranslation();  
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const { user } = useAuth();
  // const currentColors = Colors[user.compCode] || Colors.default;
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;

 useEffect(() => {
    const storedLanguage = sessionStorage.getItem('language') || 'en'; 
    i18n.changeLanguage(storedLanguage);
    document.documentElement.setAttribute('dir', storedLanguage === 'ar' ? 'rtl' : 'ltr');
  }, [i18n]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);  
    sessionStorage.setItem('language', lang);
    setAnchorEl(null);  

    if (lang === 'ar') {
      document.documentElement.setAttribute('dir', 'rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
    }
  };

  return (
    <React.Fragment>
      <IconButton
        data-screenshot="toggle-mode"
        onClick={handleClick}
        disableRipple
        size="small"
        aria-controls={open ? 'color-scheme-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <TranslateIcon sx={{color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary}} />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        id="menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        slotProps={{
          paper: {
            variant: 'outlined',
            elevation: 0,
            sx: {
              my: '4px',
            },
          },
        }}
        sx={{
          [`& .${listClasses.root}`]: {
            padding: '4px',
          },
          [`& .${paperClasses.root}`]: {
            padding: 0,
          },
        }}
      >
        <MenuItem onClick={() => handleLanguageChange('ar')}>{t("LC_000002")}</MenuItem>
        <MenuItem onClick={() => handleLanguageChange('en')}>{t("LC_000003")}</MenuItem>
      </Menu>
    </React.Fragment>
  );
}



// import * as React from 'react';
// import { styled } from '@mui/material/styles';
// import Menu from '@mui/material/Menu';
// import MuiMenuItem from '@mui/material/MenuItem';
// import { paperClasses } from '@mui/material/Paper';
// import { listClasses } from '@mui/material/List';
// import { IconButton } from '@mui/material';
// import TranslateIcon from '@mui/icons-material/Translate';
// import { useTranslation } from 'react-i18next';  
// import Colors from '../Common/Colors';

// const MenuItem = styled(MuiMenuItem)({
//   margin: '2px 0',
// });

// export default function LanguageIconDropdown() {
//   const { i18n } = useTranslation();  
//   const [anchorEl, setAnchorEl] = React.useState(null);
//   const open = Boolean(anchorEl);
//   const handleClick = (event) => {
//     setAnchorEl(event.currentTarget);
//   };
//   const handleClose = () => {
//     setAnchorEl(null);
//   };

//   const handleLanguageChange = (lang) => {
//     i18n.changeLanguage(lang);  
//     setAnchorEl(null);  

//     // تغيير الاتجاه بناءً على اللغة
//     if (lang === 'ar') {
//       document.documentElement.setAttribute('dir', 'rtl');
//     } else {
//       document.documentElement.setAttribute('dir', 'ltr');
//     }
//   };

//   return (
//     <React.Fragment>
//       <IconButton
//         data-screenshot="toggle-mode"
//         onClick={handleClick}
//         disableRipple
//         size="small"
//         aria-controls={open ? 'color-scheme-menu' : undefined}
//         aria-haspopup="true"
//         aria-expanded={open ? 'true' : undefined}
//       >
//         <TranslateIcon sx={{color: (theme) => theme.palette.mode === 'dark' ? 'white' : Colors.secondary}} />
//       </IconButton>
//       <Menu
//         anchorEl={anchorEl}
//         id="menu"
//         open={open}
//         onClose={handleClose}
//         onClick={handleClose}
//         transformOrigin={{ horizontal: 'right', vertical: 'top' }}
//         anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
//         slotProps={{
//           paper: {
//             variant: 'outlined',
//             elevation: 0,
//             sx: {
//               my: '4px',
//             },
//           },
//         }}
//         sx={{
//           [`& .${listClasses.root}`]: {
//             padding: '4px',
//           },
//           [`& .${paperClasses.root}`]: {
//             padding: 0,
//           },
//         }}
//       >
//         <MenuItem onClick={() => handleLanguageChange('ar')}>Arabic</MenuItem>
//         <MenuItem onClick={() => handleLanguageChange('en')}>English</MenuItem>
//       </Menu>
//     </React.Fragment>
//   );
// }
