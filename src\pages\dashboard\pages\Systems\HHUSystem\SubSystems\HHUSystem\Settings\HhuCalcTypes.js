import React from 'react';
import { alpha } from '@mui/material/styles';
import AppTheme from '../../../../../../../../shared-theme/AppTheme';
import { Box, Stack } from '@mui/material';
import SideMenu from '../../../../../../components/SideMenu';
import AppNavbar from '../../../../../../components/AppNavbar';
import Header from '../../../../../../components/Header';
import CssBaseline from '@mui/material/CssBaseline';
import {
  chartsCustomizations,
  dataGridCustomizations,
  datePickersCustomizations,
  treeViewCustomizations,
} from '../../../../../../theme/customizations';
import HhuCalcTypesGrid from './components/HhuCalcTypesGrid';
import { useLocation } from 'react-router-dom'; 

const xThemeComponents = {
  ...chartsCustomizations,
  ...dataGridCustomizations,
  ...datePickersCustomizations,
  ...treeViewCustomizations,
};

export default function HhuCalcTypes(props) {
  const location = useLocation();
  const { id } = location.state || {};

  return (
    <AppTheme {...props} themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      <Box sx={{ display: 'flex' }}>
        {/* Main content */}
        <Box
          component="main"
          sx={(theme) => ({
            flexGrow: 1,
            backgroundColor: theme.vars
              ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
              : alpha(theme.palette.background.default, 1),
            overflow: 'auto',
          })}
        >
          <Stack
            spacing={2}
            sx={{
              alignItems: 'center',
              mx: 3,
              pb: 5,
              mt: { xs: 8, md: 0 },
            }}
          >
            <Header pageName={id} />
            <HhuCalcTypesGrid pageName="HhuCalcTypesGrid" />
          </Stack>
        </Box>
      </Box>
    </AppTheme>
  );
}
