import * as React from 'react';
import { styled } from '@mui/material/styles';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import MuiToolbar from '@mui/material/Toolbar';
import { tabsClasses } from '@mui/material/Tabs';
import Typography from '@mui/material/Typography';
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';
import DashboardRoundedIcon from '@mui/icons-material/DashboardRounded';
import SideMenuMobile from './SideMenuMobile';
import MenuButton from './MenuButton';
import ColorModeIconDropdown from '../../../shared-theme/ColorModeIconDropdown';
import LanguageIconDropdown from '../../../shared-theme/LanguageIconDropdown';
import { useTranslation } from 'react-i18next';
import DashboardIcon from '@mui/icons-material/Dashboard';
import Colors from '../../../Common/Colors';
import { useAuth } from '../../../AuthProvider';
import Cookies from 'js-cookie';

const Toolbar = styled(MuiToolbar)({
  width: '100%',
  padding: '12px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'start',
  justifyContent: 'center',
  gap: '12px',
  flexShrink: 0,
  [`& ${tabsClasses.flexContainer}`]: {
    gap: '8px',
    p: '8px',
    pb: 0,
  },
});

export default function AppNavbar() {
  const [open, setOpen] = React.useState(false);
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  // const currentColors = Colors[user.compCode] || Colors.default;

  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;


  const toggleDrawer = (newOpen) => () => {
    setOpen(newOpen);
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        display: { xs: 'auto', md: 'none' },
        boxShadow: 0,
        bgcolor: 'background.paper',
        backgroundImage: 'none',
        borderBottom: '1px solid',
        borderColor: 'divider',
        top: 'var(--template-frame-height, 0px)',
      }}
    >
      <Toolbar variant="regular">
        <Stack
          direction="row"
          sx={{
            alignItems: 'center',
            flexGrow: 1,
            width: '100%',
            gap: 1,
          }}
        >
          <Stack
            direction="row"
            spacing={1}
            sx={{
              justifyContent: i18n.language === 'ar' ? 'flex-start' : 'flex-start',
              ml: i18n.language === 'ar' ? 'auto' : 0,
              mr: i18n.language === 'en' ? 'auto' : 0,
              direction: i18n.language === 'ar' ? 'rtl' : 'ltr',
            }}
          >
            <DashboardIcon sx={{
              color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
            }} />
            <Typography variant="h5" component="h1" sx={{ color: 'text.primary' }} >
              {t('LC_000013')}
            </Typography>
          </Stack>
          <LanguageIconDropdown />
          <ColorModeIconDropdown />
          <MenuButton aria-label="menu" onClick={toggleDrawer(true)}>
            <MenuRoundedIcon sx={{color:(theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary,}}/>
          </MenuButton>
          <SideMenuMobile open={open} toggleDrawer={toggleDrawer} currentLanguage={i18n.language} />
        </Stack>
      </Toolbar>
    </AppBar>
  );
}