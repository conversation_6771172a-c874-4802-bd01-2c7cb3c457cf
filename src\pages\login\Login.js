import * as React from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Stack from '@mui/material/Stack';
import SignInCard from './components/SignInCard';
import Content from './components/Content';
import AppTheme from '../../shared-theme/AppTheme';
import ColorModeSelect from '../../shared-theme/ColorModeSelect';
import { Box } from '@mui/material';
import LanguageIconSignIn from './components/LanguageIconSignIn';

export default function Login(props) {
  return (
    <AppTheme {...props}>
      <CssBaseline enableColorScheme />
      {/* <ColorModeSelect
        sx={{
          position: 'fixed',
          top: '1rem',
          right: '1rem',
          zIndex: 1, 
        }}
      /> */}
      <LanguageIconSignIn
        sx={{
          position: 'fixed',
          top: '2rem',
          right: '1rem',
          zIndex: 1000,
        }}
      />

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          // height: '100vh',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'url(./background.jpg)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            opacity: 0.3,
            zIndex: -1,
          }}
        />
        {/* Main content */}
        <Stack
          direction="column"
          component="main"
          sx={{
            justifyContent: 'center',
            height: '100%',
            width: '100%',
            // p: 2,
            mx: 'auto',
          }}
        >
          <Stack
            direction={{ xs: 'column-reverse', md: 'row' }}
            sx={{
              justifyContent: 'center',
              gap: { xs: 6, sm: 12 },
              // p: { xs: 2, sm: 4 },
              m: 'auto',
              width: '100%',
              height: '100%'
            }}
          >
            {/* <Content /> */}
            <SignInCard />
          </Stack>
        </Stack>
      </Box>
    </AppTheme>
  );
}
