import React from "react";
import { Button, Typography, Box } from "@mui/material";
import { Home as HomeIcon } from '@mui/icons-material';
import { keyframes } from "@emotion/react";
import Colors from "./Common/Colors";
import { useTranslation } from 'react-i18next';

const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(-60px); }
  to { opacity: 1; transform: translateY(0); }
`;

const NotFound = () => {
  const { t, i18n } = useTranslation();
  // const language = sessionStorage.getItem('language') || 'en';
  // const direction = language === 'ar' ? 'rtl' : 'ltr';

  React.useEffect(() => {
    const storedLanguage = sessionStorage.getItem('language') || 'en';
    i18n.changeLanguage(storedLanguage);
    document.documentElement.setAttribute('dir', storedLanguage === 'ar' ? 'rtl' : 'ltr');
  }, [i18n]);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        bgcolor: "background.default",
        color: "text.primary",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center",
        animation: `${fadeIn} 1s ease-out`,
        p: 3,
      }}
    >
      <Typography
        variant="h1"
        sx={{
          fontWeight: "bold",
          fontSize: { xs: "6rem", md: "8rem" },
          color: Colors.primary,
          mb: 2,
          // fontFamily: "Cairo, sans-serif"
          fontFamily: 'Segoe UI Symbol',

        }}
      >
        404
      </Typography>

      <Typography
        variant="h5"
        sx={{
          mb: 1,
          // fontFamily: "Cairo, sans-serif"
          fontFamily: 'Segoe UI Symbol',

        }}
      >
        {t("LC_000151")}
      </Typography>

      <Typography
        variant="body1"
        sx={{
          color: "text.secondary",
          mb: 4,
          // fontFamily: "Cairo, sans-serif"
          fontFamily: 'Segoe UI Symbol',

        }}
      >
        {t("LC_000152")}
      </Typography>

      <Button
        variant="contained"
        href="/"
        startIcon={i18n.language === 'ar' ? <HomeIcon sx={{ ml: 1, }} /> : null}
        endIcon={i18n.language === 'en' ? <HomeIcon /> : null}
        sx={{
          bgcolor: Colors.secondary,
          color: "white",
          px: 4,
          py: 1.5,
          fontSize: "1rem",
          // fontWeight: "bold",
          borderRadius: 8,
          boxShadow: 4,
          // fontFamily: "Cairo, sans-serif",
          fontFamily: 'Segoe UI Symbol',
          transition: "all 0.3s ease",
          "&:hover": {
            bgcolor: Colors.primary,
            transform: "scale(1.05)",
            boxShadow: 6,
          },
          // ...(i18n.language === 'ar' && {
          //   // mr: 1,
          // })
        }}
      >
        {t("LC_000149")}
      </Button>
    </Box>
  );
};

export default NotFound;

// import React from "react";
// import { Button, Typography, Box } from "@mui/material";
// import Colors from "./Common/Colors";

// const NotFound = () => {
//   return (
//     <Box
//       sx={{
//         display: "flex",
//         flexDirection: "column",
//         alignItems: "center",
//         justifyContent: "center",
//         height: "100vh",
//         textAlign: "center",
//         p: 2,
//       }}
//     >
//       <Typography variant="h1" sx={{ fontWeight: "bold", mb: 2 }}>
//         404
//       </Typography>
//       <Typography variant="h5" sx={{ mb: 1 }}>
//         Page Not Found
//       </Typography>
//       <Typography variant="body1" sx={{ color: "text.secondary", mb: 3 }}>
//         Sorry, the page you are looking for does not exist.
//       </Typography>
//       <Button
//         variant="contained"
//         href="/"
//         sx={{
//           bgcolor: Colors.secondary,
//           color: "white",
//           fontWeight: "bold",
//           boxShadow: 3,
//           fontFamily: "cairo",
//           transition: "transform 0.2s, background-color 0.3s",
//           "&:hover": {
//             bgcolor: Colors.primary,
//             transform: "scale(1.05)",
//           },
//         }}
//       >
//         Go Back to Home
//       </Button>
//     </Box>
//   );
// };

// export default NotFound;
