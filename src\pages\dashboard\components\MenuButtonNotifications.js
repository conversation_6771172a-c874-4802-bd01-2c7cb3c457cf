import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Badge, { badgeClasses } from '@mui/material/Badge';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import Button from '@mui/material/Button';

function MenuButtonNotifications({ showBadge = false, notifications = [], ...props }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const isOpen = Boolean(anchorEl);

  const handleOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const renderNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleOutlineIcon color="success" />;
      case 'error':
        return <ErrorOutlineIcon color="error" />;
      default:
        return <NotificationsActiveIcon color="primary" />;
    }
  };

  return (
    <>
      <Badge
        color="error"
        variant="dot"
        invisible={!showBadge}
        sx={{ [`& .${badgeClasses.badge}`]: { right: 2, top: 2 } }}
      >
        <IconButton size="small" {...props} onClick={handleOpen}>
          {props.children}
        </IconButton>
      </Badge>
      <Menu
        anchorEl={anchorEl}
        open={isOpen}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 350,
            maxHeight: 450,
            overflow: 'auto',
            boxShadow: 4,
          },
        }}
      >
        <Typography variant="subtitle1" sx={{ px: 2, py: 1, fontWeight: 'bold' }}>
          Notifications
        </Typography>
        <Divider />
        {notifications.length === 0 ? (
          <MenuItem disabled>
            <Typography variant="body2" color="text.secondary">
              No new notifications
            </Typography>
          </MenuItem>
        ) : (
          notifications.map((notification, index) => (
            <MenuItem key={index} onClick={handleClose} sx={{ alignItems: 'flex-start' }}>
              <ListItemIcon>{renderNotificationIcon(notification.type)}</ListItemIcon>
              <ListItemText
                primary={notification.message}
                secondary={notification.time}
                primaryTypographyProps={{ fontSize: '0.9rem', fontWeight: '500' }}
                secondaryTypographyProps={{ fontSize: '0.75rem', color: 'text.secondary' }}
              />
            </MenuItem>
          ))
        )}
        {notifications.length > 0 && (
          <>
            <Divider />
            <Button
              size="small"
              fullWidth
              sx={{ textTransform: 'none', py: 1 }}
              onClick={handleClose}
            >
              View All Notifications
            </Button>
          </>
        )}
      </Menu>
    </>
  );
}

MenuButtonNotifications.propTypes = {
  showBadge: PropTypes.bool,
  notifications: PropTypes.arrayOf(
    PropTypes.shape({
      type: PropTypes.oneOf(['success', 'error', 'info']), // Notification type
      message: PropTypes.string.isRequired, // Notification message
      time: PropTypes.string.isRequired, // Time or timestamp
    })
  ),
};

export default MenuButtonNotifications;



// import * as React from 'react';
// import PropTypes from 'prop-types';
// import Badge, { badgeClasses } from '@mui/material/Badge';
// import IconButton from '@mui/material/IconButton';

// function MenuButtonNotifications({ showBadge = false, ...props }) {
//   return (
//     <Badge
//       color="error"
//       variant="dot"
//       invisible={!showBadge}
//       sx={{ [`& .${badgeClasses.badge}`]: { right: 2, top: 2 } }}
//     >
//       <IconButton size="small" {...props} />
//     </Badge>
//   );
// }

// MenuButtonNotifications.propTypes = {
//   showBadge: PropTypes.bool,
// };

// export default MenuButtonNotifications;
