import * as React from 'react';
import FormControl from '@mui/material/FormControl';
import InputAdornment from '@mui/material/InputAdornment';
import OutlinedInput from '@mui/material/OutlinedInput';
import SearchRoundedIcon from '@mui/icons-material/SearchRounded';
import Colors from '../../../Common/Colors';
import { useAuth } from '../../../AuthProvider';
import Cookies from 'js-cookie';
import { useTranslation } from 'react-i18next';

export default function Search() {
  const { user } = useAuth();
  const { t, i18n } = useTranslation();

  // const currentColors = Colors[user.compCode] || Colors.default;
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;
  
  return (
    <FormControl sx={{ width: { xs: '100%', md: '25ch' } }} variant="outlined">
      <OutlinedInput
        size="small"  id="search"
        placeholder={t('LC_000146')}
        sx={{ flexGrow: 1 }}
        startAdornment={
          <InputAdornment position="start" sx={{ color: 'text.primary' }}>
            <SearchRoundedIcon fontSize="small" sx={{color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary}} />
          </InputAdornment>
        }
        inputProps={{
          'aria-label': 'search',
        }}
      />
    </FormControl>
  );
}
