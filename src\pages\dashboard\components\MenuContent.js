import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Link } from 'react-router-dom';
import { Box, Divider, List, ListItemButton, ListItemIcon, ListItemText, ListSubheader, Collapse, Stack, Button, IconButton, ButtonGroup } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import DashboardIcon from '@mui/icons-material/Dashboard';
import DescriptionIcon from '@mui/icons-material/Description';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import SupportIcon from '@mui/icons-material/Support';
import TapAndPlayIcon from '@mui/icons-material/TapAndPlay';
import EditNoteIcon from '@mui/icons-material/EditNote';
import GavelIcon from '@mui/icons-material/Gavel';
import ArchiveIcon from '@mui/icons-material/Archive';
import BusinessIcon from '@mui/icons-material/Business';
import EngineeringIcon from '@mui/icons-material/Engineering';
import HistoryEduIcon from '@mui/icons-material/HistoryEdu';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import SelectAllIcon from '@mui/icons-material/SelectAll';
import SolarPowerIcon from '@mui/icons-material/SolarPower';
import MiscellaneousServicesIcon from '@mui/icons-material/MiscellaneousServices';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import LocalPoliceIcon from '@mui/icons-material/LocalPolice';
import CarRentalIcon from '@mui/icons-material/CarRental';
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircle';
import IsoIcon from '@mui/icons-material/Iso';
import BalanceIcon from '@mui/icons-material/Balance';
import ApartmentIcon from '@mui/icons-material/Apartment';
import { useAuth } from '../../../AuthProvider';
import routesConfig from '../../../routesConfig';
import ConstructionIcon from '@mui/icons-material/Construction';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import Colors from '../../../Common/Colors';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import StarBorderIcon from '@mui/icons-material/StarBorder';
import StarIcon from '@mui/icons-material/Star';
import { hostURL } from '../../../Common/APIs';
import axios from 'axios';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FavoriteIcon from '@mui/icons-material/Favorite';
import Cookies from 'js-cookie';

const ICONS = {
  CallCenter: <SupportAgentIcon />,
  ComplaintsSystems: <EditNoteIcon />,
  DamageSystems: <ConstructionIcon />,
  BasicSystem: <ViewModuleIcon />,
  AlShamelSystem: <SelectAllIcon />,
  BIDSystem: <GavelIcon />,
  BranchesPlan: <ApartmentIcon />,
  DocumentArchiving: <ArchiveIcon />,
  DaughterCompany: <BusinessIcon />,
  ExamSystem: <HistoryEduIcon />,
  FixSystem: <EngineeringIcon />,
  HHUSystem: <TapAndPlayIcon />,
  ISOSystem: <IsoIcon />,
  JudiciarySystem: <BalanceIcon />,
  MiscellaneousManagementSystem: <MiscellaneousServicesIcon />,
  ProjectManagementSystem: <AccountTreeIcon />,
  SolarSystem: <SolarPowerIcon />,
  ElectriciansLicensing: <LocalPoliceIcon />,
  RentSystem: <CarRentalIcon />,
  ShareHolders: <SupervisedUserCircleIcon />,
};

const DotIcon = memo(({ color }) => (
  <Box sx={{ marginRight: 1, display: 'flex', alignItems: 'center' }}>
    <svg width={6} height={6}>
      <circle cx={3} cy={3} r={3} fill={color} />
    </svg>
  </Box>
));

DotIcon.propTypes = {
  color: PropTypes.string.isRequired,
};

const MenuContent = memo(({ isDrawerOpen, onOpenDrawer, expanded, setExpanded }) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [activeItem, setActiveItem] = useState('');
  const { user, updateFavorites } = useAuth();
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;
  const accessTokenExpiryInSeconds = Cookies.get('accessTokenExpiryInSeconds');

  const [favorites, setFavorites] = useState(user.favorites || []);
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(() => {
    const value = Cookies.get('showFavoritesOnly');
    return value ? value === 'true' : false;
  });
  const token = Cookies.get('authToken');
  const theme = useTheme();


  useEffect(() => {
    // console.log("showFavoritesOnly value:", showFavoritesOnly);

    Cookies.set('showFavoritesOnly', showFavoritesOnly.toString(), { expires: (accessTokenExpiryInSeconds / 86400 ) });
  }, [showFavoritesOnly]);

  const handleShowFavorites = useCallback(() => {
    if (favorites.length === 0) {
      alert(t('لا توجد عناصر مفضلة لعرضها'));
    } else {
      setShowFavoritesOnly(true);
    }
  }, [favorites, t]);

  const handleShowAll = useCallback(() => {
    setShowFavoritesOnly(false);
    // alert('سيتم عرض جميع الأنظمة');
  }, []);

  const toggleFavorite = useCallback(async (itemId) => {
    try {
      const sysCode = itemId.substring(0, 2);
  
      const response = await axios.post(
        `${hostURL}/favoriteAction`,
        { sysCode: sysCode, objectCode: itemId },
        { headers: { Authorization: `Bearer ${token}` } }
      );
  
      if (response.data.resultCode === '0') {
        setFavorites((prevFavorites) => {
          let updatedFavorites = [...prevFavorites];
          const isFavorite = prevFavorites.includes(itemId);
  
          const parentIds = [
            itemId.substring(0, 2),
            itemId.substring(0, 4),
            itemId.substring(0, 6),
          ];
  
          if (isFavorite) {
            updatedFavorites = updatedFavorites.filter(id => id !== itemId);
  
            parentIds.reverse().forEach((parentId) => {
              const hasChildren = updatedFavorites.some(
                id => id !== parentId && id.startsWith(parentId)
              );
              if (!hasChildren) {
                updatedFavorites = updatedFavorites.filter(id => id !== parentId);
              }
            });
          } else {
            updatedFavorites = Array.from(new Set([
              ...updatedFavorites,
              ...parentIds,
              itemId
            ]));
          }
  
          updateFavorites(updatedFavorites);
          return updatedFavorites;
        });
      } else {
        console.error('خطأ في تحديث المفضلة:', response.data.resultMessageE);
      }
    } catch (error) {
      console.error('خطأ في تحديث المفضلة:', error);
    }
  }, [token, updateFavorites]);
  


  const hasFavoriteInSubItems = useCallback((item) => {
    if (favorites.includes(item.id)) {
      return true;
    }

    if (item.subSystems && item.subSystems.some((subItem) => hasFavoriteInSubItems(subItem))) {
      return true;
    }

    if (item.subMenus && item.subMenus.some((subItem) => hasFavoriteInSubItems(subItem))) {
      return true;
    }

    if (item.subObjects && item.subObjects.some((subItem) => hasFavoriteInSubItems(subItem))) {
      return true;
    }

    return false;
  }, [favorites]);

  const filteredItems = useMemo(() => {
    return showFavoritesOnly
      ? routesConfig.filter((item) => {
        return user.favorites.includes(item.id) || hasFavoriteInSubItems(item);
      })
      : routesConfig;
  }, [showFavoritesOnly, user.favorites, hasFavoriteInSubItems]);

  const handleSubmenuClick = useCallback((id, path) => {
    if (path) {
      navigate(path, { state: { expandedMenus: expanded, id: id } });
    }
    setActiveItem(id);

    const isMainSystem = id.length === 2;
    const isSubSystem = id.length === 4;
    const isMenu = id.length === 6;
    const isObject = id.length === 8;

    if (isSubSystem || isMenu || isObject) {
      setExpanded((prev) => {
        const parentMainSystem = id.substring(0, 2);
        const parentSubSystem = isMenu || isObject ? id.substring(0, 4) : null;
        const newState = [...prev];
        if (!newState.includes(parentMainSystem)) {
          newState.push(parentMainSystem);
        }
        if (parentSubSystem && !newState.includes(parentSubSystem)) {
          newState.push(parentSubSystem);
        }
        return newState;
      });
    } else if (isMainSystem) {
      setExpanded((prev) => {
        const newState = prev.filter((menu) => menu === id || menu.startsWith(id));
        if (!newState.includes(id)) {
          newState.push(id);
        }
        return newState;
      });
    }
  }, [navigate, expanded, setExpanded]);

  const toggleSubmenu = useCallback((menuId) => {
    if (!isDrawerOpen) onOpenDrawer();

    setExpanded((prev) => {
      const isMainSystem = menuId.length === 2;
      const isSubSystem = menuId.length === 4;
      const isMenu = menuId.length === 6;
      const isObject = menuId.length === 8;

      if (isMainSystem) {
        if (prev.includes(menuId)) {
          return prev.filter((id) => id !== menuId && !id.startsWith(menuId));
        } else {
          const newState = prev.filter((id) => id.length !== 2);
          newState.push(menuId);
          return newState;
        }
      } else if (isSubSystem) {
        const parentMainSystem = menuId.substring(0, 2);
        if (prev.includes(menuId)) {
          return prev.filter((id) => id !== menuId);
        } else {
          const newState = prev.filter((id) => !id.startsWith(parentMainSystem) || id === parentMainSystem);
          newState.push(menuId);
          return newState;
        }
      } else if (isMenu) {
        const parentSubSystem = menuId.substring(0, 4);
        const parentMainSystem = menuId.substring(0, 2);
        if (prev.includes(menuId)) {
          return prev.filter((id) => id !== menuId);
        } else {
          const newState = prev.filter((id) => !id.startsWith(parentSubSystem) || id === parentSubSystem || id === parentMainSystem);
          newState.push(menuId);
          return newState;
        }
      } else if (isObject) {
        return prev;
      }

      return prev;
    });
  }, [isDrawerOpen, onOpenDrawer, setExpanded]);

  const renderMenuItems = useCallback((items) => {
    return items.map((item) => {
      const hasSubSystems = item.subSystems && item.subSystems.length > 0;
      const hasSubMenus = item.subMenus && item.subMenus.length > 0;
      const hasSubObjects = item.subObjects && item.subObjects.length > 0;

      // console.log("Favorites:", user.favorites);
      // console.log("Permissions:", user.permissions);
      // console.log("Item ID:", item.id);

      // console.log("user:", user);
      // console.log("user.permissions:", user?.permissions);
      // console.log("favorites:", favorites);
      // console.log("item.id:", item.id);

      const hasPermissionForMainSystem = showFavoritesOnly
        ? Array.isArray(favorites) && favorites.includes(item.id)
        : Array.isArray(user?.permissions) && user.permissions.includes(item.id);

      if (!hasPermissionForMainSystem) {
        return null;
      }


      const isActive = location.pathname === item.path;
      const isExpanded = Array.isArray(expanded) && expanded.includes(item.id);
      const isFavorite = favorites.includes(item.id);

      const isMainSystem = hasSubSystems || hasSubMenus || hasSubObjects;

      return (
        <React.Fragment key={item.id}>
          <ListItemButton
            sx={{
              [i18n.dir() === 'rtl' ? 'marginRight' : 'marginLeft']: -1,
              textAlign: i18n.dir() === 'rtl' ? 'right' : 'left',
              bgcolor: isActive ? 'primary.main' : 'inherit',
              '&:hover': { bgcolor: 'primary.light' },
            }}
            selected={isActive}
            onClick={() => {
              if (hasSubSystems || hasSubMenus || hasSubObjects) {
                toggleSubmenu(item.id);
              } else if (item.path) {
                handleSubmenuClick(item.id, item.path);
              }
            }}
          >
            <ListItemIcon
              sx={{
                [i18n.dir() === 'rtl' ? 'marginLeft' : 'marginRight']: 3,
                color: isActive ? 'primary.contrastText' : (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.secondary),
              }}
            >
              {/* {ICONS[item.systemIcon] || <DotIcon color={isActive ? currentColors.primary : currentColors.secondary} />} */}
              {ICONS[item.systemIcon] || !isMainSystem && !showFavoritesOnly && (<IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(item.id);
                }}
                sx={{ color: isFavorite ? 'gold' : 'gray' }}
              >
                {isFavorite ? <StarIcon /> : <StarBorderIcon />}
              </IconButton>) || <DotIcon color={isActive ? currentColors.primary : currentColors.secondary} />}

            </ListItemIcon>
            {/* <Stack direction="row" alignItems="center" spacing={1}> */}
            <ListItemText
              primary={t(item.id)}
              sx={{
                color: isActive ? currentColors.primary : (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.secondary),
              }}
              primaryTypographyProps={{ fontFamily: theme.fontPrimary, fontWeight: 'bold !important' }}
            />
            {hasSubSystems || hasSubMenus || hasSubObjects ? (
              isExpanded ? <ExpandLess /> : <ExpandMore />
            ) : null}
          </ListItemButton>

          {(hasSubSystems || hasSubMenus || hasSubObjects) && (
            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {hasSubSystems && renderMenuItems(item.subSystems)}
                {hasSubMenus && renderMenuItems(item.subMenus)}
                {hasSubObjects &&
                  renderMenuItems(
                    item.subObjects.map((subItem) => ({
                      ...subItem,
                      id: subItem.id,
                    }))
                  )}
              </List>
            </Collapse>
          )}
        </React.Fragment>
      );
    });
  }, [showFavoritesOnly, favorites, user.permissions, location.pathname, expanded, toggleSubmenu, handleSubmenuClick, toggleFavorite, t, i18n, currentColors]);

  return (
    <Box sx={{
      overflowY: 'scroll',
      height: '1',
      position: 'relative',
      scrollbarWidth: 'none',
      '&::-webkit-scrollbar': {
        display: 'none',
      },
    }}>
      {isDrawerOpen && (
        <Box sx={{ padding: 2, textAlign: 'center' }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              // flexDirection: i18n.language === 'ar' ? 'row-reverse' : 'row',
              gap: 2,
              justifyContent: 'center',
            }}
          >
            <Button
              onClick={handleShowAll}
              variant={!showFavoritesOnly ? '' : 'outlined'}
              startIcon={<VisibilityIcon />}
              sx={{
                backgroundColor: !showFavoritesOnly ? currentColors.secondary : 'transparent',
                color: (theme) =>
                  theme.palette.mode === 'dark'
                    ? 'white'
                    : !showFavoritesOnly
                      ? 'white'
                      : currentColors.secondary,
                borderColor: currentColors.secondary,
                transition: 'all 0.3s ease-in-out',
                display: 'flex',
                alignItems: 'center',
                gap: i18n.language === 'ar' ? 1 : 0,
                fontFamily: theme.fontPrimary,
                fontWeight: 'bold',
                '&:hover': {
                  backgroundColor: currentColors.primary,
                  color: 'white',
                },
              }}
            >
              {t('LC_000070')}
            </Button>
            <Button
              onClick={handleShowFavorites}
              variant={showFavoritesOnly ? '' : 'outlined'}
              startIcon={<StarIcon />}
              sx={{
                backgroundColor: showFavoritesOnly ? currentColors.secondary : 'transparent',
                color: (theme) => (theme.palette.mode === 'dark' ? 'white' : showFavoritesOnly ? 'white' : currentColors.secondary),
                borderColor: currentColors.secondary,
                transition: 'all 0.3s ease-in-out',
                display: 'flex',
                alignItems: 'center',
                fontFamily: theme.fontPrimary,
                gap: i18n.language === 'ar' ? 1 : 0,
                fontWeight: 'bold',
                '&:hover': {
                  backgroundColor: currentColors.primary,
                  color: 'white',
                },
              }}
            >
              {t('LC_000069')}
            </Button>
          </Box>
        </Box>
      )}
      <Divider />

      <List
        subheader={
          <ListSubheader
            component="div"
            sx={{
              [i18n.dir() === 'rtl' ? 'marginRight' : 'marginLeft']: -1.3,
              fontSize: 10,
              fontFamily: theme.fontPrimary,
              fontWeight: 'bold',
              color: (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.primary),
            }}
          >
            {t('LC_000013')}
          </ListSubheader>
        }
      >
        <ListItemButton
          sx={{
            [i18n.dir() === 'rtl' ? 'marginRight' : 'marginLeft']: -1,
            textAlign: i18n.dir() === 'rtl' ? 'right' : 'left',
          }}
          component={Link} to="/dashboard"
        >
          <ListItemIcon
            sx={{
              [i18n.dir() === 'rtl' ? 'marginLeft' : 'marginRight']: 3,
              color: (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.secondary),
            }}
          >
            <DashboardIcon />
          </ListItemIcon>
          <ListItemText primary={t('LC_000012')} sx={{ color: (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.secondary) }} primaryTypographyProps={{ fontFamily: theme.fontPrimary, fontWeight: 'bold !important' }} />
        </ListItemButton>
        <ListItemButton
          sx={{
            [i18n.dir() === 'rtl' ? 'marginRight' : 'marginLeft']: -1,
            textAlign: i18n.dir() === 'rtl' ? 'right' : 'left',
          }}
          component={Link} to="/supportsystem"
        >
          <ListItemIcon
            sx={{
              [i18n.dir() === 'rtl' ? 'marginLeft' : 'marginRight']: 3,
              color: (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.secondary)
            }}
          >
            <SupportIcon />
          </ListItemIcon>
          <ListItemText primary={t('0002')} sx={{ color: (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.secondary) }} primaryTypographyProps={{ fontFamily: theme.fontPrimary, fontWeight: 'bold !important' }} />
        </ListItemButton>
      </List>
      <Divider />
      <List
        subheader={
          <ListSubheader
            component="div"
            sx={{
              marginLeft: -1.2,
              fontSize: 11,
              fontFamily: theme.fontPrimary,
              color: (theme) => (theme.palette.mode === 'dark' ? 'white' : currentColors.primary),
              fontWeight: 'bold',
            }}
          >
            {t('LC_000014')}
          </ListSubheader>
        }
      >
        {renderMenuItems(filteredItems)}
      </List>
    </Box>
  );
});

export default MenuContent;


