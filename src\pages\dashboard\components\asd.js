import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Box, Divider, List, ListItemButton, ListItemIcon, ListItemText, ListSubheader, Collapse } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import DashboardIcon from '@mui/icons-material/Dashboard';
import DescriptionIcon from '@mui/icons-material/Description';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import SupportIcon from '@mui/icons-material/Support';
import TapAndPlayIcon from '@mui/icons-material/TapAndPlay';
import EditNoteIcon from '@mui/icons-material/EditNote';
import GavelIcon from '@mui/icons-material/Gavel';
import ArchiveIcon from '@mui/icons-material/Archive';
import BusinessIcon from '@mui/icons-material/Business';
import EngineeringIcon from '@mui/icons-material/Engineering';
import HistoryEduIcon from '@mui/icons-material/HistoryEdu';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import SelectAllIcon from '@mui/icons-material/SelectAll';
import SolarPowerIcon from '@mui/icons-material/SolarPower';
import MiscellaneousServicesIcon from '@mui/icons-material/MiscellaneousServices';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import LocalPoliceIcon from '@mui/icons-material/LocalPolice';
import CarRentalIcon from '@mui/icons-material/CarRental';
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircle';
import IsoIcon from '@mui/icons-material/Iso';
import BalanceIcon from '@mui/icons-material/Balance';
import ApartmentIcon from '@mui/icons-material/Apartment';
import { useAuth } from '../../../AuthProvider';
import routesConfig from '../../../routesConfig';
import ConstructionIcon from '@mui/icons-material/Construction';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import Colors from '../../../Common/Colors';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';


const ICONS = {
    CallCenter: <SupportAgentIcon />,
    ComplaintsSystems: <EditNoteIcon />,
    DamageSystems: <ConstructionIcon />,
    BasicSystem: <ViewModuleIcon />,
    AlShamelSystem: <SelectAllIcon />,
    BIDSystem: <GavelIcon />,
    BranchesPlan: <ApartmentIcon />,
    DocumentArchiving: <ArchiveIcon />,
    DaughterCompany: <BusinessIcon />,
    ExamSystem: <HistoryEduIcon />,
    FixSystem: <EngineeringIcon />,
    HHUSystem: <TapAndPlayIcon />,
    ISOSystem: <IsoIcon />,
    JudiciarySystem: <BalanceIcon />,
    MiscellaneousManagementSystem: <MiscellaneousServicesIcon />,
    ProjectManagementSystem: <AccountTreeIcon />,
    SolarSystem: <SolarPowerIcon />,
    ElectriciansLicensing: <LocalPoliceIcon />,
    RentSystem: <CarRentalIcon />,
    ShareHolders: <SupervisedUserCircleIcon />,
  };

function DotIcon({ color }) {
  return (
    <Box sx={{ marginRight: 1, display: 'flex', alignItems: 'center' }}>
      <svg width={6} height={6}>
        <circle cx={3} cy={3} r={3} fill={color} />
      </svg>
    </Box>
  );
}

DotIcon.propTypes = {
  color: PropTypes.string.isRequired,
};

function MenuContent({ isDrawerOpen, onOpenDrawer }) {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [expanded, setExpanded] = useState(JSON.parse(localStorage.getItem('expandedMenus')) || []);
  const savedPermissions = useMemo(() => new Set(user.permissions), [user.permissions]); // Cache permissions

  useEffect(() => {
    localStorage.setItem('expandedMenus', JSON.stringify(expanded));
  }, [expanded]);

  useEffect(() => {
    if (!isDrawerOpen) 
        setExpanded([]);
  }, [isDrawerOpen]);

  useEffect(() => {
    const activeMenu = location.pathname;
    setExpanded([activeMenu]);
  }, [location]);

  const toggleSubmenu = (menuId, isSubmenu = false) => {
    if (!isDrawerOpen) onOpenDrawer();

    setExpanded((prev) => {
      const newState = prev.includes(menuId) 
        ? prev.filter(id => id !== menuId) 
        : [...prev, menuId];

      localStorage.setItem('expandedMenus', JSON.stringify(newState));
      return newState;
    });
  };

  const handleSubmenuClick = (id, path) => {
    if (path) navigate(path, { state: { expandedMenus: expanded, id: id } });
  };

  const renderMenuItems = (items) => {
    return items.map((item) => {
      if (!savedPermissions.has(item.id)) return null;
      const isActive = location.pathname === item.path;
      const hasSubSystems = item.subSystems?.length > 0;
      const hasSubMenus = item.subMenus?.length > 0;
      const hasSubObjects = item.subObjects?.length > 0;

      return (
        <React.Fragment key={item.id}>
          <ListItemButton
            sx={{
              [i18n.dir() === 'rtl' ? 'marginRight' : 'marginLeft']: -1,
              textAlign: i18n.dir() === 'rtl' ? 'right' : 'left',
              bgcolor: isActive ? 'primary.main' : 'inherit',
              '&:hover': { bgcolor: 'primary.light' },
            }}
            selected={isActive}
            onClick={() => {
              if (hasSubSystems || hasSubMenus || hasSubObjects) {
                toggleSubmenu(item.id);
              } else if (item.path) {
                handleSubmenuClick(item.id, item.path);
              }
            }}
          >
            <ListItemIcon
              sx={{
                [i18n.dir() === 'rtl' ? 'marginLeft' : 'marginRight']: 3,
                color: isActive ? 'primary.contrastText' : Colors.secondary,
              }}
            >
              {ICONS[item.systemIcon] || <DotIcon color={isActive ? Colors.primary : Colors.secondary} />}
            </ListItemIcon>
            <ListItemText
              primary={t(item.id)}
              sx={{
                color: isActive ? Colors.primary : Colors.secondary,
              }}
            />
            {(hasSubSystems || hasSubMenus || hasSubObjects) && (
              expanded.includes(item.id) ? <ExpandLess /> : <ExpandMore />
            )}
          </ListItemButton>

          {(hasSubSystems || hasSubMenus || hasSubObjects) && (
            <Collapse in={expanded.includes(item.id)} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {hasSubSystems && renderMenuItems(item.subSystems)}
                {hasSubMenus && renderMenuItems(item.subMenus)}
                {hasSubObjects && renderMenuItems(item.subObjects)}
              </List>
            </Collapse>
          )}
        </React.Fragment>
      );
    });
  };

  return (
    <Box sx={{ overflowY: 'scroll', height: '100vh', position: 'relative' }}>
      <List subheader={<ListSubheader>{t('LC_000013')}</ListSubheader>}>
        <ListItemButton component={Link} to="/dashboard">
          <ListItemIcon><DashboardIcon /></ListItemIcon>
          <ListItemText primary={t('LC_000012')} />
        </ListItemButton>
        <ListItemButton component={Link} to="/supportsystem">
          <ListItemIcon><SupportIcon /></ListItemIcon>
          <ListItemText primary={t('0002')} />
        </ListItemButton>
      </List>
      <Divider />
      <List subheader={<ListSubheader>{t('LC_000014')}</ListSubheader>}>
        {renderMenuItems(routesConfig)}
      </List>
    </Box>
  );
}

export default MenuContent;
