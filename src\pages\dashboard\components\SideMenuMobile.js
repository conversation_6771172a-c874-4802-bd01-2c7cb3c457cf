import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import Drawer, { drawerClasses } from "@mui/material/Drawer";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import LogoutRoundedIcon from "@mui/icons-material/LogoutRounded";
import NotificationsRoundedIcon from "@mui/icons-material/NotificationsRounded";

import MenuButton from "./MenuButton";
import MenuContent from "./MenuContent";
import CardAlert from "./CardAlert";

import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import AutoAwesomeRoundedIcon from "@mui/icons-material/AutoAwesomeRounded";
import MenuButtonNotifications from "./MenuButtonNotifications";

import Colors from "../../../Common/Colors";
import { useAuth } from "../../../AuthProvider";
import { Box } from "@mui/material";

import Cookies from "js-cookie";
import { hostURL } from "../../../Common/APIs";
import { useNavigate } from "react-router-dom";

function SideMenuMobile({ open, toggleDrawer, currentLanguage }) {
  const [expanded, setExpanded] = useState([]);
  const drawerAnchor = currentLanguage === "ar" ? "right" : "left";
  const { user } = useAuth();
  const userNameEn = Cookies.get("userNameEn")
    ? JSON.parse(Cookies.get("userNameEn"))
    : null;
  const userCode = Cookies.get("userCode")
    ? JSON.parse(Cookies.get("userCode"))
    : null;
  const userNameAr = Cookies.get("userNameAr")
    ? JSON.parse(Cookies.get("userNameAr"))
    : null;
  const compCode = Cookies.get("compCode")
    ? JSON.parse(Cookies.get("compCode"))
    : null;
  const empID = Cookies.get("empID") ? JSON.parse(Cookies.get("empID")) : null;
  const email = Cookies.get("email") ? JSON.parse(Cookies.get("email")) : null;
  const phoneNo = Cookies.get("phoneNo")
    ? JSON.parse(Cookies.get("phoneNo"))
    : null;
  const currentColors = Colors[compCode] || Colors.default;
  const navigate = useNavigate();

  const handleOpenDrawer = () => {
    toggleDrawer(!open);
  };
  const handleLogout = async () => {
    try {
      const token = Cookies.get("authToken");

      if (token) {
        const response = await fetch(`${hostURL}/doLogout`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          console.log("Token invalidated successfully.");
        } else {
          console.error("Failed to invalidate token:", await response.json());
        }
      }

      Cookies.remove("authToken");
      // sessionStorage.removeItem('userData');
      // setUser(null);
      navigate("/login");
      // window.location.reload();
    } catch (error) {
      console.error("An error occurred during logout:", error);
      Cookies.remove("authToken");

      navigate("/login");
      // window.location.reload();
    }
  };

  return (
    <Drawer
      anchor={drawerAnchor}
      open={open}
      onClose={toggleDrawer(false)}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        [`& .${drawerClasses.paper}`]: {
          backgroundImage: "none",
          backgroundColor: "background.paper",
        },
      }}
    >
      <Stack
        sx={{
          width: "75dvw",
          height: "100%",
        }}
      >
        <Stack direction="row" sx={{ p: 1, pb: 0, gap: 1 }}>
          <Stack
            direction="row"
            sx={{ gap: 1, alignItems: "center", flexGrow: 1, p: 1 }}
          >
            <Avatar
              alt={userNameEn}
              src="/amr.jpg"
              sx={{
                width: 42,
                height: 42,
                border: (theme) =>
                  `2px solid ${
                    theme.palette.mode === "dark"
                      ? "#fff"
                      : currentColors.secondary
                  }`,
              }}
            />
            <Box>
              <Typography
                variant="body2"
                sx={{ fontWeight: 500, fontFamily: "Segoe UI Symbol" }}
              >
                {drawerAnchor === "right" ? userNameAr : userNameEn}
                {/* {t('LC_000015')} */}
              </Typography>
              <Typography
                variant="caption"
                sx={{ color: "text.secondary", fontFamily: "Segoe UI Symbol" }}
              >
                {email} | {empID}
                {/* <EMAIL> */}
              </Typography>
            </Box>
          </Stack>
          {/* <MenuButtonNotifications showBadge>
            <NotificationsRoundedIcon sx={{ color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary }} />
          </MenuButtonNotifications> */}
        </Stack>
        <Divider />
        <Stack sx={{ flexGrow: 1 }}>
          <MenuContent
            isDrawerOpen={open}
            onOpenDrawer={handleOpenDrawer}
            expanded={expanded}
            setExpanded={setExpanded}
          />
          <Divider />
        </Stack>
        {/* <CardAlert /> */}
        {/* <Card variant="outlined" sx={{ m: 1.5, p: 1.5, pb: 15 }}>
          <CardContent>
            <img
              src="./images/STDDP_logo.png"
              height="100px"
              width="100%" 
              alt={"STDDP"}
              style={{ objectFit: 'contain' }} 
            />

          </CardContent>
        </Card> */}
        <Stack sx={{ p: 2 }}>
          <Button
            variant="outlined"
            onClick={handleLogout}
            sx={{
              color: (theme) =>
                theme.palette.mode === "dark"
                  ? "white"
                  : currentColors.secondary,
            }}
            fullWidth
            startIcon={<LogoutRoundedIcon />}
          >
            Logout
          </Button>
        </Stack>
      </Stack>
    </Drawer>
  );
}

SideMenuMobile.propTypes = {
  open: PropTypes.bool,
  toggleDrawer: PropTypes.func.isRequired,
  currentLanguage: PropTypes.string.isRequired,
};

export default SideMenuMobile;
