import axios from 'axios';
import fs from 'fs';
import { hostURL } from '../Common/APIs.js';

axios.post(`${hostURL}/getTranslationByLanguage`, {
  lang: 'AR'
})
  .then(response => {
    const apiResponse = response.data;
    console.log('API Response:', apiResponse); 

    if (apiResponse.resultCode === "0" && apiResponse.data) {
      const translations = apiResponse.data;

      fs.writeFileSync('ar.json', JSON.stringify(translations, null, 2));
      console.log('Data received from API and saved to en.json file successfully!');
    } else {
      console.error('Error: Unexpected response structure or resultCode is not 0');
    }
  })
  .catch(error => {
    console.error('Error:', error);
  });
