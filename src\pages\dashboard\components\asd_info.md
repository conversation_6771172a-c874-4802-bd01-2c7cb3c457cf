# شرح ملف asd.js - مكون قائمة التنقل الجانبية

## نظرة عامة
هذا الملف يحتوي على مكون React يسمى `MenuContent` والذي يمثل قائمة التنقل الجانبية (Sidebar Navigation) في لوحة التحكم. المكون يعرض قائمة هرمية من الأنظمة والوحدات المختلفة التي يمكن للمستخدم الوصول إليها.

## المكونات الرئيسية

### 1. الاستيرادات (Imports)
- **React Hooks**: `useState`, `useEffect`, `useMemo` لإدارة الحالة والتأثيرات الجانبية
- **React Router**: `Link`, `useLocation`, `useNavigate` للتنقل بين الصفحات
- **Material-UI Components**: مكونات واجهة المستخدم مثل `Box`, `List`, `ListItemButton`, إلخ
- **Material-UI Icons**: مجموعة كبيرة من الأيقونات المختلفة لكل نظام
- **Custom Hooks**: `useAuth` للمصادقة و `useTranslation` للترجمة

### 2. كائن الأيقونات (ICONS Object)
```javascript
const ICONS = {
    CallCenter: <SupportAgentIcon />,
    ComplaintsSystems: <EditNoteIcon />,
    DamageSystems: <ConstructionIcon />,
    // ... المزيد من الأيقونات
};
```
يربط كل نظام بأيقونة محددة لعرضها في القائمة.

### 3. مكون DotIcon
مكون بسيط يعرض نقطة ملونة كأيقونة افتراضية عندما لا تتوفر أيقونة محددة للنظام.

### 4. المكون الرئيسي MenuContent

#### المتغيرات والحالات (State Variables)
- `expanded`: مصفوفة تحتوي على معرفات القوائم المفتوحة
- `savedPermissions`: مجموعة صلاحيات المستخدم المحفوظة في الذاكرة
- `location`: الموقع الحالي في التطبيق
- `user`: بيانات المستخدم المسجل

#### الوظائف الرئيسية

##### `toggleSubmenu(menuId, isSubmenu)`
- تتحكم في فتح وإغلاق القوائم الفرعية
- تحفظ حالة القوائم المفتوحة في localStorage
- تفتح الدرج الجانبي إذا كان مغلقاً

##### `handleSubmenuClick(id, path)`
- تتعامل مع النقر على عناصر القائمة الفرعية
- تنقل المستخدم إلى الصفحة المحددة مع حفظ حالة القوائم المفتوحة

##### `renderMenuItems(items)`
هذه الوظيفة الأساسية التي تعرض عناصر القائمة:

1. **فحص الصلاحيات**: تتحقق من أن المستخدم لديه صلاحية للوصول للنظام
2. **تحديد الحالة النشطة**: تحدد إذا كان العنصر هو الصفحة الحالية
3. **فحص القوائم الفرعية**: تتحقق من وجود أنظمة فرعية أو قوائم فرعية
4. **عرض العنصر**: تعرض العنصر مع الأيقونة والنص المترجم
5. **عرض القوائم الفرعية**: تعرض القوائم الفرعية إذا كانت مفتوحة

## الميزات المتقدمة

### 1. دعم اللغات المتعددة (i18n)
- يدعم الاتجاه من اليمين إلى اليسار (RTL) والعكس
- يترجم النصوص باستخدام `useTranslation`

### 2. إدارة الصلاحيات
- يعرض فقط الأنظمة التي لدى المستخدم صلاحية للوصول إليها
- يستخدم `useMemo` لتحسين الأداء

### 3. حفظ الحالة
- يحفظ حالة القوائم المفتوحة في localStorage
- يستعيد الحالة عند إعادة تحميل الصفحة

### 4. التنقل الذكي
- يفتح القائمة المناسبة تلقائياً بناءً على الصفحة الحالية
- يحافظ على حالة القوائم أثناء التنقل

## هيكل القائمة النهائية

القائمة تتكون من قسمين رئيسيين:

### القسم الأول: الروابط الأساسية
- لوحة التحكم (Dashboard)
- نظام الدعم (Support System)

### القسم الثاني: الأنظمة المختلفة
يتم عرضها من `routesConfig` وتشمل أنظمة مثل:
- نظام الشكاوى
- نظام الأضرار  
- النظام الأساسي
- نظام الشامل
- نظام المناقصات
- وغيرها من الأنظمة

## التصميم والأسلوب
- يستخدم Material-UI للتصميم
- يدعم الثيمات (Themes)
- يتكيف مع حجم الشاشة
- يوفر تجربة مستخدم سلسة مع الانتقالات المتحركة
