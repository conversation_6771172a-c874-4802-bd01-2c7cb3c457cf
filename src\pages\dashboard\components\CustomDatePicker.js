import * as React from 'react';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import Button from '@mui/material/Button';
import CalendarTodayRoundedIcon from '@mui/icons-material/CalendarTodayRounded';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import Colors from '../../../Common/Colors';
// import { useAuth } from '../../../AuthProvider';

import { useTranslation } from 'react-i18next';
import Cookies from 'js-cookie';


function ButtonField(props) {
  const { i18n } = useTranslation();
  // const { user } = useAuth();

  const {
    // setOpen,
    label,
    id,
    disabled,
    InputProps: { ref } = {},
    inputProps: { 'aria-label': ariaLabel } = {},
  } = props;

  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;

  return (
    <Button
      variant="outlined"
      id={id}
      disabled={disabled}
      ref={ref}
      aria-label={ariaLabel}
      size="small"
      // onClick={() => setOpen?.((prev) => !prev)}
      startIcon={
        <CalendarTodayRoundedIcon
          fontSize="small"
          sx={{
            marginLeft: i18n.dir() === 'rtl' ? 1 : 0,
            // marginRight: i18n.dir() === 'rtl' ? 0 : 1 
          }}
        />
      }
      sx={{
        minWidth: 'fit-content',
        color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary
      }}
    >
      {label ? `${label}` : 'Pick a date'}
    </Button>

    //   <Button
    //     variant="outlined"
    //     id={id}
    //     disabled={disabled}
    //     ref={ref}
    //     aria-label={ariaLabel}
    //     size="small"
    //     // onClick={() => setOpen?.((prev) => !prev)}
    //     startIcon={<CalendarTodayRoundedIcon fontSize="small" />}
    //     sx={{ minWidth: 'fit-content', color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary }}
    //   >
    //     {label ? `${label}` : 'Pick a date'}
    //   </Button>
  );
}

ButtonField.propTypes = {
  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled: PropTypes.bool,
  id: PropTypes.string,
  inputProps: PropTypes.shape({
    'aria-label': PropTypes.string,
  }),
  InputProps: PropTypes.shape({
    endAdornment: PropTypes.node,
    startAdornment: PropTypes.node,
  }),
  label: PropTypes.node,
  setOpen: PropTypes.func,
};

export default function CustomDatePicker() {
  const [value, setValue] = React.useState(dayjs()); // dayjs('2024-12-01') // dayjs().startOf('day')
  const [open, setOpen] = React.useState(false);
  // const { t, i18n } = useTranslation();
  // const { user } = useAuth();
  // const currentColors = Colors[user.compCode] || Colors.default;
// console.log();

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        value={value}
        label={value == null ? null : value.format('DD / MM / YYYY')}
        onChange={(newValue) => setValue(newValue)}
        slots={{ field: ButtonField }}
        slotProps={{
          field: { setOpen },
          nextIconButton: { size: 'small' },
          previousIconButton: { size: 'small' },
        }}
        open={open}
        onClose={() => setOpen(false)}
        onOpen={() => setOpen(true)}
        views={['day', 'month', 'year']}
      />
    </LocalizationProvider>
  );
}
