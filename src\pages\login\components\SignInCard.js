import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import MuiCard from '@mui/material/Card';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../AuthProvider';
import { hostURL } from '../../../Common/APIs';
import Cookies from 'js-cookie';
import { useTranslation } from 'react-i18next';
import { CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import Colors from '../../../Common/Colors';

const Card = styled(MuiCard)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignSelf: 'center',
  width: '100%',
  padding: theme.spacing(4),
  gap: theme.spacing(2),
  boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.05)',
  [theme.breakpoints.up('sm')]: { width: '450px' },
  [theme.breakpoints.down('sm')]: { width: '100vw', height: '100vh', borderRadius: 0 },
}));

export default function SignInCard() {
  const [loading, setLoading] = useState(false);
  const [CompCode, setCompCode] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [otp, setOtp] = useState('');
  const [sessionToken, setSessionToken] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [otpError, setOtpError] = useState('');

  const navigate = useNavigate();
  const { setUser } = useAuth();
  const { t, i18n } = useTranslation();

  useEffect(() => {
    const token = Cookies.get('authToken');
    if (token) navigate('/dashboard');
  }, [navigate]);

  useEffect(() => {
    const storedLanguage = sessionStorage.getItem('language') || 'ar';
    i18n.changeLanguage(storedLanguage);
    document.documentElement.setAttribute('dir', storedLanguage === 'ar' ? 'rtl' : 'ltr');
  }, [i18n]);

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!CompCode || !email || !password) {
      setErrorMessage('Please fill all required fields.');
      return;
    }

    setLoading(true);

    const requestBody = {
      thirdPartyAppId: 'STDDP-WEB',
      companyShortName: CompCode,
      userName: email,
      password,
      lang: sessionStorage.getItem('language') || 'ar',
      twoFactorAuthenticationToken: null,
      verificationCode: null,
      resendVerificationCode: "N"
    };

    try {
      const response = await axios.post(`${hostURL}/doLogin`, requestBody);
      const responseData = response.data;

      if (responseData.resultCode === '0') {
        const { enableTwoFactorAuthentication, twoFactorAuthenticationToken } = responseData.data;

        if (enableTwoFactorAuthentication === "Y") {
          setSessionToken(twoFactorAuthenticationToken);
          setOpenDialog(true);
        } else {
          completeLogin(responseData.data);
        }
      } else {
        const lang = i18n.language || 'en';
        const message = lang === 'ar' ? responseData.resultMessageA : responseData.resultMessageE;
        setErrorMessage(message || 'Login failed. Please try again.');
      //setErrorMessage(responseData.resultMessageE || 'Login failed. Please try again.');
        
      }
    } catch (error) {
      console.error('Error during login:', error);
      setErrorMessage('An error occurred while processing your request. Please try again later.');
    } finally {
      setLoading(false);
    }
  };



  const handleOtpSubmit = async () => {
    if (!otp) {
      setOtpError('Please enter the Code.');
      return;
    }

    setLoading(true);

    try {
      const response = await axios.post(`${hostURL}/doLogin`, {
        thirdPartyAppId: 'STDDP-WEB',
        companyShortName: CompCode,
        userName: email,
        password,
        lang: sessionStorage.getItem('language'),
        twoFactorAuthenticationToken: sessionToken,
        verificationCode: otp,
        resendVerificationCode: "N"
      });

      const responseData = response.data;

      if (responseData.resultCode === '0') {
        console.log("asd");
        setOpenDialog(false);
        completeLogin(responseData.data);
      } else {
        setOtpError(responseData.resultMessageE || 'Invalid Code. Please try again.');
      }
    } catch (error) {
      console.error('Error verifying Code:', error);
      setOtpError('An error occurred while verifying Code.');
    } finally {
      setLoading(false);
    }
  };


  const completeLogin = (userData) => {
    const { accessToken, userCode, userEmail, mobileNo, companyInfo, userEmployeeId, userNameE, userNameA, accessTokenExpiryInSeconds, lastLoginTimeStamp, intExtNo, profilePic } = userData;
    Cookies.set('authToken', accessToken, { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('phoneNo', JSON.stringify(mobileNo), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('email', JSON.stringify(userEmail), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('compCode', JSON.stringify(companyInfo.companyCode), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('userCode', JSON.stringify(userCode), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('empID', JSON.stringify(userEmployeeId), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('userNameAr', JSON.stringify(userNameA), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('userNameEn', JSON.stringify(userNameE), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('accessTokenExpiryInSeconds', accessTokenExpiryInSeconds, { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('lastLoginTimeStamp', lastLoginTimeStamp, { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('exNo', JSON.stringify(intExtNo), { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });
    Cookies.set('profilePic', profilePic, { expires: (accessTokenExpiryInSeconds / 86400), secure: false, sameSite: 'Strict' });

    setUser(accessToken);
    navigate('/dashboard');
  };

  return (
    <Card variant="outlined">
      <img src="images/STDDP_logo.png" alt="Logo" style={{ width: '90%', maxWidth: '180px', marginBottom: '16px', alignSelf: 'center' }} />
      <Typography component="h1" variant="h5">{t('LC_000078')}</Typography>
      <Box component="form" onSubmit={handleSubmit} noValidate sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <FormControl><FormLabel htmlFor="CompCode">{t('LC_000072')}</FormLabel><TextField id="CompCode" placeholder={t('LC_000072')} value={CompCode} onChange={(e) => setCompCode(e.target.value)} required /></FormControl>
        <FormControl><FormLabel htmlFor="email">{t('LC_000074')}</FormLabel><TextField id="email" placeholder={t('LC_000121')} type="email" value={email} onChange={(e) => setEmail(e.target.value)} required /></FormControl>
        <FormControl><FormLabel htmlFor="password">{t('LC_000073')}</FormLabel><TextField id="password" type="password" placeholder="••••••" value={password} onChange={(e) => setPassword(e.target.value)} required /></FormControl>

        {errorMessage && <Typography color="error">{errorMessage}</Typography>}

        <Button type="submit" fullWidth variant="contained" disabled={loading}>
          {loading ? <CircularProgress size="30px" /> : t('LC_000078')}
        </Button>

        {sessionToken && !openDialog && (
          <Button
            variant=""
            color={Colors.default.secondary}
            onClick={() => setOpenDialog(true)}
          >
            {t('LC_000084')}
          </Button>
        )}

        <Typography textAlign="center"><Link href="/ForgotPassword">{t('LC_000077')}</Link></Typography>
      </Box>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>{t('LC_000084')}</DialogTitle>
        <DialogContent>
          <TextField
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            sx={{ m: 1 }}
            error={Boolean(otpError)}
            helperText={otpError}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>{t('LC_000087')}</Button>
          <Button onClick={handleOtpSubmit} variant="contained">{t('LC_000088')}</Button>
        </DialogActions>
      </Dialog>


    </Card>
  );
}









// import React, { useState, useEffect } from 'react';
// import axios from 'axios';
// import Box from '@mui/material/Box';
// import Button from '@mui/material/Button';
// import MuiCard from '@mui/material/Card';
// import FormLabel from '@mui/material/FormLabel';
// import FormControl from '@mui/material/FormControl';
// import TextField from '@mui/material/TextField';
// import Typography from '@mui/material/Typography';
// import Link from '@mui/material/Link';
// import { styled } from '@mui/material/styles';
// import { useNavigate } from 'react-router-dom';
// import { useAuth } from '../../../AuthProvider';
// import { hostURL } from '../../../Common/APIs';
// import Cookies from 'js-cookie';
// import { useTranslation } from 'react-i18next';
// import { CircularProgress } from '@mui/material';

// const Card = styled(MuiCard)(({ theme }) => ({
//   display: 'flex',
//   flexDirection: 'column',
//   alignSelf: 'center',
//   width: '100%',
//   padding: theme.spacing(4),
//   gap: theme.spacing(2),
//   boxShadow:
//     'hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px',
//   [theme.breakpoints.up('sm')]: {
//     width: '450px',
//   },
//   [theme.breakpoints.down('sm')]: {
//     width: '100vw',
//     height: '100vh',
//     borderRadius: 0,
//   },
// }));

// export default function SignInCard() {
//   const [loading, setLoading] = useState(false);
//   const [CompCode, setCompCode] = useState('');
//   const [email, setEmail] = useState('');
//   const [password, setPassword] = useState('');
//   const [errorMessage, setErrorMessage] = useState('');
//   const navigate = useNavigate();
//   const { setUser } = useAuth();
//   const { t, i18n } = useTranslation();

//   useEffect(() => {
//     const token = Cookies.get('authToken');
//    // console.log("authToken");
//     if (token) {
//       //console.log("authToken done");
//       navigate('/dashboard');
//     }
//   }, [navigate]);


//   useEffect(() => {
//     const storedLanguage = sessionStorage.getItem('language') || 'ar';
//     i18n.changeLanguage(storedLanguage);
//     document.documentElement.setAttribute('dir', storedLanguage === 'ar' ? 'rtl' : 'ltr');
//   }, [i18n]);

//   const handleSubmit = async (event) => {
//     event.preventDefault();

//     if (!CompCode || !email || !password) {
//       setErrorMessage('Please fill all required fields.');
//       return;
//     }

//     setLoading(true);

//     const requestBody = {
//       companyShortName: CompCode,
//       thirdPartyAppId: 'STDDP-WEB',
//       userName: email,
//       password,
//       lang: sessionStorage.getItem('language'),
//     };

//     try {
//       const response = await axios.post(${hostURL}/doLogin, requestBody);
//       const responseData = response.data;

//       if (responseData.resultCode === '0') {
//         const { accessToken, userCode, userNameE, userNameA, userEmail, userEmployeeId, exNo, mobileNo, companyInfo } = responseData.data;
//         //const hours = 7;
//         // Cookies.set('authToken', accessToken, { expires: (hours/24), sameSite: 'Strict' });
//         Cookies.set('authToken', accessToken, { expires: 0.3, secure: false, sameSite: 'Strict' }); // secure: true,
//         // console.log("ٍِيشسيسشي: " + document.cookie);

//         const fetchUserData = async () => {
//           try {
//             const userResponse = await axios.get(${hostURL}/getAuthProviderByUserCode, {
//               headers: { Authorization: Bearer ${accessToken} },
//             });

//             if (userResponse.data.resultCode === '0') {
//               const { permissions, favorites } = userResponse.data.data;

//               const userData = {
//                 token: accessToken,
//                 userCode,
//                 permissions,
//                 favorites,
//                 userNameAr: userNameA,
//                 userNameEn: userNameE,
//                 email: userEmail,
//                 empID: userEmployeeId,
//                 exNo,
//                 phoneNo: mobileNo,
//                 compCode: companyInfo.companyCode,
//               };

//               setUser(userData.token);
//               //console.log("sdsadsadsad 1223123213123: " + userData);
//               // sessionStorage.setItem('userData', JSON.stringify(userData));
//               Cookies.set('phoneNo', JSON.stringify(userData.phoneNo), { expires: 0.3, secure: false, sameSite: 'Strict' }); // secure: true
//               Cookies.set('email', JSON.stringify(userData.email), { expires: 0.3, secure: false, sameSite: 'Strict' }); // secure: true
//               Cookies.set('compCode', JSON.stringify(userData.compCode), { expires: 0.3, secure: false, sameSite: 'Strict' });// secure: true
//               Cookies.set('empID', JSON.stringify(userData.empID), { expires: 0.3, secure: false, sameSite: 'Strict' }); // secure: true
//               Cookies.set('userNameAr', JSON.stringify(userData.userNameAr), { expires: 0.3, secure: false, sameSite: 'Strict' }); // secure: true
//               Cookies.set('userNameEn', JSON.stringify(userData.userNameEn), { expires: 0.3, secure: false, sameSite: 'Strict' }); // secure: true
//               Cookies.set('userCode', JSON.stringify(userData.userCode), { expires: 0.3, secure: false, sameSite: 'Strict' }); // secure: true


//               navigate('/dashboard');
//             } else {
//               setErrorMessage(userResponse.data.resultMessageE || 'Login failed. Please try again.');
//             }
//           } catch (error) {
//             console.error('Error fetching user data:', error);
//             setErrorMessage('Error fetching user data.');
//           }
//         };

//         await fetchUserData();
//       } else {
//         setErrorMessage(responseData.resultMessageE || 'Login failed. Please try again.');
//       }
//     } catch (error) {
//       console.error('Error during login:', error);
//       setErrorMessage('An error occurred while processing your request. Please try again later.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <Card variant="outlined">
//       <img
//         src="images/STDDP_logo.png"
//         alt="Logo"
//         style={{ width: '90%', maxWidth: '180px', marginBottom: '16px', alignSelf: 'center' }}
//       />
//       <Typography component="h1" variant="h5" sx={{ fontSize: 'clamp(2rem, 10vw, 2.15rem)' }}>
//         {t('LC_000078')}
//       </Typography>
//       <Box
//         component="form"
//         onSubmit={handleSubmit}
//         noValidate
//         sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
//       >
//         <FormControl>
//           <FormLabel htmlFor="CompCode">{t('LC_000072')}</FormLabel>
//           <TextField
//             id="CompCode"
//             placeholder={t('LC_000072')}
//             value={CompCode}
//             onChange={(e) => setCompCode(e.target.value)}
//             variant="outlined"
//             required
//           />
//         </FormControl>
//         <FormControl>
//           <FormLabel htmlFor="email">{t('LC_000074')}</FormLabel>
//           <TextField
//             id="email"
//             type="email"
//             placeholder={t('LC_000075')}
//             value={email}
//             onChange={(e) => setEmail(e.target.value)}
//             required
//           />
//         </FormControl>
//         <FormControl>
//           <FormLabel htmlFor="password">{t('LC_000073')}</FormLabel>
//           <TextField
//             id="password"
//             type="password"
//             placeholder="••••••"
//             value={password}
//             onChange={(e) => setPassword(e.target.value)}
//             required
//           />
//         </FormControl>

//         {errorMessage && (
//           <Typography color="error" sx={{ fontSize: '0.9rem' }}>
//             {errorMessage}
//           </Typography>
//         )}

//         <Button type="submit" fullWidth variant="contained" disabled={loading}>
//           {loading ? <CircularProgress size="30px" /> : t('LC_000078')}
//           {/* {loading ? t('LC_000089') : t('LC_000078')} */}
//         </Button>

//         <Typography textAlign="center">
//           <Link href="/ForgotPassword" variant="body2">
//             {t('LC_000077')}
//           </Link>
//         </Typography>
//       </Box>
//     </Card>
//   );
// }