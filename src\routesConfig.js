const routesConfig = [
  {
    id: "00",
    systemIcon: "BasicSystem",
    subSystems: [
      {
        id: "0001",
        subMenus: [
          {
            id: "000101",
            subObjects: [
              {
                id: "********",
                path: "/BasicSystem/Countries",
                component: "Countries",
              },
              {
                id: "********",
                path: "/BasicSystem/BankBranchesAccounts",
                component: "BankBranchesAccounts",
              },
              {
                id: "********",
                path: "/BasicSystem/ComBanks",
                component: "ComBanks",
              },
              {
                id: "********",
                path: "/BasicSystem/ComIdTypes",
                component: "ComIdTypes",
              },
              {
                id: "********",
                path: "/BasicSystem/RelationsType",
                component: "RelationsType",
              },
              {
                id: "********",
                path: "/BasicSystem/TaxTypes",
                component: "TaxTypes",
              },
              {
                id: "********",
                path: "/BasicSystem/ComPrefixSufix",
                component: "ComPrefixSufix",
              },
              {
                id: "********",
                path: "/BasicSystem/ComContactTypes",
                component: "ComContactTypes",
              },
              {
                id: "********",
                path: "/BasicSystem/HiddenForms",
                component: "HiddenForms",
              },
              {
                id: "00010110",
                path: "/BasicSystem/Systems",
                component: "Systems",
              },
              {
                id: "00010120",
                path: "/BasicSystem/Companies",
                component: "Companies",
              },
              {
                id: "00010130",
                path: "/BasicSystem/SystemCompanies",
                component: "SystemCompanies",
              },
              {
                id: "00010160",
                path: "/BasicSystem/Currencies",
                component: "Currencies",
              },
              {
                id: "00010140",
                path: "/BasicSystem/Templates",
                component: "Templates",
              },
              {
                id: "00010150",
                path: "/BasicSystem/Users",
                component: "Users",
              },
              {
                id: "00010151",
                path: "/BasicSystem/ComLookups",
                component: "ComLookups",
              },
              {
                id: "00010152",
                path: "/BasicSystem/SystemUserParameters",
                component: "SystemUserParameters",
              },
              {
                id: "00010153",
                path: "/BasicSystem/UserParameters",
                component: "UserParameters",
              },
              {
                id: "00010197",
                path: "/BasicSystem/NotificationTypes",
                component: "NotificationTypes",
              },
              {
                id: "00010198",
                path: "/BasicSystem/SystemNotificationsSetup",
                component: "SystemNotificationsSetup",
              },
              {
                id: "00010199",
                path: "/BasicSystem/SystemNotificationsVariables",
                component: "SystemNotificationsVariables",
              },
            ],
          },
          {
            id: "000103",
            subObjects: [
              {
                id: "00010301",
                path: "/BasicSystem/RepUserPrivilege",
                component: "RepUserPrivilege",
              },
              {
                id: "00010302",
                path: "/BasicSystem/RepReportsLogVw",
                component: "RepReportsLogVw",
              },
            ],
          },
          {
            id: "000102",
            subObjects: [
              {
                id: "00010201",
                path: "/BasicSystem/ReportsLogVw",
                component: "ReportsLogVw",
              },
            ],
          },
        ],
      },
      {
        id: "0002",
        subMenus: [
          {
            id: "000201",
            subObjects: [
              {
                id: "00020101",
                path: "/BasicSystem/SupportEmployees",
                component: "SupportEmployees",
              },
              {
                id: "00020102",
                path: "/BasicSystem/SupportCustomers",
                component: "SupportCustomers",
              },
              {
                id: "00020103",
                path: "/BasicSystem/SupportTypes",
                component: "SupportTypes",
              },
              {
                id: "00020104",
                path: "/BasicSystem/SupportExtraSystems",
                component: "SupportExtraSystems",
              },
            ],
          },
          {
            id: "000202",
            subObjects: [
              {
                id: "00020201",
                path: "/BasicSystem/SupportRequests",
                component: "SupportRequests",
              },
              {
                id: "00020202",
                path: "/BasicSystem/SupportDailyReports",
                component: "SupportDailyReports",
              },

              {
                id: "00020203",
                path: "/BasicSystem/SupportFollows",
                component: "SupportFollows",
              },
            ],
          },
          {
            id: "000203",
            subObjects: [
              {
                id: "00020301",
                path: "/BasicSystem/SupportRequestsDetails",
                component: "SupportRequestsDetails",
              },
            ],
          },
        ],
      },
      {
        id: "0003",
        subMenus: [
          {
            id: "000301",
            subObjects: [
              {
                id: "00030101",
                path: "/TabletsSystem/TabletDevices",
                component: "TabletDevices",
              },
              {
                id: "00030102",
                path: "/TabletsSystem/TabletSims",
                component: "TabletSims",
              },
            ],
          },
          {
            id: "000302",
            subObjects: [
              {
                id: "00030201",
                path: "/TabletsSystem/TabletDevicesUsers",
                component: "TabletDevicesUsers",
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "01",
    systemIcon: "HHUSystem",
    subSystems: [
      {
        id: "0101",
        subMenus: [
          {
            id: "010101",
            subObjects: [
              {
                id: "01010112",
                path: "/HHUSystem/HhuStatus",
                component: "HhuStatus",
              },
              {
                id: "01010101",
                path: "/HHUSystem/HhuCustomerTypes",
                component: "HhuCustomerTypes",
              },
              {
                id: "01010102",
                path: "/HHUSystem/HhuVatTypes",
                component: "HhuVatTypes",
              },
              {
                id: "01010103",
                path: "/HHUSystem/HhuServiceTypes",
                component: "HhuServiceTypes",
              },
              {
                id: "01010104",
                path: "/HHUSystem/HhuCycleTypes",
                component: "HhuCycleTypes",
              },
              {
                id: "01010105",
                path: "/HHUSystem/HhuMeterStatus",
                component: "HhuMeterStatus",
              },
              {
                id: "01010106",
                path: "/HHUSystem/HhuPeriodicalServices",
                component: "HhuPeriodicalServices",
              },
              {
                id: "01010107",
                path: "/HHUSystem/HhuTransactionCodes",
                component: "HhuTransactionCodes",
              },
              {
                id: "01010108",
                path: "/HHUSystem/HhuTariffTypes",
                component: "HhuTariffTypes",
              },
              {
                id: "01010109",
                path: "/HHUSystem/HhuPenaltyFactors",
                component: "HhuPenaltyFactors",
              },
              {
                id: "01010110",
                path: "/HHUSystem/HhuDevices",
                component: "HhuDevices",
              },
              {
                id: "01010111",
                path: "/HHUSystem/HhuReaders",
                component: "HhuReaders",
              },
              {
                id: "01010113",
                path: "/HHUSystem/HhuCalcTypes",
                component: "HhuCalcTypes",
              },
              {
                id: "01010120",
                path: "/HHUSystem/CompaniesSystemParamHHUSystem",
                component: "CompaniesSystemParamHHUSystem",
              },
              {
                id: "01010121",
                path: "/HHUSystem/HhuInvoiceNotes",
                component: "HhuInvoiceNotes",
              },
              {
                id: "01010115",
                path: "/HHUSystem/HhuHolidays",
                component: "HhuHolidays",
              },
              {
                id: "01010116",
                path: "/HHUSystem/HhuServiceCategory",
                component: "HhuServiceCategory",
              },
              {
                id: "01010122",
                path: "/HHUSystem/HhuExtraData",
                component: "HhuExtraData",
              },
            ],
          },
          {
            id: "010102",
            subObjects: [
              {
                id: "01010205",
                path: "/HHUSystem/HhuReadings",
                component: "HhuReadings",
              },
              {
                id: "01010208",
                path: "/HHUSystem/HhuInvoicesForm",
                component: "HhuInvoicesForm",
              },
              {
                id: "01010212",
                path: "/HHUSystem/HhuServiceDetails",
                component: "HhuServiceDetails",
              },
              {
                id: "01010201",
                path: "/HHUSystem/HhuPatches",
                component: "HhuPatches",
              },
              {
                id: "01010211",
                path: "/HHUSystem/HhuDevicesSummary",
                component: "HhuDevicesSummary",
              },
              {
                id: "01010203",
                path: "/HHUSystem/HhuLog",
                component: "HhuLog",
              },
              {
                id: "01010204",
                path: "/HHUSystem/HhuErrorsLog",
                component: "HhuErrorsLog",
              },
              {
                id: "01010207",
                path: "/HHUSystem/HhuAppendService",
                component: "HhuAppendService",
              },
              {
                id: "01010206",
                path: "/HHUSystem/HhuInvoices",
                component: "HhuInvoices",
              },
              {
                id: "01010223",
                path: "/HHUSystem/HhuGpsStatVw",
                component: "HhuGpsStatVw",
              },
              {
                id: "01010224",
                path: "/HHUSystem/HhuPatchesDailyStatusVw",
                component: "HhuPatchesDailyStatusVw",
              },
            ],
          },
          {
            id: "010103",
            subObjects: [
              {
                id: "01010304",
                path: "/HHUSystem/RepHhuAppendServices",
                component: "RepHhuAppendServices",
              },
              {
                id: "01010301",
                path: "/HHUSystem/RepHhuReadings",
                component: "RepHhuReadings",
              },
              {
                id: "01010302",
                path: "/HHUSystem/RepHhuInvoices",
                component: "RepHhuInvoices",
              },
              {
                id: "01010306",
                path: "/HHUSystem/RepHhuReadingPeriod",
                component: "RepHhuReadingPeriod",
              },
              {
                id: "01010303",
                path: "/HHUSystem/RepHhuDailySummary",
                component: "RepHhuDailySummary",
              },
              {
                id: "01010305",
                path: "/HHUSystem/RepHhuNotes",
                component: "RepHhuNotes",
              },
              {
                id: "01010307",
                path: "/HHUSystem/RepHhuPeriodSummary",
                component: "RepHhuPeriodSummary",
              },
              {
                id: "01010312",
                path: "/HHUSystem/RepHhuDisServicesBranches",
                component: "RepHhuDisServicesBranches",
              },
              {
                id: "01010313",
                path: "/HHUSystem/RepHhuConServices",
                component: "RepHhuConServices",
              },
              {
                id: "01010314",
                path: "/BasicSystem/RepHhuPaymentsUnsent",
                component: "RepHhuPaymentsUnsent",
              },
              {
                id: "01010316",
                path: "/HHUSystem/RepHhuMonthlySummary",
                component: "RepHhuMonthlySummary",
              },

              {
                id: "01010308",
                path: "/HHUSystem/RepHhuOddCases",
                component: "RepHhuOddCases",
              },
              {
                id: "01010310",
                path: "/HHUSystem/RepHhuDisServices",
                component: "RepHhuDisServices",
              },
              {
                id: "01010311",
                path: "/HHUSystem/RepHhuErrors",
                component: "RepHhuErrors",
              },
              {
                id: "01010309",
                path: "/HHUSystem/RepHhuReadingsByStatus",
                component: "RepHhuReadingsByStatus",
              },
              {
                id: "01010317",
                path: "/HHUSystem/RepHhuUnenteredServices",
                component: "RepHhuUnenteredServices",
              },
              {
                id: "01010318",
                path: "/HHUSystem/RepHhuPayments",
                component: "RepHhuPayments",
              },
              {
                id: "01010315",
                path: "/HHUSystem/RepHhuUnreadServices",
                component: "RepHhuUnreadServices",
              },
              {
                id: "01010320",
                path: "/HHUSystem/RepHhuInvoicesDiff",
                component: "RepHhuInvoicesDiff",
              },
              {
                id: "01010319",
                path: "/HHUSystem/RepHhuTariffTypesRegions",
                component: "RepHhuTariffTypesRegions",
              },
              {
                id: "01010321",
                path: "/HHUSystem/RepHhuEstimatedReadings",
                component: "RepHhuEstimatedReadings",
              },
              {
                id: "01010322",
                path: "/HHUSystem/RepHhuNotice",
                component: "RepHhuNotice",
              },
              {
                id: "01010323",
                path: "/HHUSystem/RepHhuRepeatedServices",
                component: "RepHhuRepeatedServices",
              },
            ],
          },
        ],
      },
      {
        id: "0102",
        subMenus: [
          {
            id: "010201",
            subObjects: [
              {
                id: "01020101",
                path: "/HHUSystem/HhuInspectors",
                component: "HhuInspectors",
              },
              {
                id: "01020102",
                path: "/HHUSystem/HhuInspectionTrans",
                component: "HhuInspectionTrans",
              },
            ],
          },
          {
            id: "010203",
            subObjects: [
              {
                id: "01020301",
                path: "/HHUSystem/RepHhuInsTransRepStatistic",
                component: "RepHhuInsTransRepStatistic",
              },
              {
                id: "01020303",
                path: "/HHUSystem/RepHhuInsBySbranchAppl",
                component: "RepHhuInsBySbranchAppl",
              },
              {
                id: "01020310",
                path: "/HHUSystem/RepHhuReaderPoints",
                component: "RepHhuReaderPoints",
              },
              {
                id: "01020309",
                path: "/HHUSystem/RepHhuInsProcessByInspector",
                component: "RepHhuInsProcessByInspector",
              },
              {
                id: "01020308",
                path: "/HHUSystem/RepHhuInsLateProcess",
                component: "RepHhuInsLateProcess",
              },
              {
                id: "01020307",
                path: "/HHUSystem/RepHhuInsProcessRepSrvs",
                component: "RepHhuInsProcessRepSrvs",
              },
              {
                id: "01020306",
                path: "/HHUSystem/RepHhuInsByService",
                component: "RepHhuInsByService",
              },
              {
                id: "01020305",
                path: "/HHUSystem/RepHhuInsBySbranch",
                component: "RepHhuInsBySbranch",
              },
              {
                id: "01020304",
                path: "/HHUSystem/RepHhuInsByReader",
                component: "RepHhuInsByReader",
              },
              {
                id: "01020302",
                path: "/HHUSystem/RepHhuInsMeterStatusStat",
                component: "RepHhuInsMeterStatusStat",
              },
            ],
          },
          {
            id: "010202",
            subObjects: [
              {
                id: "01020201",
                path: "/HHUSystem/HhuInspection",
                component: "HhuInspection",
              },
              {
                id: "01020212",
                path: "/HHUSystem/HhuInsPrepaidManualEntry",
                component: "HhuInsPrepaidManualEntry",
              },
              {
                id: "01020202",
                path: "/HHUSystem/HhuInspectionProcess",
                component: "HhuInspectionProcess",
              },
              {
                id: "01020203",
                path: "/HHUSystem/HhuInspectionProcessEntrybyservice",
                component: "HhuInspectionProcessEntrybyservice",
              },
              {
                id: "01020204",
                path: "/HHUSystem/HhuInspectionProcessEntrybyinspector",
                component: "HhuInspectionProcessEntrybyinspector",
              },
              {
                id: "01020205",
                path: "/HHUSystem/HhuInspectionProcessFollowup",
                component: "HhuInspectionProcessFollowup",
              },
              {
                id: "01020206",
                path: "/HHUSystem/HhuInspectionPointsApproval",
                component: "HhuInspectionPointsApproval",
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "11",
    systemIcon: "ISOSystem",
    subSystems: [
      {
        id: "1102",
        subMenus: [
          {
            id: "110201",
            subObjects: [
              {
                id: "11020101",
                path: "/ISOSystem/IsoDocumentsProcesses",
                component: "IsoDocumentsProcesses",
              },
              {
                id: "11020102",
                path: "/ISOSystem/IsoDocumentsProcessesLog",
                component: "IsoDocumentsProcessesLog",
              },
              {
                id: "11020103",
                path: "/ISOSystem/IsoDocumentsProcessesLogReview",
                component: "IsoDocumentsProcessesLogReview",
              },
            ],
          },
        ],
      },
      {
        id: "1101",
        subMenus: [
          {
            id: "110104",
            subObjects: [
              {
                id: "11010401",
                path: "/ISOSystem/IsoAuditPlan",
                component: "IsoAuditPlan",
              },
              {
                id: "11010404",
                path: "/ISOSystem/IsoAuditPlanAuditSeqVw",
                component: "IsoAuditPlanAuditSeqVw",
              },
              {
                id: "11010407",
                path: "/ISOSystem/IsoAuditPlanNotesReview",
                component: "IsoAuditPlanNotesReview",
              },

              {
                id: "11010403",
                path: "/ISOSystem/IsoAuditPlanSchedule",
                component: "IsoAuditPlanSchedule",
              },
              {
                id: "11010406",
                path: "/ISOSystem/IsoAuditPlanScheduleEdit",
                component: "IsoAuditPlanScheduleEdit",
              },
              {
                id: "11010402",
                path: "/ISOSystem/IsoAuditYear",
                component: "IsoAuditYear",
              },
              {
                id: "11010405",
                path: "/ISOSystem/IsoCorrectiveActions",
                component: "IsoCorrectiveActions",
              },
              {
                id: "11010428",
                path: "/ISOSystem/RepIsoAuditorSummaryRep",
                component: "RepIsoAuditorSummaryRep",
              },
              {
                id: "11010421",
                path: "/ISOSystem/RepIsoAuditPlan",
                component: "RepIsoAuditPlan",
              },
              {
                id: "11010427",
                path: "/ISOSystem/RepIsoAuditPlanComparison",
                component: "RepIsoAuditPlanComparison",
              },
              {
                id: "11010429",
                path: "/ISOSystem/RepIsoAuditPlanComparisonH",
                component: "RepIsoAuditPlanComparisonH",
              },
              {
                id: "11010425",
                path: "/ISOSystem/RepIsoAuditPlanNotes",
                component: "RepIsoAuditPlanNotes",
              },
              {
                id: "11010430",
                path: "/ISOSystem/RepIsoAuditPlanNotesMgr",
                component: "RepIsoAuditPlanNotesMgr",
              },
              {
                id: "11010431",
                path: "/ISOSystem/RepIsoAuditPlanNotesRev",
                component: "RepIsoAuditPlanNotesRev",
              },
              {
                id: "11010422",
                path: "/ISOSystem/RepIsoAuditPlanSchedule",
                component: "RepIsoAuditPlanSchedule",
              },
              {
                id: "11010423",
                path: "/ISOSystem/RepIsoAuditPlanSummary",
                component: "RepIsoAuditPlanSummary",
              },
              {
                id: "11010424",
                path: "/ISOSystem/RepIsoCorrectiveActions",
                component: "RepIsoCorrectiveActions",
              },
              {
                id: "11010426",
                path: "/ISOSystem/RepIsoPlanKeysMatrix",
                component: "RepIsoPlanKeysMatrix",
              },
            ],
          },
          {
            id: "110103",
            subObjects: [
              {
                id: "11010306",
                path: "/ISOSystem/RepIsoDelDocuments",
                component: "RepIsoDelDocuments",
              },
              {
                id: "11010309",
                path: "/ISOSystem/RepIsoDocumentRecords",
                component: "RepIsoDocumentRecords",
              },
              {
                id: "11010305",
                path: "/ISOSystem/RepIsoEmployeesDiv",
                component: "RepIsoEmployeesDiv",
              },

              {
                id: "11010308",
                path: "/ISOSystem/RepIsoMainMenu",
                component: "RepIsoMainMenu",
              },
              {
                id: "11010302",
                path: "/ISOSystem/RepIsoNewDocuments",
                component: "RepIsoNewDocuments",
              },
              {
                id: "11010307",
                path: "/ISOSystem/RepIsoRequests",
                component: "RepIsoRequests",
              },
              {
                id: "11010303",
                path: "/ISOSystem/RepIsoTemplatesEmployees",
                component: "RepIsoTemplatesEmployees",
              },
              {
                id: "11010304",
                path: "/ISOSystem/RepIsoTemplatesEmployeesE",
                component: "RepIsoTemplatesEmployeesE",
              },
              {
                id: "11010301",
                path: "/ISOSystem/RepIsoUpdatedDocuments",
                component: "RepIsoUpdatedDocuments",
              },
            ],
          },
          {
            id: "110101",
            subObjects: [
              {
                id: "11010100",
                path: "/ISOSystem/CompaniesSystemParamISOSystem",
                component: "CompaniesSystemParamISOSystem",
              },
              {
                id: "11010109",
                path: "/ISOSystem/IsoCorrectiveActionSources",
                component: "IsoCorrectiveActionSources",
              },
              {
                id: "11010101",
                path: "/ISOSystem/IsoDocumentTypes",
                component: "IsoDocumentTypes",
              },

              {
                id: "11010105",
                path: "/ISOSystem/IsoEmployees",
                component: "IsoEmployees",
              },
              {
                id: "11010108",
                path: "/ISOSystem/IsoHolidays",
                component: "IsoHolidays",
              },
              {
                id: "11010103",
                path: "/ISOSystem/IsoOrgTreeDep",
                component: "IsoOrgTreeDep",
              },
              {
                id: "11010102",
                path: "/ISOSystem/IsoOrgTreeDiv",
                component: "IsoOrgTreeDiv",
              },
              {
                id: "11010107",
                path: "/ISOSystem/IsoPlanKeys",
                component: "IsoPlanKeys",
              },
              {
                id: "11010104",
                path: "/ISOSystem/IsoReportFormat",
                component: "IsoReportFormat",
              },
              {
                id: "11010106",
                path: "/ISOSystem/IsoTemplates",
                component: "IsoTemplates",
              },
              {
                id: "11010110",
                path: "/ISOSystem/IsoUnconformityReasons",
                component: "IsoUnconformityReasons",
              },
            ],
          },
          {
            id: "110102",
            subObjects: [
              {
                id: "11010201",
                path: "/ISOSystem/IsoDocuments",
                component: "IsoDocuments",
              },
              {
                id: "11010203",
                path: "/ISOSystem/IsoDocumentsEmployees",
                component: "IsoDocumentsEmployees",
              },
              {
                id: "11010205",
                path: "/ISOSystem/IsoDocumentsH",
                component: "IsoDocumentsH",
              },

              {
                id: "11010204",
                path: "/ISOSystem/IsoDocumentsTemp",
                component: "IsoDocumentsTemp",
              },
              {
                id: "11010206",
                path: "/ISOSystem/IsoRequests",
                component: "IsoRequests",
              },
              {
                id: "11010202",
                path: "/ISOSystem/IsoTemplatesEmployees",
                component: "IsoTemplatesEmployees",
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "15",
    systemIcon: "SolarSystem",
    subSystems: [
      {
        id: "1501",
        subMenus: [
          {
            id: "150101",
            subObjects: [
              {
                id: "15010113",
                path: "/SolarSystem/SolItemsGroups",
                component: "SolItemsGroups",
              },
              {
                id: "15010114",
                path: "/SolarSystem/SolLookupTables",
                component: "SolLookupTables",
              },
              {
                id: "15010115",
                path: "/SolarSystem/SolResultCodes",
                component: "SolResultCodes",
              },
              {
                id: "15010100",
                path: "/SolarSystem/CompaniesSystemParamSolarSystem",
                component: "CompaniesSystemParamSolarSystem",
              },
              {
                id: "15010102",
                path: "/SolarSystem/SolCellTypes",
                component: "SolCellTypes",
              },
              {
                id: "15010104",
                path: "/SolarSystem/SolMeterTypes",
                component: "SolMeterTypes",
              },
              {
                id: "15010105",
                path: "/SolarSystem/SolSuppliers",
                component: "SolSuppliers",
              },
              {
                id: "15010103",
                path: "/SolarSystem/SolInverterTypes",
                component: "SolInverterTypes",
              },
              {
                id: "15010101",
                path: "/SolarSystem/SolSystemTypes",
                component: "SolSystemTypes",
              },
              {
                id: "15010106",
                path: "/SolarSystem/SolCommunicationDevices",
                component: "SolCommunicationDevices",
              },
              {
                id: "15010108",
                path: "/SolarSystem/SolTransactionTypes",
                component: "SolTransactionTypes",
              },
              {
                id: "15010116",
                path: "/SolarSystem/SolStationsCategories",
                component: "SolStationsCategories",
              },
              {
                id: "15010117",
                path: "/SolarSystem/SolVisitsReadingsSetup",
                component: "SolVisitsReadingsSetup",
              },
              {
                id: "15010118",
                path: "/SolarSystem/SolExtras",
                component: "SolExtras",
              },
              {
                id: "15010119",
                path: "/SolarSystem/SolItems",
                component: "SolItems",
              },
              {
                id: "15010109",
                path: "/SolarSystem/SolOrganizations",
                component: "SolOrganizations",
              },
              {
                id: "15010112",
                path: "/SolarSystem/SolTariffTypesMapping",
                component: "SolTariffTypesMapping",
              },
              {
                id: "15010110",
                path: "/SolarSystem/SolAttachmentTypes",
                component: "SolAttachmentTypes",
              },
            ],
          },
          {
            id: "150103",
            subObjects: [
              {
                id: "15010304",
                path: "/SolarSystem/RepSolVisitsSummaryRep",
                component: "RepSolVisitsSummaryRep",
              },
              {
                id: "15010305",
                path: "/SolarSystem/RepSolVisitsDetails",
                component: "RepSolVisitsDetails",
              },
              {
                id: "15010306",
                path: "/SolarSystem/RepSolVisits",
                component: "RepSolVisits",
              },
              {
                id: "15010301",
                path: "/SolarSystem/RepSolStationsSummaryRep",
                component: "RepSolStationsSummaryRep",
              },
              {
                id: "15010302",
                path: "/SolarSystem/RepSolStationsDetails",
                component: "RepSolStationsDetails",
              },
              {
                id: "15010303",
                path: "/SolarSystem/RepSolStations",
                component: "RepSolStations",
              },
              {
                id: "15010310",
                path: "/SolarSystem/RepSolStationsCellsInv",
                component: "RepSolStationsCellsInv",
              },
              {
                id: "15010311",
                path: "/SolarSystem/RepSolStationsSummaryNewRep",
                component: "RepSolStationsSummaryNewRep",
              },
              {
                id: "15010309",
                path: "/SolarSystem/RepSolVisitsSummaryDtlRep",
                component: "RepSolVisitsSummaryDtlRep",
              },
              {
                id: "15010307",
                path: "/SolarSystem/RepSolStationsQtyStatRep",
                component: "RepSolStationsQtyStatRep",
              },
              {
                id: "15010308",
                path: "/SolarSystem/RepSolVisitsOrganizations",
                component: "RepSolVisitsOrganizations",
              },
            ],
          },
          {
            id: "150102",
            subObjects: [
              {
                id: "15010204",
                path: "/SolarSystem/SolStationsQuery",
                component: "SolStationsQuery",
              },
              {
                id: "15010205",
                path: "/SolarSystem/SolVisitsQuery",
                component: "SolVisitsQuery",
              },
              {
                id: "15010208",
                path: "/SolarSystem/SolStationsVisitsMetersVw",
                component: "SolStationsVisitsMetersVw",
              },
              {
                id: "15010221",
                path: "/SolarSystem/SolStationsInfoChange",
                component: "SolStationsInfoChange",
              },
              {
                id: "15010201",
                path: "/SolarSystem/SolStations",
                component: "SolStations",
              },
              {
                id: "15010202",
                path: "/SolarSystem/SolVisits",
                component: "SolVisits",
              },
              {
                id: "15010203",
                path: "/SolarSystem/SolVisitsTransfer",
                component: "SolVisitsTransfer",
              },
              {
                id: "15010206",
                path: "/SolarSystem/SolVisitsTransferM",
                component: "SolVisitsTransferM",
              },
              {
                id: "15010226",
                path: "/SolarSystem/SolStationsMetersInfoChange",
                component: "SolStationsMetersInfoChange",
              },
              {
                id: "15010228",
                path: "/SolarSystem/SolStationsUploadedVw",
                component: "SolStationsUploadedVw",
              },
              {
                id: "15010227",
                path: "/SolarSystem/SolUpdateVisitDateLog",
                component: "SolUpdateVisitDateLog",
              },
              {
                id: "15010207",
                path: "/SolarSystem/SolVisitsTabular",
                component: "SolVisitsTabular",
              },
              {
                id: "15010209",
                path: "/SolarSystem/SolRecycleBalanceNetting",
                component: "SolRecycleBalanceNetting",
              },
              {
                id: "15010214",
                path: "/SolarSystem/SolStationsVisitsMetersVwQ",
                component: "SolStationsVisitsMetersVwQ",
              },
              {
                id: "15010223",
                path: "/SolarSystem/SolCheckMonthlyVisits",
                component: "SolCheckMonthlyVisits",
              },
              {
                id: "15010222",
                path: "/SolarSystem/SolMoveRecycleBalance",
                component: "SolMoveRecycleBalance",
              },
              {
                id: "15010225",
                path: "/SolarSystem/SolBenStationsConsReadings",
                component: "SolBenStationsConsReadings",
              },
              {
                id: "15010210",
                path: "/SolarSystem/SolBillingConsumptionQty",
                component: "SolBillingConsumptionQty",
              },
              {
                id: "15010211",
                path: "/SolarSystem/SolVisitsSolarQtyVw",
                component: "SolVisitsSolarQtyVw",
              },
              {
                id: "15010212",
                path: "/SolarSystem/SolStationsVisitsMetersVwM",
                component: "SolStationsVisitsMetersVwM",
              },
              {
                id: "15010213",
                path: "/SolarSystem/SolIntegrationVw",
                component: "SolIntegrationVw",
              },

              {
                id: "15010218",
                path: "/SolarSystem/SolVisitsOrganizations",
                component: "SolVisitsOrganizations",
              },
              {
                id: "15010217",
                path: "/SolarSystem/SolStationsOrganizations",
                component: "SolStationsOrganizations",
              },
              {
                id: "15010215",
                path: "/SolarSystem/SolStationsMetersReadVw",
                component: "SolStationsMetersReadVw",
              },
              {
                id: "15010216",
                path: "/SolarSystem/SolRecycleBalanceDiffVw",
                component: "SolRecycleBalanceDiffVw",
              },
              {
                id: "15010224",
                path: "/SolarSystem/SolStationsMetersChanged",
                component: "SolStationsMetersChanged",
              },
              {
                id: "15010219",
                path: "/SolarSystem/SolStationsMetersPReadVw",
                component: "SolStationsMetersPReadVw",
              },
              {
                id: "15010220",
                path: "/SolarSystem/SolOldRecycleBalanceDifVw",
                component: "SolOldRecycleBalanceDifVw",
              },
            ],
          },
          {
            id: "150104",
            subObjects: [
              {
                id: "15010401",
                path: "/SolarSystem/SolInjazReadingsFileVw",
                component: "SolInjazReadingsFileVw",
              },
              {
                id: "15010404",
                path: "/SolarSystem/RepSolMonthlyVisitsRep",
                component: "RepSolMonthlyVisitsRep",
              },
              {
                id: "15010402",
                path: "/SolarSystem/SolInjazVisitsDtls",
                component: "SolInjazVisitsDtls",
              },
              {
                id: "15010450",
                path: "/SolarSystem/RepSolInjazReadingsFileVw",
                component: "RepSolInjazReadingsFileVw",
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "04",
    systemIcon: "ShareHoldersSystem",
    subSystems: [
      //0401
      {
        id: "0401",
        subMenus: [
          {
            id: "040103",
            subObjects: [
              {
                id: "04010308",
                path: "/ShareHoldersSystem/ShSubscribeForShares",
                component: "ShSubscribeForShares",
              },
              {
                id: "04010307",
                path: "/ShareHoldersSystem/ShUpdateShareValue",
                component: "ShUpdateShareValue",
              },
              {
                id: "04010306",
                path: "/ShareHoldersSystem/ShUpdateProfitCurrency",
                component: "ShUpdateProfitCurrency",
              },
              {
                id: "04010305",
                path: "/ShareHoldersSystem/ShIncreasingProcess",
                component: "ShIncreasingProcess",
              },
              {
                id: "04010304",
                path: "/ShareHoldersSystem/ShSingleSideActivate",
                component: "ShSingleSideActivate",
              },
              {
                id: "04010303",
                path: "/ShareHoldersSystem/ShTransferBenToHolder",
                component: "ShTransferBenToHolder",
              },
              {
                id: "04010302",
                path: "/ShareHoldersSystem/ShMultiSideTrans",
                component: "ShMultiSideTrans",
              },
              {
                id: "04010301",
                path: "/ShareHoldersSystem/ShSingleSideTrans",
                component: "ShSingleSideTrans",
              },
              {
                id: "04010310",
                path: "/ShareHoldersSystem/ShSharesTransCommQuery",
                component: "ShSharesTransCommQuery",
              },
              {
                id: "04010309",
                path: "/ShareHoldersSystem/ShSharesTransCommission",
                component: "ShSharesTransCommission",
              },
              {
                id: "04010313",
                path: "/ShareHoldersSystem/ShFeesTransactions",
                component: "ShFeesTransactions",
              },
              {
                id: "04010311",
                path: "/ShareHoldersSystem/ShDistributeTreasuryStock",
                component: "ShDistributeTreasuryStock",
              },
              {
                id: "04010312",
                path: "/ShareHoldersSystem/ShHoldersSendSms",
                component: "ShHoldersSendSms",
              },
            ],
          },
          {
            id: "040106",
            subObjects: [
              {
                id: "04010608",
                path: "/ShareHoldersSystem/ShHoldersSummary",
                component: "ShHoldersSummary",
              },
              {
                id: "04010607",
                path: "/ShareHoldersSystem/ShReviewDocInfoChange",
                component: "ShReviewDocInfoChange",
              },
              {
                id: "04010601",
                path: "/ShareHoldersSystem/ShReviewDocuments",
                component: "ShReviewDocuments",
              },
              {
                id: "04010606",
                path: "/ShareHoldersSystem/ShTaxExeReview",
                component: "ShTaxExeReview",
              },
              {
                id: "04010605",
                path: "/ShareHoldersSystem/ShReviewOrgProfit",
                component: "ShReviewOrgProfit",
              },
              {
                id: "04010604",
                path: "/ShareHoldersSystem/ShReviewOrgDocuments",
                component: "ShReviewOrgDocuments",
              },
              {
                id: "04010603",
                path: "/ShareHoldersSystem/ShReviewHolDocuments",
                component: "ShReviewHolDocuments",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShReviewHolderProfit",
                component: "ShReviewHolderProfit",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShProfitCurrencyChangeLog",
                component: "ShProfitCurrencyChangeLog",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShReviewDelBankLists",
                component: "ShReviewDelBankLists",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShReviewRevDiv",
                component: "ShReviewRevDiv",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggReviewDocuments",
                component: "ShAggReviewDocuments",
              },
            ],
          },
          {
            id: "040104",
            subObjects: [
              {
                id: "********",
                path: "/ShareHoldersSystem/ShDividingProfitBalance",
                component: "ShDividingProfitBalance",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShProfitSetup",
                component: "ShProfitSetup",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShProfitApproval",
                component: "ShProfitApproval",
              },
              {
                id: "04010404",
                path: "/ShareHoldersSystem/ShCancelUnreceivedDocuments",
                component: "ShCancelUnreceivedDocuments",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShProfitGenerate",
                component: "ShProfitGenerate",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShProfitGenerateSetup",
                component: "ShProfitGenerateSetup",
              },
            ],
          },
          {
            id: "040107",
            subObjects: [
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShBankDocumentsList",
                component: "RepShBankDocumentsList",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShReviewOrgProfit",
                component: "RepShReviewOrgProfit",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShReviewOrgDocuments",
                component: "RepShReviewOrgDocuments",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShReviewHolderProfit",
                component: "RepShReviewHolderProfit",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShReviewHolderDocuments",
                component: "RepShReviewHolderDocuments",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShProfitSeqSummary",
                component: "RepShProfitSeqSummary",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShDocDueDateTrans",
                component: "RepShDocDueDateTrans",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShBankTransDocuments",
                component: "RepShBankTransDocuments",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShGeneratesSummary",
                component: "RepShGeneratesSummary",
              },

              {
                id: "********",
                path: "/ShareHoldersSystem/RepShProfitOrgSlip",
                component: "RepShProfitOrgSlip",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShOrgProfitDocumentsH",
                component: "RepShOrgProfitDocumentsH",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShOrgProfitDocumentsA",
                component: "RepShOrgProfitDocumentsA",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShHolProfitDocumentsH",
                component: "RepShHolProfitDocumentsH",
              },
              {
                id: "04010708",
                path: "/ShareHoldersSystem/RepShHolProfitDocumentsA",
                component: "RepShHolProfitDocumentsA",
              },
              {
                id: "04010707",
                path: "/ShareHoldersSystem/RepShSharesProfitIist",
                component: "RepShSharesProfitIist",
              },
              {
                id: "04010706",
                path: "/ShareHoldersSystem/RepShProfitSlip",
                component: "RepShProfitSlip",
              },
              {
                id: "04010705",
                path: "/ShareHoldersSystem/RepShHoldersAddressList",
                component: "RepShHoldersAddressList",
              },
              {
                id: "04010723",
                path: "/ShareHoldersSystem/RepShHoldersListCheckId",
                component: "RepShHoldersListCheckId",
              },
              {
                id: "04010730",
                path: "/ShareHoldersSystem/RepShGenDocumentSummary",
                component: "RepShGenDocumentSummary",
              },
              {
                id: "04010704",
                path: "/ShareHoldersSystem/RepShHoldersFs",
                component: "RepShHoldersFs",
              },
              {
                id: "04010703",
                path: "/ShareHoldersSystem/RepShHoldersTrans",
                component: "RepShHoldersTrans",
              },
              {
                id: "04010702",
                path: "/ShareHoldersSystem/RepShHoldersCert",
                component: "RepShHoldersCert",
              },
              {
                id: "04010701",
                path: "/ShareHoldersSystem/RepShHoldersList",
                component: "RepShHoldersList",
              },
              {
                id: "04010721",
                path: "/ShareHoldersSystem/RepShTaxExeUpdates",
                component: "RepShTaxExeUpdates",
              },
              {
                id: "04010726",
                path: "/ShareHoldersSystem/RepShHoldersReviewData",
                component: "RepShHoldersReviewData",
              },
              {
                id: "04010722",
                path: "/ShareHoldersSystem/RepShHoldersProfitList",
                component: "RepShHoldersProfitList",
              },
              {
                id: "04010729",
                path: "/ShareHoldersSystem/RepShHoldersCard",
                component: "RepShHoldersCard",
              },
              {
                id: "04010728",
                path: "/ShareHoldersSystem/RepShHoldersListWithout",
                component: "RepShHoldersListWithout",
              },
              {
                id: "04010725",
                path: "/ShareHoldersSystem/RepShHolListByCategory",
                component: "RepShHolListByCategory",
              },
              {
                id: "04010724",
                path: "/ShareHoldersSystem/RepShHoldersSummary",
                component: "RepShHoldersSummary",
              },
              {
                id: "04010733",
                path: "/ShareHoldersSystem/RepShDocDueDateTransRev",
                component: "RepShDocDueDateTransRev",
              },
              {
                id: "04010734",
                path: "/ShareHoldersSystem/RepShSharesTransCommission",
                component: "RepShSharesTransCommission",
              },
              {
                id: "04010732",
                path: "/ShareHoldersSystem/RepShProfitDivYear",
                component: "RepShProfitDivYear",
              },
              {
                id: "04010738",
                path: "/ShareHoldersSystem/RepRecievedDocumentRep",
                component: "RepRecievedDocumentRep",
              },
              {
                id: "04010737",
                path: "/ShareHoldersSystem/RepShHolderBeginBalance",
                component: "RepShHolderBeginBalance",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShProfitSummaryRep",
                component: "RepShProfitSummaryRep",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShReceivedProfitRep",
                component: "RepShReceivedProfitRep",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShGeneratesBankDtls",
                component: "RepShGeneratesBankDtls",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShHoldersDocuments",
                component: "RepShHoldersDocuments",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShBankDocumentsListAgg",
                component: "RepShBankDocumentsListAgg",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShHoldersListAlphapet",
                component: "RepShHoldersListAlphapet",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShDocDueDateTransAgg",
                component: "RepShDocDueDateTransAgg",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/RepShHoldersProfitDetails",
                component: "RepShHoldersProfitDetails",
              },
            ],
          },
          {
            id: "040105",
            subObjects: [
              {
                id: "********",
                path: "/ShareHoldersSystem/ShUpdateDocNo",
                component: "ShUpdateDocNo",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShReceivingBankTrans",
                component: "ShReceivingBankTrans",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShDividingCheckByHolder",
                component: "ShDividingCheckByHolder",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShChangeOrgDocumentsType",
                component: "ShChangeOrgDocumentsType",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShCancelDocByOrg",
                component: "ShCancelDocByOrg",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShChangeHolDocumentsType",
                component: "ShChangeHolDocumentsType",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShCancelDocByHolder",
                component: "ShCancelDocByHolder",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShReceivingLists",
                component: "ShReceivingLists",
              },
              {
                id: "04010518",
                path: "/ShareHoldersSystem/ShCheckDivideReverse",
                component: "ShCheckDivideReverse",
              },
              {
                id: "04010503",
                path: "/ShareHoldersSystem/ShReceivingDocByHolder",
                component: "ShReceivingDocByHolder",
              },
              {
                id: "04010505",
                path: "/ShareHoldersSystem/ShProfitDocInfoChange",
                component: "ShProfitDocInfoChange",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShReceivingCheck",
                component: "ShReceivingCheck",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShCancelDocuments",
                component: "ShCancelDocuments",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShDoBanksListsTrans",
                component: "ShDoBanksListsTrans",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShReceivingDocByOrg",
                component: "ShReceivingDocByOrg",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShDividingCheckByHolderOpen",
                component: "ShDividingCheckByHolderOpen",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShUpdateDocDate",
                component: "ShUpdateDocDate",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShChangeDocumentType",
                component: "ShChangeDocumentType",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShDividingCheckByHolderManual",
                component: "ShDividingCheckByHolderManual",
              },
              {
                id: "04010522",
                path: "/ShareHoldersSystem/ShReviewDocumentsUpdate",
                component: "ShReviewDocumentsUpdate",
              },
            ],
          },
          {
            id: "040102",
            subObjects: [
              {
                id: "04010232",
                path: "/ShareHoldersSystem/ShHoldersReview",
                component: "ShHoldersReview",
              },
              {
                id: "04010202",
                path: "/ShareHoldersSystem/ShHoldersTrans",
                component: "ShHoldersTrans",
              },
              {
                id: "04010203",
                path: "/ShareHoldersSystem/ShBod",
                component: "ShBod",
              },
              {
                id: "04010201",
                path: "/ShareHoldersSystem/ShHolders",
                component: "ShHolders",
              },
              {
                id: "04010204",
                path: "/ShareHoldersSystem/ShDuplicateIdNoVw",
                component: "ShDuplicateIdNoVw",
              },
            ],
          },
          {
            id: "040101",
            subObjects: [
              {
                id: "04010105",
                path: "/ShareHoldersSystem/CompaniesSystemParamShareHoldersSystem",
                component: "CompaniesSystemParamShareHoldersSystem",
              },
              {
                id: "04010110",
                path: "/ShareHoldersSystem/ShHolderCategories",
                component: "ShHolderCategories",
              },
              {
                id: "04010104",
                path: "/ShareHoldersSystem/ShSetup",
                component: "ShSetup",
              },
              {
                id: "04010103",
                path: "/ShareHoldersSystem/ShTransactionTypes",
                component: "ShTransactionTypes",
              },
              {
                id: "04010101",
                path: "/ShareHoldersSystem/ShHolderTypes",
                component: "ShHolderTypes",
              },

              {
                id: "04010108",
                path: "/ShareHoldersSystem/ShNotes",
                component: "ShNotes",
              },
              {
                id: "04010109",
                path: "/ShareHoldersSystem/ShProfitCategories",
                component: "ShProfitCategories",
              },
              {
                id: "04010107",
                path: "/ShareHoldersSystem/ShSuspensionOrg",
                component: "ShSuspensionOrg",
              },
              {
                id: "04010106",
                path: "/ShareHoldersSystem/ShCertificateSetup",
                component: "ShCertificateSetup",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAssignTypes",
                component: "ShAssignTypes",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShFeesTypes",
                component: "ShFeesTypes",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShBankListMapping",
                component: "ShBankListMapping",
              },
            ],
          },
          {
            id: "040108",
            subObjects: [
              {
                id: "********",
                path: "/ShareHoldersSystem/ShProfitAggregateSetup",
                component: "ShProfitAggregateSetup",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShProfitAggregateGenerate",
                component: "ShProfitAggregateGenerate",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggDoBanksListsTrans",
                component: "ShAggDoBanksListsTrans",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggReceivingDocByHolder",
                component: "ShAggReceivingDocByHolder",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggCancelDocByHolder",
                component: "ShAggCancelDocByHolder",
              },

              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggProfitDocInfoChange",
                component: "ShAggProfitDocInfoChange",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggChangeHolDocumentsType",
                component: "ShAggChangeHolDocumentsType",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggDividingCheckByHolder",
                component: "ShAggDividingCheckByHolder",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShLoadBankFilesCheck",
                component: "ShLoadBankFilesCheck",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShLoadBankFiles",
                component: "ShLoadBankFiles",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggDivCheckByHolderManual",
                component: "ShAggDivCheckByHolderManual",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggDivCheckByHolderOpen",
                component: "ShAggDivCheckByHolderOpen",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShCheckDivideReverseAgg",
                component: "ShCheckDivideReverseAgg",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAggReviewDocumentsUpdate",
                component: "ShAggReviewDocumentsUpdate",
              },
            ],
          },
        ],
      },
      //0402
      {
        id: "0402",
        subMenus: [
          {
            id: "040201",
            subObjects: [
              {
                id: "04020102",
                path: "/ShareHoldersSystem/ShAttendanceTypes",
                component: "ShAttendanceTypes",
              },
              {
                id: "04020101",
                path: "/ShareHoldersSystem/ShMeetingTypes",
                component: "ShMeetingTypes",
              },
              {
                id: "04020104",
                path: "/ShareHoldersSystem/ShCommitteeMembersShareHoldersSystem",
                component: "ShCommitteeMembersShareHoldersSystem",
              },
              {
                id: "04020103",
                path: "/ShareHoldersSystem/BoardCommittees",
                component: "BoardCommittees",
              },
            ],
          },
          {
            id: "040203",
            subObjects: [
              {
                id: "04020302",
                path: "/ShareHoldersSystem/RepShMeetingInvitationsLbl",
                component: "RepShMeetingInvitationsLbl",
              },
              {
                id: "04020301",
                path: "/ShareHoldersSystem/RepShMeetingInvitations",
                component: "RepShMeetingInvitations",
              },
              {
                id: "04020303",
                path: "/ShareHoldersSystem/RepShMeetingAddresses",
                component: "RepShMeetingAddresses",
              },
              {
                id: "04020305",
                path: "/ShareHoldersSystem/RepShMeetingMandates",
                component: "RepShMeetingMandates",
              },
              {
                id: "04020304",
                path: "/ShareHoldersSystem/RepShMeetingInvitations2",
                component: "RepShMeetingInvitations2",
              },
            ],
          },
          {
            id: "040202",
            subObjects: [
              {
                id: "04020201",
                path: "/ShareHoldersSystem/ShMeetings",
                component: "ShMeetings",
              },
              {
                id: "04020203",
                path: "/ShareHoldersSystem/ShMeetingAttendenceBarcode",
                component: "ShMeetingAttendenceBarcode",
              },
              {
                id: "04020202",
                path: "/ShareHoldersSystem/ShMeetingBarcode",
                component: "ShMeetingBarcode",
              },
            ],
          },
        ],
      },
      //0403
      {
        id: "0403",
        subMenus: [
          {
            id: "040302",
            subObjects: [
              {
                id: "04030202",
                path: "/ShareHoldersSystem/ShFinTransfer",
                component: "ShFinTransfer",
              },
              {
                id: "04030201",
                path: "/ShareHoldersSystem/ShFinTransLog",
                component: "ShFinTransLog",
              },
              {
                id: "04030203",
                path: "/ShareHoldersSystem/ShFinTransferSummary",
                component: "ShFinTransferSummary",
              },
            ],
          },
          {
            id: "040301",
            subObjects: [
              {
                id: "********",
                path: "/ShareHoldersSystem/ShFinCurrencyMapping",
                component: "ShFinCurrencyMapping",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShFinSetup",
                component: "ShFinSetup",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShFinBanksMapping",
                component: "ShFinBanksMapping",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShFinGeneralSetup",
                component: "ShFinGeneralSetup",
              },
            ],
          },
        ],
      },
      //0404
      {
        id: "0404",
        subMenus: [
          {
            id: "040401",
            subObjects: [
              {
                id: "********",
                path: "/ShareHoldersSystem/ShAttachmentTypes",
                component: "ShAttachmentTypes",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShCommPaymentTypes",
                component: "ShCommPaymentTypes",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/TypesOfBoardCommittees",
                component: "TypesOfBoardCommittees",
              },
              {
                id: "********",
                path: "/ShareHoldersSystem/ShMembers",
                component: "ShMembers",
              },
              {
                id: "04040105",
                path: "/ShareHoldersSystem/ShCommitteeMembersShareHoldersSystem",
                component: "ShCommitteeMembersShareHoldersSystem",
              },
            ],
          },
          {
            id: "040402",
            subObjects: [
              {
                id: "04040299",
                path: "/ShareHoldersSystem/ShCommitteeMeetingsBk",
                component: "ShCommitteeMeetingsBk",
              },
              {
                id: "04040204",
                path: "/ShareHoldersSystem/ShCommitteeMembersChange",
                component: "ShCommitteeMembersChange",
              },
              {
                id: "04040201",
                path: "/ShareHoldersSystem/ShCommitteeMeetings",
                component: "ShCommitteeMeetings",
              },
              {
                id: "04040202",
                path: "/ShareHoldersSystem/ShMembersPayments",
                component: "ShMembersPayments",
              },
              {
                id: "04040203",
                path: "/ShareHoldersSystem/ShCommMeetingsAttachmentVw",
                component: "ShCommMeetingsAttachmentVw",
              },
            ],
          },
          {
            id: "040403",
            subObjects: [
              {
                id: "04040301",
                path: "/ShareHoldersSystem/RepShCommitteeMeetings",
                component: "RepShCommitteeMeetings",
              },
              {
                id: "04040302",
                path: "/ShareHoldersSystem/RepShCommitteeMeetingsMembers",
                component: "RepShCommitteeMeetingsMembers",
              },
              {
                id: "04040303",
                path: "/ShareHoldersSystem/RepShMembersPaymentsDtls",
                component: "RepShMembersPaymentsDtls",
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "05",
    systemIcon: "MiscellaneousManagementSystem",
    subSystems: [
      //0501
      {
        id: "0501",
        subMenus: [
          {
            id: "050101",
            subObjects: [
              {
                id: "05010102",
                path: "/MiscellaneousManagementSystem/MisErrors",
                component: "MisErrors",
              },
              {
                id: "05010101",
                path: "/MiscellaneousManagementSystem/CompaniesSystemParamMiscellaneousManagementSystem",
                component: "CompaniesSystemParamMiscellaneousManagementSystem",
              },
            ],
          },
        ],
      },
      {
        id: "0502",
        subMenus: [
          {
            id: "050202",
            subObjects: [
              {
                id: "05020206",
                path: "/MiscellaneousManagementSystem/RepMisPaymentsRep",
                component: "RepMisPaymentsRep",
              },
              {
                id: "05020203",
                path: "/MiscellaneousManagementSystem/RepMisCampsSalesRepAmt",
                component: "RepMisCampsSalesRepAmt",
              },
              {
                id: "05020204",
                path: "/MiscellaneousManagementSystem/RepMisCampsSalesRepQty",
                component: "RepMisCampsSalesRepQty",
              },

              {
                id: "05020205",
                path: "/MiscellaneousManagementSystem/RepMisSultaSalesRepAmt",
                component: "RepMisSultaSalesRepAmt",
              },
              {
                id: "05020207",
                path: "/MiscellaneousManagementSystem/RepMisCampsSalesRepAmt2",
                component: "RepMisCampsSalesRepAmt2",
              },
              {
                id: "05020209",
                path: "/MiscellaneousManagementSystem/RepMisSultaSalesRepQty",
                component: "RepMisSultaSalesRepQty",
              },
              {
                id: "05020208",
                path: "/MiscellaneousManagementSystem/RepMisNewServicesDaysAvg",
                component: "RepMisNewServicesDaysAvg",
              },
              {
                id: "05020202",
                path: "/MiscellaneousManagementSystem/RepPaymentsSummary",
                component: "RepPaymentsSummary",
              },
            ],
          },
          {
            id: "050204",
            subObjects: [
              {
                id: "05020404",
                path: "/MiscellaneousManagementSystem/MisApplicationsGroupsSEVw",
                component: "MisApplicationsGroupsSEVw",
              },
              {
                id: "05020405",
                path: "/MiscellaneousManagementSystem/MisApplicationsGroupsBEVw",
                component: "MisApplicationsGroupsBEVw",
              },
              {
                id: "05020401",
                path: "/MiscellaneousManagementSystem/MisApplicationsRep",
                component: "MisApplicationsRep",
              },

              {
                id: "05020402",
                path: "/MiscellaneousManagementSystem/MisApplicationsGroupsSVw",
                component: "MisApplicationsGroupsSVw",
              },
              {
                id: "05020413",
                path: "/MiscellaneousManagementSystem/MisApplicationsItemsBVw",
                component: "MisApplicationsItemsBVw",
              },
              {
                id: "05020414",
                path: "/MiscellaneousManagementSystem/MisApplOutItemsBVw",
                component: "MisApplOutItemsBVw",
              },
              {
                id: "05020406",
                path: "/MiscellaneousManagementSystem/MisApplicationsGroupsDtlVw",
                component: "MisApplicationsGroupsDtlVw",
              },
              {
                id: "05020412",
                path: "/MiscellaneousManagementSystem/MisApplOutGroupsDtlVw",
                component: "MisApplOutGroupsDtlVw",
              },
              {
                id: "05020403",
                path: "/MiscellaneousManagementSystem/MisApplicationsGroupsBVw",
                component: "MisApplicationsGroupsBVw",
              },
              {
                id: "05020407",
                path: "/MiscellaneousManagementSystem/MisApplicationsOutstandings",
                component: "MisApplicationsOutstandings",
              },
              {
                id: "05020408",
                path: "/MiscellaneousManagementSystem/MisApplOutGroupsSVw",
                component: "MisApplOutGroupsSVw",
              },
              {
                id: "05020409",
                path: "/MiscellaneousManagementSystem/MisApplOutGroupsBVw",
                component: "MisApplOutGroupsBVw",
              },
              {
                id: "05020410",
                path: "/MiscellaneousManagementSystem/MisApplOutGroupsSEVw",
                component: "MisApplOutGroupsSEVw",
              },
              {
                id: "05020411",
                path: "/MiscellaneousManagementSystem/MisApplOutGroupsBEVw",
                component: "MisApplOutGroupsBEVw",
              },
            ],
          },
          {
            id: "050203",
            subObjects: [
              {
                id: "05020322",
                path: "/MiscellaneousManagementSystem/MisAttachmentTypes",
                component: "MisAttachmentTypes",
              },
              {
                id: "05020316",
                path: "/MiscellaneousManagementSystem/MisApplicationExecuteFlag",
                component: "MisApplicationExecuteFlag",
              },
              {
                id: "05020303",
                path: "/MiscellaneousManagementSystem/MisCentersGroups",
                component: "MisCentersGroups",
              },

              {
                id: "05020307",
                path: "/MiscellaneousManagementSystem/MisItems",
                component: "MisItems",
              },
              {
                id: "05020306",
                path: "/MiscellaneousManagementSystem/MisItemsGroups",
                component: "MisItemsGroups",
              },
              {
                id: "05020318",
                path: "/MiscellaneousManagementSystem/MisTransGroups",
                component: "MisTransGroups",
              },
              {
                id: "05020319",
                path: "/MiscellaneousManagementSystem/MisTransactions",
                component: "MisTransactions",
              },
              //
              {
                id: "05020321",
                path: "/MiscellaneousManagementSystem/MisTariffSlicesTypes",
                component: "MisTariffSlicesTypes",
              },
              {
                id: "05020304",
                path: "/MiscellaneousManagementSystem/MisCentersCollectionTypes",
                component: "MisCentersCollectionTypes",
              },
              {
                id: "05020305",
                path: "/MiscellaneousManagementSystem/MisCenters",
                component: "MisCenters",
              },
              {
                id: "05020309",
                path: "/MiscellaneousManagementSystem/MisCategoryTariff",
                component: "MisCategoryTariff",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCamps",
                component: "MisCamps",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisSultaAreas",
                component: "MisSultaAreas",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisAccountsGroups",
                component: "MisAccountsGroups",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisAccounts",
                component: "MisAccounts",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisMunicipalities",
                component: "MisMunicipalities",
              },

              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisImmovableTariff",
                component: "MisImmovableTariff",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisApplicationActivityFlag",
                component: "MisApplicationActivityFlag",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisTariffGroups",
                component: "MisTariffGroups",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisTariffTypes",
                component: "MisTariffTypes",
              },
              {
                id: "05020320",
                path: "/MiscellaneousManagementSystem/MisChangedSubBranches",
                component: "MisChangedSubBranches",
              },
            ],
          },
          {
            id: "050210",
            subObjects: [
              {
                id: "05021001",
                path: "/MiscellaneousManagementSystem/MisGreatesServicesYearlyVw",
                component: "MisGreatesServicesYearlyVw",
              },
              {
                id: "05021002",
                path: "/MiscellaneousManagementSystem/MisGreatesServicesSumVw",
                component: "MisGreatesServicesSumVw",
              },
              {
                id: "05021003",
                path: "/MiscellaneousManagementSystem/MisCampsOutstandingsYear",
                component: "MisCampsOutstandingsYear",
              },

              {
                id: "05021004",
                path: "/MiscellaneousManagementSystem/MisCampsOutstandings",
                component: "MisCampsOutstandings",
              },
              {
                id: "05021005",
                path: "/MiscellaneousManagementSystem/MisSultaOutstandings",
                component: "MisSultaOutstandings",
              },
              {
                id: "05021006",
                path: "/MiscellaneousManagementSystem/MisSultaOutstandingsYear",
                component: "MisSultaOutstandingsYear",
              },
              {
                id: "05021007",
                path: "/MiscellaneousManagementSystem/MisSultaOutstandingsBranch",
                component: "MisSultaOutstandingsBranch",
              },
              //
              {
                id: "05021008",
                path: "/MiscellaneousManagementSystem/MisSultaServices",
                component: "MisSultaServices",
              },
              {
                id: "05021009",
                path: "/MiscellaneousManagementSystem/MisSbranchOutstandings",
                component: "MisSbranchOutstandings",
              },
              {
                id: "05021010",
                path: "/MiscellaneousManagementSystem/MisSbranchOutstandingsYear",
                component: "MisSbranchOutstandingsYear",
              },
              {
                id: "05021011",
                path: "/MiscellaneousManagementSystem/MisBillingOutstandingsY",
                component: "MisBillingOutstandingsY",
              },
              {
                id: "05021012",
                path: "/MiscellaneousManagementSystem/MisSbranchesTariffOutVw",
                component: "MisSbranchesTariffOutVw",
              },
              {
                id: "05021013",
                path: "/MiscellaneousManagementSystem/MisBranchesCategoryOutVw",
                component: "MisBranchesCategoryOutVw",
              },
              {
                id: "05021014",
                path: "/MiscellaneousManagementSystem/MisBranchesCustTypeOutVw",
                component: "MisBranchesCustTypeOutVw",
              },
              {
                id: "05021015",
                path: "/MiscellaneousManagementSystem/MisBranchesTariffOutVw",
                component: "MisBranchesTariffOutVw",
              },
              {
                id: "05021016",
                path: "/MiscellaneousManagementSystem/MisSbranchOutstandingsMonthly",
                component: "MisSbranchOutstandingsMonthly",
              },

              {
                id: "05021017",
                path: "/MiscellaneousManagementSystem/MisBranchOutstandingsMonthly",
                component: "MisBranchOutstandingsMonthly",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCampsCurrOutstandingsVw",
                component: "MisCampsCurrOutstandingsVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisSultaCurrOutstandingsVw",
                component: "MisSultaCurrOutstandingsVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisBranchesAccountsVw",
                component: "MisBranchesAccountsVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisMunOutstandingsYear",
                component: "MisMunOutstandingsYear",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisMunOutstandings",
                component: "MisMunOutstandings",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisMunServices",
                component: "MisMunServices",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisBranchSultaCurrOutstVw",
                component: "MisBranchSultaCurrOutstVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisAgingBranchVw",
                component: "MisAgingBranchVw",
              },
              {
                id: "05021025",
                path: "/MiscellaneousManagementSystem/MisAgingTariffVw",
                component: "MisAgingTariffVw",
              },
              {
                id: "05021026",
                path: "/MiscellaneousManagementSystem/MisAgingTaxVw",
                component: "MisAgingTaxVw",
              },
              {
                id: "05021027",
                path: "/MiscellaneousManagementSystem/MisAgingServiceDtls",
                component: "MisAgingServiceDtls",
              },
              {
                id: "05021029",
                path: "/MiscellaneousManagementSystem/MisGreatesServicesSummary",
                component: "MisGreatesServicesSummary",
              },
              {
                id: "05021028",
                path: "/MiscellaneousManagementSystem/MisGreatesServicesAgingVw",
                component: "MisGreatesServicesAgingVw",
              },
            ],
          },
          //050220
          {
            id: "050220",
            subObjects: [
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherCreditParamVw",
                component: "MisVoucherCreditParamVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherAcctsCustRep",
                component: "MisVoucherAcctsCustRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherAccountsRep",
                component: "MisVoucherAccountsRep",
              },

              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherAccountsRepTariff",
                component: "MisVoucherAccountsRepTariff",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherAccountsRepBsVw",
                component: "MisVoucherAccountsRepBsVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherAccountsRepBvVw",
                component: "MisVoucherAccountsRepBvVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherAccountsRepBVw",
                component: "MisVoucherAccountsRepBVw",
              },
              //
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisVoucherAccountsRepGVw",
                component: "MisVoucherAccountsRepGVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCreditAccountsRep",
                component: "MisCreditAccountsRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCreditAccountsCustRep",
                component: "MisCreditAccountsCustRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCreditAccountsRepBVw",
                component: "MisCreditAccountsRepBVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCreditAccountsRepTariff",
                component: "MisCreditAccountsRepTariff",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCreditAccountsRepGVw",
                component: "MisCreditAccountsRepGVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisGreatInvoicesRep",
                component: "MisGreatInvoicesRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisImmovableType",
                component: "MisImmovableType",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCampsAccountingRepVw",
                component: "MisCampsAccountingRepVw",
              },

              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisOutstandingsCampsRep",
                component: "MisOutstandingsCampsRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisOutstandingsRep",
                component: "MisOutstandingsRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisLoadSultaPayments",
                component: "MisLoadSultaPayments",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisLoadSultaPaymentsPrev",
                component: "MisLoadSultaPaymentsPrev",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/RepMisLoadSultaPayments",
                component: "RepMisLoadSultaPayments",
              },
              {
                id: "05022018",
                path: "/MiscellaneousManagementSystem/MisVouchersLog",
                component: "MisVouchersLog",
              },
              {
                id: "05022019",
                path: "/MiscellaneousManagementSystem/MisCompanyServicesSales",
                component: "MisCompanyServicesSales",
              },
            ],
          },

          {
            id: "050230",
            subObjects: [
              {
                id: "05023001",
                path: "/MiscellaneousManagementSystem/MisPrvPaymentsVw",
                component: "MisPrvPaymentsVw",
              },
              {
                id: "05023002",
                path: "/MiscellaneousManagementSystem/MisChecksCollectionsRep",
                component: "MisChecksCollectionsRep",
              },
              {
                id: "05023003",
                path: "/MiscellaneousManagementSystem/MisChecksCollectionsRepVw",
                component: "MisChecksCollectionsRepVw",
              },

              {
                id: "05023004",
                path: "/MiscellaneousManagementSystem/MisPaymentsRepBCtVw",
                component: "MisPaymentsRepBCtVw",
              },
              {
                id: "05023005",
                path: "/MiscellaneousManagementSystem/MisPaymentsNewRep",
                component: "MisPaymentsNewRep",
              },
              {
                id: "05023006",
                path: "/MiscellaneousManagementSystem/MisPaymentsRepBGVw",
                component: "MisPaymentsRepBGVw",
              },
              {
                id: "05023007",
                path: "/MiscellaneousManagementSystem/MisPaymentsRepBBVw",
                component: "MisPaymentsRepBBVw",
              },
              //
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisPaymentsPerMonthVw",
                component: "MisPaymentsPerMonthVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisPaymentsParamAggregate",
                component: "MisPaymentsParamAggregate",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisPaymentsAccountsRep",
                component: "MisPaymentsAccountsRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCollectionsParamRep",
                component: "MisCollectionsParamRep",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisCollectionsStatParam",
                component: "MisCollectionsStatParam",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisPaymentsRep2",
                component: "MisPaymentsRep2",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisPaymentsRep2TVw",
                component: "MisPaymentsRep2TVw",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisPaymentsRep2CVw",
                component: "MisPaymentsRep2CVw",
              },
              {
                id: "05023015",
                path: "/MiscellaneousManagementSystem/MisUnpostedPayments",
                component: "MisUnpostedPayments",
              },
            ],
          },

          {
            id: "050299",
            subObjects: [
              {
                id: "05029901",
                path: "/MiscellaneousManagementSystem/MisPrepaidMeterTypes",
                component: "MisPrepaidMeterTypes",
              },
              {
                id: "05029902",
                path: "/MiscellaneousManagementSystem/MisPrepaidVendingStations",
                component: "MisPrepaidVendingStations",
              },
              {
                id: "05029903",
                path: "/MiscellaneousManagementSystem/MisPrepaidUsers",
                component: "MisPrepaidUsers",
              },

              {
                id: "05029904",
                path: "/MiscellaneousManagementSystem/MisPrepaidInitialReadings",
                component: "MisPrepaidInitialReadings",
              },
              {
                id: "05029905",
                path: "/MiscellaneousManagementSystem/MisPrepaidInitialReadingsH",
                component: "MisPrepaidInitialReadingsH",
              },
              {
                id: "05029906",
                path: "/MiscellaneousManagementSystem/MisLoadPrepaidPayments",
                component: "MisLoadPrepaidPayments",
              },
              {
                id: "05029907",
                path: "/MiscellaneousManagementSystem/MisDoUntransPrepaidPayments",
                component: "MisDoUntransPrepaidPayments",
              },

              {
                id: "05029908",
                path: "/MiscellaneousManagementSystem/MisPrepaidPaymentsFileH",
                component: "MisPrepaidPaymentsFileH",
              },
            ],
          },

          {
            id: "050205",
            subObjects: [
              {
                id: "05020523",
                path: "/MiscellaneousManagementSystem/MisChangedMetersSummaryRep",
                component: "MisChangedMetersSummaryRep",
              },
              {
                id: "05020510",
                path: "/MiscellaneousManagementSystem/MisBillingErrorsLogVw",
                component: "MisBillingErrorsLogVw",
              },
              {
                id: "05020519",
                path: "/MiscellaneousManagementSystem/MisTariffSlicesRep",
                component: "MisTariffSlicesRep",
              },

              {
                id: "05020520",
                path: "/MiscellaneousManagementSystem/MisTariffSlicesSummaryRep",
                component: "MisTariffSlicesSummaryRep",
              },
              {
                id: "05020521",
                path: "/MiscellaneousManagementSystem/MisCampConsQtyRep",
                component: "MisCampConsQtyRep",
              },
              {
                id: "05020522",
                path: "/MiscellaneousManagementSystem/MisChangedMetersRep",
                component: "MisChangedMetersRep",
              },
              {
                id: "05020524",
                path: "/MiscellaneousManagementSystem/MisCampSalesQtyRep",
                component: "MisCampSalesQtyRep",
              },

              {
                id: "05020507",
                path: "/MiscellaneousManagementSystem/MisMetersRep",
                component: "MisMetersRep",
              },
              {
                id: "05020508",
                path: "/MiscellaneousManagementSystem/MisMetersSummary",
                component: "MisMetersSummary",
              },
              {
                id: "05020509",
                path: "/MiscellaneousManagementSystem/MisDisconnectedServicesRep",
                component: "MisDisconnectedServicesRep",
              },

              {
                id: "05020525",
                path: "/MiscellaneousManagementSystem/MisNotInvoicedServicesVw",
                component: "MisNotInvoicedServicesVw",
              },
              //
              {
                id: "05020515",
                path: "/MiscellaneousManagementSystem/MisPrepaidVoucherTemp",
                component: "MisPrepaidVoucherTemp",
              },
              {
                id: "05020516",
                path: "/MiscellaneousManagementSystem/MisConsumptionPerPeriod",
                component: "MisConsumptionPerPeriod",
              },
              {
                id: "05020517",
                path: "/MiscellaneousManagementSystem/MisAccreditServices",
                component: "MisAccreditServices",
              },

              {
                id: "05020511",
                path: "/MiscellaneousManagementSystem/MisServicesTaxError",
                component: "MisServicesTaxError",
              },
              //
              {
                id: "05020512",
                path: "/MiscellaneousManagementSystem/MisServicesTaxErrorMonth",
                component: "MisServicesTaxErrorMonth",
              },
              {
                id: "05020513",
                path: "/MiscellaneousManagementSystem/MisServicesEmptyCards",
                component: "MisServicesEmptyCards",
              },
              {
                id: "05020514",
                path: "/MiscellaneousManagementSystem/MisServicesTariffError",
                component: "MisServicesTariffError",
              },
              {
                id: "05020518",
                path: "/MiscellaneousManagementSystem/MisQtyRep",
                component: "MisQtyRep",
              },
              //
              {
                id: "05020501",
                path: "/MiscellaneousManagementSystem/MisAgreementsConsumptions",
                component: "MisAgreementsConsumptions",
              },
              {
                id: "05020502",
                path: "/MiscellaneousManagementSystem/MisAgreementsConsumptionsRep",
                component: "MisAgreementsConsumptionsRep",
              },
              {
                id: "05020503",
                path: "/MiscellaneousManagementSystem/MisGreatesServices",
                component: "MisGreatesServices",
              },
              {
                id: "05020504",
                path: "/MiscellaneousManagementSystem/MisServicesQuery",
                component: "MisServicesQuery",
              },
              {
                id: "05020505",
                path: "/MiscellaneousManagementSystem/MisPrepaidServicesOutstandings",
                component: "MisPrepaidServicesOutstandings",
              },
              {
                id: "05020506",
                path: "/MiscellaneousManagementSystem/MisSbranchesAgreements",
                component: "MisSbranchesAgreements",
              },
            ],
          },

          {
            id: "050235",
            subObjects: [
              {
                id: "05023505",
                path: "/MiscellaneousManagementSystem/MisPostDatedChecksRep",
                component: "MisPostDatedChecksRep",
              },
              {
                id: "05023501",
                path: "/MiscellaneousManagementSystem/MisReturnedChecks",
                component: "MisReturnedChecks",
              },
              {
                id: "05023502",
                path: "/MiscellaneousManagementSystem/MisReturnedChecksInfoVw",
                component: "MisReturnedChecksInfoVw",
              },

              {
                id: "05023503",
                path: "/MiscellaneousManagementSystem/MisReturnedChecksRep",
                component: "MisReturnedChecksRep",
              },
              {
                id: "05023504",
                path: "/MiscellaneousManagementSystem/MisReturnedChecksBVw",
                component: "MisReturnedChecksBVw",
              },
              {
                id: "05023506",
                path: "/MiscellaneousManagementSystem/MisPostDatedChecks",
                component: "MisPostDatedChecks",
              },
            ],
          },

          {
            id: "050240",
            subObjects: [
              {
                id: "05024001",
                path: "/MiscellaneousManagementSystem/MisDepositsRep",
                component: "MisDepositsRep",
              },
            ],
          },

          {
            id: "050250",
            subObjects: [
              {
                id: "05025006",
                path: "/MiscellaneousManagementSystem/MisSultaCompensationTypes",
                component: "MisSultaCompensationTypes",
              },
              {
                id: "05025007",
                path: "/MiscellaneousManagementSystem/MisSultaCompensationParam",
                component: "MisSultaCompensationParam",
              },
              {
                id: "05025001",
                path: "/MiscellaneousManagementSystem/MisSultaRequests",
                component: "MisSultaRequests",
              },

              {
                id: "05025002",
                path: "/MiscellaneousManagementSystem/MisSultaRequestsSingle",
                component: "MisSultaRequestsSingle",
              },
              {
                id: "05025003",
                path: "/MiscellaneousManagementSystem/MisSultaRequestsSummaryVw",
                component: "MisSultaRequestsSummaryVw",
              },
              {
                id: "05025004",
                path: "/MiscellaneousManagementSystem/MisSultaPayments",
                component: "MisSultaPayments",
              },
              {
                id: "05025005",
                path: "/MiscellaneousManagementSystem/MisSultaRequestsPrivs",
                component: "MisSultaRequestsPrivs",
              },
              {
                id: "05025000",
                path: "/MiscellaneousManagementSystem/MisRequestTypes",
                component: "MisRequestTypes",
              },
            ],
          },
        ],
      },

      {
        id: "0504",
        subMenus: [
          {
            id: "050402",
            subObjects: [
              {
                id: "05040201",
                path: "/MiscellaneousManagementSystem/MisLoadPpcReadings",
                component: "MisLoadPpcReadings",
              },
              {
                id: "05040202",
                path: "/MiscellaneousManagementSystem/MisReviewPpcReading",
                component: "MisReviewPpcReading",
              },
            ],
          },
        ],
      },

      {
        id: "0505",
        subMenus: [
          {
            id: "050501",
            subObjects: [
              {
                id: "05050101",
                path: "/MiscellaneousManagementSystem/MisSavingCampaign",
                component: "MisSavingCampaign",
              },
            ],
          },
          {
            id: "050502",
            subObjects: [
              {
                id: "05050205",
                path: "/MiscellaneousManagementSystem/MisDuesFollowAgrDtlsMVw",
                component: "MisDuesFollowAgrDtlsMVw",
              },
              {
                id: "05050203",
                path: "/MiscellaneousManagementSystem/MisDuesFollowup",
                component: "MisDuesFollowup",
              },
              {
                id: "05050204",
                path: "/MiscellaneousManagementSystem/MisPrepareDuesFollowupDtls",
                component: "MisPrepareDuesFollowupDtls",
              },
            ],
          },
          {
            id: "050503",
            subObjects: [
              {
                id: "05050302",
                path: "/MiscellaneousManagementSystem/MisDuesFollowSummary",
                component: "MisDuesFollowSummary",
              },
            ],
          },
        ],
      },
      //0506
      {
        id: "0506",
        subMenus: [
          {
            id: "050601",
            subObjects: [
              {
                id: "05060103",
                path: "/MiscellaneousManagementSystem/RepMisInterestMaster",
                component: "RepMisInterestMaster",
              },
              {
                id: "05060101",
                path: "/MiscellaneousManagementSystem/MisInterestRates",
                component: "MisInterestRates",
              },
              {
                id: "05060102",
                path: "/MiscellaneousManagementSystem/MisInterestMaster",
                component: "MisInterestMaster",
              },
            ],
          },
        ],
      },
      //0507
      {
        id: "0507",
        subMenus: [
          {
            id: "050701",
            subObjects: [
              {
                id: "05070103",
                path: "/MiscellaneousManagementSystem/MisAuditYear",
                component: "MisAuditYear",
              },
              {
                id: "05070106",
                path: "/MiscellaneousManagementSystem/MisGreatesServiceBalanceVw",
                component: "MisGreatesServiceBalanceVw",
              },
              {
                id: "05070104",
                path: "/MiscellaneousManagementSystem/MisCreditNotesAccParam",
                component: "MisCreditNotesAccParam",
              },
              {
                id: "05070105",
                path: "/MiscellaneousManagementSystem/MisVouchersMisDedParam",
                component: "MisVouchersMisDedParam",
              },
              {
                id: "05070102",
                path: "/MiscellaneousManagementSystem/MisGlAggregateParamRep",
                component: "MisGlAggregateParamRep",
              },
              {
                id: "05070101",
                path: "/MiscellaneousManagementSystem/MisGreatesServicesVw",
                component: "MisGreatesServicesVw",
              },
            ],
          },
        ],
      },
      //0508
        {
        id: "0508",
        subMenus: [
          {
            id: "050801",
            subObjects: [
              {
                id: "05080103",
                path: "/MiscellaneousManagementSystem/MisSultaPaymentsCenters",
                component: "MisSultaPaymentsCenters",
              },
              {
                id: "05080101",
                path: "/MiscellaneousManagementSystem/MisGreatesServicesCamp",
                component: "MisGreatesServicesCamp",
              },
              {
                id: "05080102",
                path: "/MiscellaneousManagementSystem/MisChecksParamRep",
                component: "MisChecksParamRep",
              },
             
            ],
          },
        ],
      },
      //0509
      {
        id: "0509",
        subMenus: [
          {
            id: "050901",
            subObjects: [
              {
                id: "05090101",
                path: "/MiscellaneousManagementSystem/MisWcTariffGroups",
                component: "MisWcTariffGroups",
              },
              {
                id: "05090102",
                path: "/MiscellaneousManagementSystem/MisWcTariffTypes",
                component: "MisWcTariffTypes",
              },
             
             
            ],
          }, {
            id: "050902",
            subObjects: [
              {
                id: "05090201",
                path: "/MiscellaneousManagementSystem/MisWCapitalParam",
                component: "MisWCapitalParam",
              },
             
             
            ],
          },
        ],
      },
      //0510
       {
        id: "0510",
        subMenus: [
          {
            id: "051001",
            subObjects: [
              {
                id: "05100101",
                path: "/MiscellaneousManagementSystem/MisIfrs9TariffGroups",
                component: "MisIfrs9TariffGroups",
              },
              {
                id: "05100102",
                path: "/MiscellaneousManagementSystem/MisIfrs9TariffTypes",
                component: "MisIfrs9TariffTypes",
              },
             
             
            ],
          },
            {
            id: "051002",
            subObjects: [
              {
                id: "05100201",
                path: "/MiscellaneousManagementSystem/MisIfrs9Param",
                component: "MisIfrs9Param",
              },
             
             
             
            ],
          },
        ],
      },
      //0511
        {
        id: "0511",
        subMenus: [
          {
            id: "051101",
            subObjects: [
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisFpmTariffGroups",
                component: "MisFpmTariffGroups",
              },
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisFpmTariffTypes",
                component: "MisFpmTariffTypes",
              }, {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisFpmAccountsGroups",
                component: "MisFpmAccountsGroups",
              }, {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisFpmDeductionsGroups",
                component: "MisFpmDeductionsGroups",
              },{
                id: "********",
                path: "/MiscellaneousManagementSystem/MisFpmAccounts",
                component: "MisFpmAccounts",
              }, {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisFpmDeductions",
                component: "MisFpmDeductions",
              },
             
            ],
          },  {
            id: "051102",
            subObjects: [
              {
                id: "********",
                path: "/MiscellaneousManagementSystem/MisFpmParam",
                component: "MisFpmParam",
              },
             
             
            ],
          },
          
        ],
      },
    ],
  },
  //06
   {
    id: "06",
    systemIcon: "ElectricianLicenseSystem",
    subSystems: [
      //0601
      {
        id: "0601",
        subMenus: [
          {
            id: "060101",
            subObjects: [
              {
                id: "********",
                path: "/ElectricianLicenseSystem/ElcStaff",
                component: "ElcStaff",
              },
              {
                id: "********",
                path: "/ElectricianLicenseSystem/ElcLicensePrefix",
                component: "ElcLicensePrefix",
              },
              {
                id: "06010105",
                path: "/ElectricianLicenseSystem/ElcElectricianTypes",
                component: "ElcElectricianTypes",
              },
              {
                id: "06010107",
                path: "/ElectricianLicenseSystem/ElcApplicationTypes",
                component: "ElcApplicationTypes",
              },
              {
                id: "06010110",
                path: "/ElectricianLicenseSystem/CompaniesSystemParamElectricianLicenseSystem",
                component: "CompaniesSystemParamElectricianLicenseSystem",
              },
              {
                id: "06010109",
                path: "/ElectricianLicenseSystem/ElcAwardedLicense",
                component: "ElcAwardedLicense",
              },
              //
               {
                id: "06010108",
                path: "/ElectricianLicenseSystem/ElcApplicationStatus",
                component: "ElcApplicationStatus",
              },
              {
                id: "06010106",
                path: "/ElectricianLicenseSystem/ElcElectricianStatus",
                component: "ElcElectricianStatus",
              },
              {
                id: "06010104",
                path: "/ElectricianLicenseSystem/ElcLicenseTypes",
                component: "ElcLicenseTypes",
              },
              {
                id: "06010102",
                path: "/ElectricianLicenseSystem/ElcQualificationTypes",
                component: "ElcQualificationTypes",
              },
               {
                id: "06010111",
                path: "/ElectricianLicenseSystem/ElcExtraLicenseTypes",
                component: "ElcExtraLicenseTypes",
              },
              {
                id: "06010113",
                path: "/ElectricianLicenseSystem/ElcOpenNewCourse",
                component: "ElcOpenNewCourse",
              },
              {
                id: "06010112",
                path: "/ElectricianLicenseSystem/ElcDocumentTypes",
                component: "ElcDocumentTypes",
              },
              {
                id: "06010114",
                path: "/ElectricianLicenseSystem/ElcMessageDrafts",
                component: "ElcMessageDrafts",
              },
            ],
          }, {
            id: "060102",
            subObjects: [
              {
                id: "06010205",
                path: "/ElectricianLicenseSystem/ElcElectricians",
                component: "ElcElectricians",
              },
              {
                id: "06010204",
                path: "/ElectricianLicenseSystem/ElcApplicationFinalApproval",
                component: "ElcApplicationFinalApproval",
              },
              {
                id: "06010203",
                path: "/ElectricianLicenseSystem/ElcSessionApplications",
                component: "ElcSessionApplications",
              },
              {
                id: "06010202",
                path: "/ElectricianLicenseSystem/ElcPreparedSessions",
                component: "ElcPreparedSessions",
              },
              {
                id: "06010201",
                path: "/ElectricianLicenseSystem/ElcApplications",
                component: "ElcApplications",
              },
              {
                id: "06010207",
                path: "/ElectricianLicenseSystem/ElcElectricianInfoChange",
                component: "ElcElectricianInfoChange",
              },
              //
               {
                id: "06010206",
                path: "/ElectricianLicenseSystem/ElcApplicationRenewApproval",
                component: "ElcApplicationRenewApproval",
              },
              {
                id: "06010208",
                path: "/ElectricianLicenseSystem/ElcElectriciansManual",
                component: "ElcElectriciansManual",
              },
              {
                id: "06010209",
                path: "/ElectricianLicenseSystem/ElcSessionApplicationsAdmin",
                component: "ElcSessionApplicationsAdmin",
              },
              {
                id: "06010215",
                path: "/ElectricianLicenseSystem/ElcElectriciansQuery",
                component: "ElcElectriciansQuery",
              },
               {
                id: "06010216",
                path: "/ElectricianLicenseSystem/ElcApplicationsQuery",
                component: "ElcApplicationsQuery",
              },
              {
                id: "06010211",
                path: "/ElectricianLicenseSystem/ElcElectricianCardsByUser",
                component: "ElcElectricianCardsByUser",
              },
              {
                id: "06010210",
                path: "/ElectricianLicenseSystem/ElcElectricianCards",
                component: "ElcElectricianCards",
              },
              {
                id: "06010213",
                path: "/ElectricianLicenseSystem/ElcSessionApplicationsVw",
                component: "ElcSessionApplicationsVw",
              },

              {
                id: "06010212",
                path: "/ElectricianLicenseSystem/ElcElectricianDeleted",
                component: "ElcElectricianDeleted",
              },
              {
                id: "06010214",
                path: "/ElectricianLicenseSystem/ElcElectriciansDocuments",
                component: "ElcElectriciansDocuments",
              },
              {
                id: "06010217",
                path: "/ElectricianLicenseSystem/ElcElectriciansDataQuery",
                component: "ElcElectriciansDataQuery",
              },
              {
                id: "06010218",
                path: "/ElectricianLicenseSystem/ElcElecServices",
                component: "ElcElecServices",
              },
            ],
          },
           {
            id: "060103",
            subObjects: [
              {
                id: "06010301",
                path: "/ElectricianLicenseSystem/RepElcApplications",
                component: "RepElcApplications",
              },
              {
                id: "06010303",
                path: "/ElectricianLicenseSystem/RepElcApplicationsSummary",
                component: "RepElcApplicationsSummary",
              },
              {
                id: "06010305",
                path: "/ElectricianLicenseSystem/RepElcSessionResults",
                component: "RepElcSessionResults",
              },
              {
                id: "06010310",
                path: "/ElectricianLicenseSystem/RepElcElectricianCard",
                component: "RepElcElectricianCard",
              },
              {
                id: "06010309",
                path: "/ElectricianLicenseSystem/RepElcElectricianLicLog",
                component: "RepElcElectricianLicLog",
              },
              {
                id: "06010308",
                path: "/ElectricianLicenseSystem/RepElcElectricianInfoChange",
                component: "RepElcElectricianInfoChange",
              },
              //
               {
                id: "06010307",
                path: "/ElectricianLicenseSystem/RepElcElectriciansSummary",
                component: "RepElcElectriciansSummary",
              },
              {
                id: "06010306",
                path: "/ElectricianLicenseSystem/RepElcElectriciansList",
                component: "RepElcElectriciansList",
              },
              {
                id: "06010302",
                path: "/ElectricianLicenseSystem/RepElcApplicationsByElec",
                component: "RepElcApplicationsByElec",
              },
              {
                id: "06010312",
                path: "/ElectricianLicenseSystem/RepElcElectricianCertificate",
                component: "RepElcElectricianCertificate",
              },
               {
                id: "06010311",
                path: "/ElectricianLicenseSystem/RepElcApplicationsDtls",
                component: "RepElcApplicationsDtls",
              },
              {
                id: "06010314",
                path: "/ElectricianLicenseSystem/RepElcApplicationsDtlsPaid",
                component: "RepElcApplicationsDtlsPaid",
              },
              {
                id: "06010313",
                path: "/ElectricianLicenseSystem/RepElcElectricianCards",
                component: "RepElcElectricianCards",
              },
              {
                id: "06010315",
                path: "/ElectricianLicenseSystem/RepElcElectricianExtraLic",
                component: "RepElcElectricianExtraLic",
              },

            ],
          },
        ],
      },
      //0602
     
       {
        id: "0602",
        subMenus: [
          {
            id: "060201",
            subObjects: [
              {
                id: "06020102",
                path: "/ElectricianLicenseSystem/ElcTestPoints",
                component: "ElcTestPoints",
              },
              {
                id: "06020103",
                path: "/ElectricianLicenseSystem/ElcBranchGroups",
                component: "ElcBranchGroups",
              },
              {
                id: "06020104",
                path: "/ElectricianLicenseSystem/ElcHolidays",
                component: "ElcHolidays",
              },
              {
                id: "06020105",
                path: "/ElectricianLicenseSystem/ElcTestResults",
                component: "ElcTestResults",
              },
              {
                id: "06020106",
                path: "/ElectricianLicenseSystem/ElcTestTypes",
                component: "ElcTestTypes",
              },
              {
                id: "06020109",
                path: "/ElectricianLicenseSystem/ElcTestExceptionTypes",
                component: "ElcTestExceptionTypes",
              },
              //
               {
                id: "06020108",
                path: "/ElectricianLicenseSystem/ElcFacilityTypesExtras",
                component: "ElcFacilityTypesExtras",
              },
              {
                id: "06020107",
                path: "/ElectricianLicenseSystem/ElcFacilityTypes",
                component: "ElcFacilityTypes",
              },
              {
                id: "06020111",
                path: "/ElectricianLicenseSystem/ElcLookupTables",
                component: "ElcLookupTables",
              },
              {
                id: "06020112",
                path: "/ElectricianLicenseSystem/ElcTestGroups",
                component: "ElcTestGroups",
              },
             
            ],
          }, {
            id: "060203",
            subObjects: [
              {
                id: "06020301",
                path: "/ElectricianLicenseSystem/RepElcTestSummaryRep",
                component: "RepElcTestSummaryRep",
              },
              {
                id: "06020307",
                path: "/ElectricianLicenseSystem/RepElcTestServicesTypeNew",
                component: "RepElcTestServicesTypeNew",
              },
              {
                id: "06020306",
                path: "/ElectricianLicenseSystem/RepElcTestServicesDtls",
                component: "RepElcTestServicesDtls",
              },
              {
                id: "06020310",
                path: "/ElectricianLicenseSystem/RepElcTestServicesSchedual",
                component: "RepElcTestServicesSchedual",
              },
              {
                id: "06020311",
                path: "/ElectricianLicenseSystem/RepElcTestBoards",
                component: "RepElcTestBoards",
              },
              {
                id: "06020312",
                path: "/ElectricianLicenseSystem/RepElcTestServicesExceptions",
                component: "RepElcTestServicesExceptions",
              },
              //
               {
                id: "06020313",
                path: "/ElectricianLicenseSystem/RepElcTestTeamSummary",
                component: "RepElcTestTeamSummary",
              },
              {
                id: "06020314",
                path: "/ElectricianLicenseSystem/RepElcTestServicesNotConn",
                component: "RepElcTestServicesNotConn",
              },
              {
                id: "06020315",
                path: "/ElectricianLicenseSystem/RepElcTestTeamSummaryTeam",
                component: "RepElcTestTeamSummaryTeam",
              },
              {
                id: "06020316",
                path: "/ElectricianLicenseSystem/RepElcTestTeamSummaryMonthly",
                component: "RepElcTestTeamSummaryMonthly",
              },
               {
                id: "06020303",
                path: "/ElectricianLicenseSystem/RepElcTestServicesGroupsVw",
                component: "RepElcTestServicesGroupsVw",
              },
              {
                id: "06020304",
                path: "/ElectricianLicenseSystem/RepElcTestServicesGroups",
                component: "RepElcTestServicesGroups",
              },
              {
                id: "06020305",
                path: "/ElectricianLicenseSystem/RepElcTestServicesGroupsSb",
                component: "RepElcTestServicesGroupsSb",
              },
              {
                id: "06020302",
                path: "/ElectricianLicenseSystem/RepElcTestServices",
                component: "RepElcTestServices",
              },

              {
                id: "06020309",
                path: "/ElectricianLicenseSystem/RepElcTestServicesTempConn",
                component: "RepElcTestServicesTempConn",
              },
              {
                id: "06020308",
                path: "/ElectricianLicenseSystem/RepElcTestDaysAvg",
                component: "RepElcTestDaysAvg",
              },
              {
                id: "06020317",
                path: "/ElectricianLicenseSystem/RepElcTestServicesDaily",
                component: "RepElcTestServicesDaily",
              },
            
            ],
          },
           {
            id: "060202",
            subObjects: [
              {
                id: "06020203",
                path: "/ElectricianLicenseSystem/ElcTestServicesResult",
                component: "ElcTestServicesResult",
              },
              {
                id: "06020207",
                path: "/ElectricianLicenseSystem/ElcTestServicesResultBQuery",
                component: "ElcTestServicesResultBQuery",
              },
              {
                id: "06020208",
                path: "/ElectricianLicenseSystem/ElcTestExceptionReview",
                component: "ElcTestExceptionReview",
              },
              {
                id: "06020209",
                path: "/ElectricianLicenseSystem/ElcTestServicesUndownloaded",
                component: "ElcTestServicesUndownloaded",
              },
              {
                id: "06020201",
                path: "/ElectricianLicenseSystem/ElcTestServices",
                component: "ElcTestServices",
              },
              {
                id: "06020202",
                path: "/ElectricianLicenseSystem/ElcTestServicesResultAgree",
                component: "ElcTestServicesResultAgree",
              },
              //
               {
                id: "06020206",
                path: "/ElectricianLicenseSystem/ElcTestServicesResultQuery",
                component: "ElcTestServicesResultQuery",
              },
              {
                id: "06020205",
                path: "/ElectricianLicenseSystem/ElcTestServicesStatVw",
                component: "ElcTestServicesStatVw",
              },
             
            ],
          },

           {
            id: "060204",
            subObjects: [
              {
                id: "06020401",
                path: "/ElectricianLicenseSystem/ElcTestServicesActions",
                component: "ElcTestServicesActions",
              },
             
            ],
          },
        ],
      },
    ],
  },
  //09
  {
    id: "09",
    systemIcon: "JudiciarySystem",
    subSystems: [
      //0901
      {
        id: "0901",
        subMenus: [
          {
            id: "090101",
            subObjects: [
              {
                id: "09010108",
                path: "/JudiciarySystem/JudPaymentTypes",
                component: "JudPaymentTypes",
              },
              {
                id: "09010105",
                path: "/JudiciarySystem/JudFeesCodes",
                component: "JudFeesCodes",
              }, {
                id: "09010101",
                path: "/JudiciarySystem/JudActionCodes",
                component: "JudActionCodes",
              },
              {
                id: "09010103",
                path: "/JudiciarySystem/JudCaseTypes",
                component: "JudCaseTypes",
              }, {
                id: "09010100",
                path: "/JudiciarySystem/CompaniesSystemParamJudiciarySystem",
                component: "CompaniesSystemParamJudiciarySystem",
              },
              {
                id: "09010104",
                path: "/JudiciarySystem/JudLawyers",
                component: "JudLawyers",
              },
              //
               {
                id: "09010102",
                path: "/JudiciarySystem/JudCaseStatus",
                component: "JudCaseStatus",
              },
              {
                id: "09010106",
                path: "/JudiciarySystem/JudLawyerTypes",
                component: "JudLawyerTypes",
              }, {
                id: "09010111",
                path: "/JudiciarySystem/JudCaseStages",
                component: "JudCaseStages",
              },
              {
                id: "09010109",
                path: "/JudiciarySystem/JudLawyerGuarantees",
                component: "JudLawyerGuarantees",
              },
              //
              {
                id: "09010110",
                path: "/JudiciarySystem/JudErrors",
                component: "JudErrors",
              },
              {
                id: "09010112",
                path: "/JudiciarySystem/JudCaseAttachmentTypes",
                component: "JudCaseAttachmentTypes",
              }, {
                id: "09010115",
                path: "/JudiciarySystem/JudLawyerAttachmentTypes",
                component: "JudLawyerAttachmentTypes",
              },
              {
                id: "09010114",
                path: "/JudiciarySystem/JudCaseStatusQuery",
                component: "JudCaseStatusQuery",
              },
              
            ],
          },
          //090102
          {
            id: "090102",
            subObjects: [
              {
                id: "09010222",
                path: "/JudiciarySystem/JudLawyerPayments",
                component: "JudLawyerPayments",
              },
              {
                id: "09010201",
                path: "/JudiciarySystem/JudCases",
                component: "JudCases",
              }, {
                id: "09010202",
                path: "/JudiciarySystem/JudCasesInfoChange",
                component: "JudCasesInfoChange",
              },
              {
                id: "09010204",
                path: "/JudiciarySystem/JudCasesUpdated",
                component: "JudCasesUpdated",
              }, {
                id: "09010226",
                path: "/JudiciarySystem/JudCasesEmptyAgreements",
                component: "JudCasesEmptyAgreements",
              },
              {
                id: "09010213",
                path: "/JudiciarySystem/JudCasesNewUpdates",
                component: "JudCasesNewUpdates",
              },
              //
               {
                id: "09010214",
                path: "/JudiciarySystem/JudCasesQueryTemp",
                component: "JudCasesQueryTemp",
              },
              {
                id: "09010216",
                path: "/JudiciarySystem/JudCasesBranch",
                component: "JudCasesBranch",
              }, {
                id: "09010203",
                path: "/JudiciarySystem/JudCasesChangeStatus",
                component: "JudCasesChangeStatus",
              },
              {
                id: "09010220",
                path: "/JudiciarySystem/JudCasesLate",
                component: "JudCasesLate",
              },
              //
              {
                id: "09010221",
                path: "/JudiciarySystem/JudServicesQueryRead",
                component: "JudServicesQueryRead",
              },
              {
                id: "09010223",
                path: "/JudiciarySystem/JudLawyerCredit",
                component: "JudLawyerCredit",
              }, {
                id: "09010228",
                path: "/JudiciarySystem/JudFeesSheetMaster",
                component: "JudFeesSheetMaster",
              },
              {
                id: "09010217",
                path: "/JudiciarySystem/JudCasesAdded",
                component: "JudCasesAdded",
              },
              //
               {
                id: "09010218",
                path: "/JudiciarySystem/JudCasesTypeLog",
                component: "JudCasesTypeLog",
              },
              {
                id: "09010219",
                path: "/JudiciarySystem/JudMultiCasesServicesVw",
                component: "JudMultiCasesServicesVw",
              }, {
                id: "09010227",
                path: "/JudiciarySystem/JudLoadLawyerData",
                component: "JudLoadLawyerData",
              },
              {
                id: "09010207",
                path: "/JudiciarySystem/JudCasesL",
                component: "JudCasesL",
              },
              //
               {
                id: "09010209",
                path: "/JudiciarySystem/JudServicesQuerySavedRev",
                component: "JudServicesQuerySavedRev",
              },
              {
                id: "09010205",
                path: "/JudiciarySystem/JudServicesQuery",
                component: "JudServicesQuery",
              }, {
                id: "09010206",
                path: "/JudiciarySystem/JudServicesQuerySaved",
                component: "JudServicesQuerySaved",
              },
              {
                id: "09010212",
                path: "/JudiciarySystem/JudServicesApprovedCasesVw",
                component: "JudServicesApprovedCasesVw",
              },
              //
                {
                id: "09010211",
                path: "/JudiciarySystem/JudCasesIsCourtVw",
                component: "JudCasesIsCourtVw",
              },
              {
                id: "09010210",
                path: "/JudiciarySystem/JudServicesQuerySavedT",
                component: "JudServicesQuerySavedT",
              }, {
                id: "09010208",
                path: "/JudiciarySystem/JudCasesActionsTemp",
                component: "JudCasesActionsTemp",
              },
              {
                id: "09010224",
                path: "/JudiciarySystem/JudCasesScheduling",
                component: "JudCasesScheduling",
              },
              //
                {
                id: "09010225",
                path: "/JudiciarySystem/JudLawyerFeesReceive",
                component: "JudLawyerFeesReceive",
              },
              {
                id: "09010215",
                path: "/JudiciarySystem/JudServicesQuerySavedView",
                component: "JudServicesQuerySavedView",
              }, {
                id: "09010236",
                path: "/JudiciarySystem/JudCasesActionsSms",
                component: "JudCasesActionsSms",
              },
              {
                id: "09010234",
                path: "/JudiciarySystem/JudCasesReactivateLog",
                component: "JudCasesReactivateLog",
              },
              //
               {
                id: "09010229",
                path: "/JudiciarySystem/JudLawyerPaymentsQuery",
                component: "JudLawyerPaymentsQuery",
              },
              {
                id: "09010230",
                path: "/JudiciarySystem/JudLawyerAgreementLog",
                component: "JudLawyerAgreementLog",
              }, {
                id: "09010231",
                path: "/JudiciarySystem/JudCasesQuery",
                component: "JudCasesQuery",
              },
              {
                id: "09010232",
                path: "/JudiciarySystem/JudLawyerPaymentsDetailsVw",
                component: "JudLawyerPaymentsDetailsVw",
              }, {
                id: "09010233",
                path: "/JudiciarySystem/JudCasesDetailsPaidInv",
                component: "JudCasesDetailsPaidInv",
              },

              
            ],
          },
          //090103
           {
            id: "090103",
            subObjects: [
              {
                id: "09010309",
                path: "/JudiciarySystem/RepJudCasesByLawyer",
                component: "RepJudCasesByLawyer",
              },
              {
                id: "09010308",
                path: "/JudiciarySystem/RepJudCasesFees",
                component: "RepJudCasesFees",
              }, {
                id: "09010305",
                path: "/JudiciarySystem/RepJudCasesActionsSummary",
                component: "RepJudCasesActionsSummary",
              },
              {
                id: "09010304",
                path: "/JudiciarySystem/RepJudCasesActionsDetails",
                component: "RepJudCasesActionsDetails",
              }, {
                id: "09010303",
                path: "/JudiciarySystem/RepJudLateCases",
                component: "RepJudLateCases",
              },
              {
                id: "09010302",
                path: "/JudiciarySystem/RepJudCasesSummaryRep2",
                component: "RepJudCasesSummaryRep2",
              },
              //
               {
                id: "09010301",
                path: "/JudiciarySystem/RepJudCasesSummaryRep",
                component: "RepJudCasesSummaryRep",
              },
              {
                id: "09010306",
                path: "/JudiciarySystem/RepJudTransTemplates",
                component: "RepJudTransTemplates",
              }, {
                id: "09010307",
                path: "/JudiciarySystem/RepJudFees",
                component: "RepJudFees",
              },
              {
                id: "09010324",
                path: "/JudiciarySystem/RepJudFinishedCasesRep",
                component: "RepJudFinishedCasesRep",
              },
              //
              {
                id: "09010318",
                path: "/JudiciarySystem/RepJudCasesTransfered",
                component: "RepJudCasesTransfered",
              },
              {
                id: "09010315",
                path: "/JudiciarySystem/RepSummaryPaymentsRep",
                component: "RepSummaryPaymentsRep",
              }, {
                id: "09010316",
                path: "/JudiciarySystem/RepJudLawyerPayments",
                component: "RepJudLawyerPayments",
              },
              {
                id: "09010317",
                path: "/JudiciarySystem/RepJudLawyerPaymentsDtl",
                component: "RepJudLawyerPaymentsDtl",
              },
              //
               {
                id: "09010323",
                path: "/JudiciarySystem/RepJudCasesActionsDetailsByUser",
                component: "RepJudCasesActionsDetailsByUser",
              },
              {
                id: "09010321",
                path: "/JudiciarySystem/RepJudCasesLawyerStages",
                component: "RepJudCasesLawyerStages",
              }, {
                id: "09010322",
                path: "/JudiciarySystem/RepJudCasesLawyerStagesDtls",
                component: "RepJudCasesLawyerStagesDtls",
              },
              {
                id: "09010311",
                path: "/JudiciarySystem/RepJudCasesUpdates",
                component: "RepJudCasesUpdates",
              },
              //
               {
                id: "09010312",
                path: "/JudiciarySystem/RepJudMergedCases",
                component: "RepJudMergedCases",
              },
              {
                id: "09010313",
                path: "/JudiciarySystem/RepJudDeletedCase",
                component: "RepJudDeletedCase",
              }, {
                id: "09010314",
                path: "/JudiciarySystem/RepJudCasesLawyerStat",
                component: "RepJudCasesLawyerStat",
              },
              {
                id: "09010319",
                path: "/JudiciarySystem/RepJudCasesPaymentsChkRep",
                component: "RepJudCasesPaymentsChkRep",
              },
              //
                {
                id: "09010320",
                path: "/JudiciarySystem/RepSummaryPaymentsChkRep",
                component: "RepSummaryPaymentsChkRep",
              },
              {
                id: "09010310",
                path: "/JudiciarySystem/RepJudCasesPaymentsRep",
                component: "RepJudCasesPaymentsRep",
              }, {
                id: "09010327",
                path: "/JudiciarySystem/RepJudDeletedCases",
                component: "RepJudDeletedCases",
              },
              {
                id: "09010325",
                path: "/JudiciarySystem/JudCasesSchedulingStat",
                component: "JudCasesSchedulingStat",
              },
              //
                {
                id: "09010326",
                path: "/JudiciarySystem/RepJudCasesScheduling",
                component: "RepJudCasesScheduling",
              },


             

              
            ],
          },
        ],
      },
      //0902
       {
        id: "0902",
        subMenus: [
          {
            id: "090201",
            subObjects: [
              {
                id: "09020110",
                path: "/JudiciarySystem/JudCortInsurance",
                component: "JudCortInsurance",
              },
              {
                id: "09020101",
                path: "/JudiciarySystem/JudCortActionCodes",
                component: "JudCortActionCodes",
              }, {
                id: "09020102",
                path: "/JudiciarySystem/JudCortSueTypes",
                component: "JudCortSueTypes",
              },
              {
                id: "09020103",
                path: "/JudiciarySystem/JudCortCaseTypes",
                component: "JudCortCaseTypes",
              }, {
                id: "09020104",
                path: "/JudiciarySystem/JudCortAttachmentTypes",
                component: "JudCortAttachmentTypes",
              },
              {
                id: "09020106",
                path: "/JudiciarySystem/JudCortPaymentTypes",
                component: "JudCortPaymentTypes",
              },
              //
               {
                id: "09020107",
                path: "/JudiciarySystem/JudCortPersons",
                component: "JudCortPersons",
              },
              {
                id: "09020108",
                path: "/JudiciarySystem/JudEmployees",
                component: "JudEmployees",
              }, {
                id: "09020109",
                path: "/JudiciarySystem/JudCortCaseStatus",
                component: "JudCortCaseStatus",
              },
             
              
            ],
          },
          //090102
          {
            id: "090102",
            subObjects: [
              {
                id: "09010222",
                path: "/JudiciarySystem/JudLawyerPayments",
                component: "JudLawyerPayments",
              },
              {
                id: "09010201",
                path: "/JudiciarySystem/JudCases",
                component: "JudCases",
              }, {
                id: "09010202",
                path: "/JudiciarySystem/JudCasesInfoChange",
                component: "JudCasesInfoChange",
              },
              {
                id: "09010204",
                path: "/JudiciarySystem/JudCasesUpdated",
                component: "JudCasesUpdated",
              }, {
                id: "09010226",
                path: "/JudiciarySystem/JudCasesEmptyAgreements",
                component: "JudCasesEmptyAgreements",
              },
              {
                id: "09010213",
                path: "/JudiciarySystem/JudCasesNewUpdates",
                component: "JudCasesNewUpdates",
              },
              //
               {
                id: "09010214",
                path: "/JudiciarySystem/JudCasesQueryTemp",
                component: "JudCasesQueryTemp",
              },
              {
                id: "09010216",
                path: "/JudiciarySystem/JudCasesBranch",
                component: "JudCasesBranch",
              }, {
                id: "09010203",
                path: "/JudiciarySystem/JudCasesChangeStatus",
                component: "JudCasesChangeStatus",
              },
              {
                id: "09010220",
                path: "/JudiciarySystem/JudCasesLate",
                component: "JudCasesLate",
              },
              //
              {
                id: "09010221",
                path: "/JudiciarySystem/JudServicesQueryRead",
                component: "JudServicesQueryRead",
              },
              {
                id: "09010223",
                path: "/JudiciarySystem/JudLawyerCredit",
                component: "JudLawyerCredit",
              }, {
                id: "09010228",
                path: "/JudiciarySystem/JudFeesSheetMaster",
                component: "JudFeesSheetMaster",
              },
              {
                id: "09010217",
                path: "/JudiciarySystem/JudCasesAdded",
                component: "JudCasesAdded",
              },
              //
               {
                id: "09010218",
                path: "/JudiciarySystem/JudCasesTypeLog",
                component: "JudCasesTypeLog",
              },
              {
                id: "09010219",
                path: "/JudiciarySystem/JudMultiCasesServicesVw",
                component: "JudMultiCasesServicesVw",
              }, {
                id: "09010227",
                path: "/JudiciarySystem/JudLoadLawyerData",
                component: "JudLoadLawyerData",
              },
              {
                id: "09010207",
                path: "/JudiciarySystem/JudCasesL",
                component: "JudCasesL",
              },
              //
               {
                id: "09010209",
                path: "/JudiciarySystem/JudServicesQuerySavedRev",
                component: "JudServicesQuerySavedRev",
              },
              {
                id: "09010205",
                path: "/JudiciarySystem/JudServicesQuery",
                component: "JudServicesQuery",
              }, {
                id: "09010206",
                path: "/JudiciarySystem/JudServicesQuerySaved",
                component: "JudServicesQuerySaved",
              },
              {
                id: "09010212",
                path: "/JudiciarySystem/JudServicesApprovedCasesVw",
                component: "JudServicesApprovedCasesVw",
              },
              //
                {
                id: "09010211",
                path: "/JudiciarySystem/JudCasesIsCourtVw",
                component: "JudCasesIsCourtVw",
              },
              {
                id: "09010210",
                path: "/JudiciarySystem/JudServicesQuerySavedT",
                component: "JudServicesQuerySavedT",
              }, {
                id: "09010208",
                path: "/JudiciarySystem/JudCasesActionsTemp",
                component: "JudCasesActionsTemp",
              },
              {
                id: "09010224",
                path: "/JudiciarySystem/JudCasesScheduling",
                component: "JudCasesScheduling",
              },
              //
                {
                id: "09010225",
                path: "/JudiciarySystem/JudLawyerFeesReceive",
                component: "JudLawyerFeesReceive",
              },
              {
                id: "09010215",
                path: "/JudiciarySystem/JudServicesQuerySavedView",
                component: "JudServicesQuerySavedView",
              }, {
                id: "09010236",
                path: "/JudiciarySystem/JudCasesActionsSms",
                component: "JudCasesActionsSms",
              },
              {
                id: "09010234",
                path: "/JudiciarySystem/JudCasesReactivateLog",
                component: "JudCasesReactivateLog",
              },
              //
               {
                id: "09010229",
                path: "/JudiciarySystem/JudLawyerPaymentsQuery",
                component: "JudLawyerPaymentsQuery",
              },
              {
                id: "09010230",
                path: "/JudiciarySystem/JudLawyerAgreementLog",
                component: "JudLawyerAgreementLog",
              }, {
                id: "09010231",
                path: "/JudiciarySystem/JudCasesQuery",
                component: "JudCasesQuery",
              },
              {
                id: "09010232",
                path: "/JudiciarySystem/JudLawyerPaymentsDetailsVw",
                component: "JudLawyerPaymentsDetailsVw",
              }, {
                id: "09010233",
                path: "/JudiciarySystem/JudCasesDetailsPaidInv",
                component: "JudCasesDetailsPaidInv",
              },

              
            ],
          },
          //090103
           {
            id: "090103",
            subObjects: [
              {
                id: "09010309",
                path: "/JudiciarySystem/RepJudCasesByLawyer",
                component: "RepJudCasesByLawyer",
              },
              {
                id: "09010308",
                path: "/JudiciarySystem/RepJudCasesFees",
                component: "RepJudCasesFees",
              }, {
                id: "09010305",
                path: "/JudiciarySystem/RepJudCasesActionsSummary",
                component: "RepJudCasesActionsSummary",
              },
              {
                id: "09010304",
                path: "/JudiciarySystem/RepJudCasesActionsDetails",
                component: "RepJudCasesActionsDetails",
              }, {
                id: "09010303",
                path: "/JudiciarySystem/RepJudLateCases",
                component: "RepJudLateCases",
              },
              {
                id: "09010302",
                path: "/JudiciarySystem/RepJudCasesSummaryRep2",
                component: "RepJudCasesSummaryRep2",
              },
              //
               {
                id: "09010301",
                path: "/JudiciarySystem/RepJudCasesSummaryRep",
                component: "RepJudCasesSummaryRep",
              },
              {
                id: "09010306",
                path: "/JudiciarySystem/RepJudTransTemplates",
                component: "RepJudTransTemplates",
              }, {
                id: "09010307",
                path: "/JudiciarySystem/RepJudFees",
                component: "RepJudFees",
              },
              {
                id: "09010324",
                path: "/JudiciarySystem/RepJudFinishedCasesRep",
                component: "RepJudFinishedCasesRep",
              },
              //
              {
                id: "09010318",
                path: "/JudiciarySystem/RepJudCasesTransfered",
                component: "RepJudCasesTransfered",
              },
              {
                id: "09010315",
                path: "/JudiciarySystem/RepSummaryPaymentsRep",
                component: "RepSummaryPaymentsRep",
              }, {
                id: "09010316",
                path: "/JudiciarySystem/RepJudLawyerPayments",
                component: "RepJudLawyerPayments",
              },
              {
                id: "09010317",
                path: "/JudiciarySystem/RepJudLawyerPaymentsDtl",
                component: "RepJudLawyerPaymentsDtl",
              },
              //
               {
                id: "09010323",
                path: "/JudiciarySystem/RepJudCasesActionsDetailsByUser",
                component: "RepJudCasesActionsDetailsByUser",
              },
              {
                id: "09010321",
                path: "/JudiciarySystem/RepJudCasesLawyerStages",
                component: "RepJudCasesLawyerStages",
              }, {
                id: "09010322",
                path: "/JudiciarySystem/RepJudCasesLawyerStagesDtls",
                component: "RepJudCasesLawyerStagesDtls",
              },
              {
                id: "09010311",
                path: "/JudiciarySystem/RepJudCasesUpdates",
                component: "RepJudCasesUpdates",
              },
              //
               {
                id: "09010312",
                path: "/JudiciarySystem/RepJudMergedCases",
                component: "RepJudMergedCases",
              },
              {
                id: "09010313",
                path: "/JudiciarySystem/RepJudDeletedCase",
                component: "RepJudDeletedCase",
              }, {
                id: "09010314",
                path: "/JudiciarySystem/RepJudCasesLawyerStat",
                component: "RepJudCasesLawyerStat",
              },
              {
                id: "09010319",
                path: "/JudiciarySystem/RepJudCasesPaymentsChkRep",
                component: "RepJudCasesPaymentsChkRep",
              },
              //
                {
                id: "09010320",
                path: "/JudiciarySystem/RepSummaryPaymentsChkRep",
                component: "RepSummaryPaymentsChkRep",
              },
              {
                id: "09010310",
                path: "/JudiciarySystem/RepJudCasesPaymentsRep",
                component: "RepJudCasesPaymentsRep",
              }, {
                id: "09010327",
                path: "/JudiciarySystem/RepJudDeletedCases",
                component: "RepJudDeletedCases",
              },
              {
                id: "09010325",
                path: "/JudiciarySystem/JudCasesSchedulingStat",
                component: "JudCasesSchedulingStat",
              },
              //
                {
                id: "09010326",
                path: "/JudiciarySystem/RepJudCasesScheduling",
                component: "RepJudCasesScheduling",
              },
 
            ],
          },
          //090202
            {
            id: "090202",
            subObjects: [
              {
                id: "09020201",
                path: "/JudiciarySystem/JudCortCases",
                component: "JudCortCases",
              },
              {
                id: "09020202",
                path: "/JudiciarySystem/JudCortSessions",
                component: "JudCortSessions",
              }, {
                id: "09020203",
                path: "/JudiciarySystem/JudCortCasesQ",
                component: "JudCortCasesQ",
              },
             
 
            ],
          },
        ],
      },
    
    ],
  },
  //10
 {
    id: "10",
    systemIcon: "ComplaintsSystem",
    subSystems: [
      //1001
      {
        id: "1001",
        subMenus: [
          {
            id: "100101",
            subObjects: [
              {
                id: "10010101",
                path: "/JudiciarySystem/CompComplaintTypes",
                component: "CompComplaintTypes",
              },
              {
                id: "10010102",
                path: "/JudiciarySystem/CompComplaintStatus",
                component: "CompComplaintStatus",
              }, {
                id: "10010103",
                path: "/JudiciarySystem/CompActionCodes",
                component: "CompActionCodes",
              },
              {
                id: "10010104",
                path: "/JudiciarySystem/CompEmployees",
                component: "CompEmployees",
              }, {
                id: "10010105",
                path: "/JudiciarySystem/CompComplaintResources",
                component: "CompComplaintResources",
              },
              {
                id: "10010108",
                path: "/JudiciarySystem/CompAttachmentTypes",
                component: "CompAttachmentTypes",
              },
              //
              {
                id: "10010106",
                path: "/JudiciarySystem/CompNoteTypes",
                component: "CompNoteTypes",
              },
              {
                id: "10010109",
                path: "/JudiciarySystem/CompBranchesInfo",
                component: "CompBranchesInfo",
              }, {
                id: "10010100",
                path: "/JudiciarySystem/CompaniesSystemParamComplaintsSystem",
                component: "CompaniesSystemParamComplaintsSystem",
              },
              {
                id: "10010107",
                path: "/JudiciarySystem/CompComplaintGroups",
                component: "CompComplaintGroups",
              }, {
                id: "10010110",
                path: "/JudiciarySystem/CompComplaintCategories",
                component: "CompComplaintCategories",
              },
              
            ],
          },  {
            id: "100102",
            subObjects: [
              {
                id: "10010201",
                path: "/JudiciarySystem/CompComplaints",
                component: "CompComplaints",
              },
              {
                id: "10010202",
                path: "/JudiciarySystem/CompComplaintsInbox",
                component: "CompComplaintsInbox",
              }, {
                id: "10010203",
                path: "/JudiciarySystem/CompComplaintsReview",
                component: "CompComplaintsReview",
              },
              {
                id: "10010206",
                path: "/JudiciarySystem/CompComplaintsTempQuery",
                component: "CompComplaintsTempQuery",
              }, {
                id: "10010207",
                path: "/JudiciarySystem/CompComplaintsEmployeesLog",
                component: "CompComplaintsEmployeesLog",
              },
              {
                id: "10010205",
                path: "/JudiciarySystem/CompComplaintsTemp",
                component: "CompComplaintsTemp",
              },
              //
              {
                id: "10010204",
                path: "/JudiciarySystem/CompComplaintsUnarchived",
                component: "CompComplaintsUnarchived",
              },
              {
                id: "10010208",
                path: "/JudiciarySystem/CompComplaintsAllQueryOnly",
                component: "CompComplaintsAllQueryOnly",
              }, {
                id: "10010209",
                path: "/JudiciarySystem/CompComplaintsCallCenter",
                component: "CompComplaintsCallCenter",
              },
              {
                id: "10010210",
                path: "/JudiciarySystem/CompComplaintsCallCenterReview",
                component: "CompComplaintsCallCenterReview",
              }, 
              
            ],
          }, {
            id: "100103",
            subObjects: [
              {
                id: "10010307",
                path: "/JudiciarySystem/RepCompComplaintsDaysLog",
                component: "RepCompComplaintsDaysLog",
              },
              {
                id: "10010301",
                path: "/JudiciarySystem/RepCompComplaintsSummaryRep",
                component: "RepCompComplaintsSummaryRep",
              }, {
                id: "10010302",
                path: "/JudiciarySystem/RepCompComplaintsDuePeriod",
                component: "RepCompComplaintsDuePeriod",
              },
              {
                id: "10010303",
                path: "/JudiciarySystem/RepFinComplaintsDuePeriod",
                component: "RepFinComplaintsDuePeriod",
              }, {
                id: "10010304",
                path: "/JudiciarySystem/RepCompLateComplaints",
                component: "RepCompLateComplaints",
              },
              {
                id: "10010305",
                path: "/JudiciarySystem/RepComplaintsActionsDuePeriod",
                component: "RepComplaintsActionsDuePeriod",
              },
              //
              {
                id: "10010306",
                path: "/JudiciarySystem/RepCompMonthlySummary",
                component: "RepCompMonthlySummary",
              },
              {
                id: "10010308",
                path: "/JudiciarySystem/RepCompComplaintsTemp",
                component: "RepCompComplaintsTemp",
              },
              
            ],
          },
        ],
      },
    ],
  },
  //12
  {
    id: "12",
    systemIcon: "DocumentArchivingSystem",
    subSystems: [
      //1001
      {
        id: "1201",
        subMenus: [
          {
            id: "120101",
            subObjects: [
              {
                id: "12010101",
                path: "/DocumentArchivingSystem/EdmFirstLevelTree",
                component: "EdmFirstLevelTree",
              },
              {
                id: "12010102",
                path: "/DocumentArchivingSystem/EdmTree",
                component: "EdmTree",
              },  {
                id: "12010103",
                path: "/DocumentArchivingSystem/EdmDocumentFormats",
                component: "EdmDocumentFormats",
              },
              {
                id: "12010106",
                path: "/DocumentArchivingSystem/EdmUsersFilePrivs",
                component: "EdmUsersFilePrivs",
              }, 
              //
               {
                id: "12010107",
                path: "/DocumentArchivingSystem/EdmUsersParams",
                component: "EdmUsersParams",
              },
              {
                id: "12010100",
                path: "/DocumentArchivingSystem/CompaniesSystemParamDocumentArchivingSystem",
                component: "CompaniesSystemParamDocumentArchivingSystem",
              },  {
                id: "12010105",
                path: "/DocumentArchivingSystem/EdmDocumentTypes",
                component: "EdmDocumentTypes",
              },
              {
                id: "12010104",
                path: "/DocumentArchivingSystem/EdmLookups",
                component: "EdmLookups",
              }, 
              
            ],
          },
          //
           {
            id: "120102",
            subObjects: [
              {
                id: "12010201",
                path: "/DocumentArchivingSystem/EdmUsersTree",
                component: "EdmUsersTree",
              },
              {
                id: "12010202",
                path: "/DocumentArchivingSystem/EdmUsersTreeSearch",
                component: "EdmUsersTreeSearch",
              },  {
                id: "12010203",
                path: "/DocumentArchivingSystem/EdmUsersTreeSearchM",
                component: "EdmUsersTreeSearchM",
              },
              {
                id: "12010204",
                path: "/DocumentArchivingSystem/EdmBrowseLogsVw",
                component: "EdmBrowseLogsVw",
              }, 
           
              
              
            ],
          },

            {
            id: "120103",
            subObjects: [
              {
                id: "12010304",
                path: "/DocumentArchivingSystem/RepEdmUserTreePrivs",
                component: "RepEdmUserTreePrivs",
              },
              {
                id: "12010302",
                path: "/DocumentArchivingSystem/RepEdmTreeSummary",
                component: "RepEdmTreeSummary",
              },  {
                id: "12010301",
                path: "/DocumentArchivingSystem/RepEdmFilesDocs",
                component: "RepEdmFilesDocs",
              },
              {
                id: "12010306",
                path: "/DocumentArchivingSystem/RepEdmTreeDetails",
                component: "RepEdmTreeDetails",
              }, 
              //
            {
                id: "12010305",
                path: "/DocumentArchivingSystem/RepEdmTreeUserPrivs",
                component: "RepEdmTreeUserPrivs",
              },
              {
                id: "12010303",
                path: "/DocumentArchivingSystem/RepEdmFilesDocsNotConfirmed",
                component: "RepEdmFilesDocsNotConfirmed",
              },  {
                id: "12010307",
                path: "/DocumentArchivingSystem/RepEdmFilesDocsSummary",
                component: "RepEdmFilesDocsSummary",
              },
               
              
              
            ],
          },
        ],
      },
    ],
  },
  //13
   {
    id: "13",
    systemIcon: "BranchesPlanSystem",
    subSystems: [
    
      {
        id: "1301",
        subMenus: [
          {
            id: "130100",
            subObjects: [
              {
                id: "13010001",
                path: "/BranchesPlanSystem/MizPaths",
                component: "MizPaths",
              },
              {
                id: "13010002",
                path: "/BranchesPlanSystem/MisEmployees",
                component: "MisEmployees",
              }, 
              
              
            ],
          }, {
            id: "130107",
            subObjects: [
              {
                id: "13010701",
                path: "/BranchesPlanSystem/MizMonthlyPlan",
                component: "MizMonthlyPlan",
              },
         
            ],
          }, {
            id: "130101",
            subObjects: [
              {
                id: "13010101",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamO",
                component: "MizMonthlyPlanParamO",
              }, {
                id: "13010102",
                path: "/BranchesPlanSystem/MizMonthlyRepO",
                component: "MizMonthlyRepO",
              }, {
                id: "13010106",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamOS",
                component: "MizMonthlyPlanParamOS",
              }, {
                id: "13010105",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamOC",
                component: "MizMonthlyPlanParamOC",
              }, {
                id: "13010103",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamODtl",
                component: "MizMonthlyPlanParamODtl",
              }, {
                id: "13010104",
                path: "/BranchesPlanSystem/MizMonthlyRepODtl",
                component: "MizMonthlyRepODtl",
              },
         
            ],
          },{
            id: "130102",
            subObjects: [
              {
                id: "13010201",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamS",
                component: "MizMonthlyPlanParamS",
              }, {
                id: "13010202",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamSDtl",
                component: "MizMonthlyPlanParamSDtl",
              }, {
                id: "13010203",
                path: "/BranchesPlanSystem/MizMonthlySalesS",
                component: "MizMonthlySalesS",
              }, {
                id: "13010204",
                path: "/BranchesPlanSystem/MizMonthlySalesSDtl",
                component: "MizMonthlySalesSDtl",
              }, 
         
            ],
          },{
            id: "130103",
            subObjects: [
              {
                id: "13010302",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamDDtl",
                component: "MizMonthlyPlanParamDDtl",
              }, {
                id: "13010301",
                path: "/BranchesPlanSystem/MizMonthlyPlanParamD",
                component: "MizMonthlyPlanParamD",
              }, {
                id: "13010304",
                path: "/BranchesPlanSystem/MizMonthlyCreditD",
                component: "MizMonthlyCreditD",
              }, {
                id: "13010303",
                path: "/BranchesPlanSystem/MizMonthlyCreditDDtl",
                component: "MizMonthlyCreditDDtl",
              }, 
         
            ],
          },{
            id: "130105",
            subObjects: [
              {
                id: "13010501",
                path: "/BranchesPlanSystem/MizSultaCampsMonthlyB",
                component: "MizSultaCampsMonthlyB",
              }, {
                id: "13010502",
                path: "/BranchesPlanSystem/MizSultaCampsMonthly",
                component: "MizSultaCampsMonthly",
              },
         
            ],
          },
          {
            id: "130106",
            subObjects: [
              {
                id: "13010606",
                path: "/BranchesPlanSystem/RepMizMonthlyPlanRepSbranchComp",
                component: "RepMizMonthlyPlanRepSbranchComp",
              }, {
                id: "13010603",
                path: "/BranchesPlanSystem/RepMizMonthlyPlanRepPath",
                component: "RepMizMonthlyPlanRepPath",
              }, {
                id: "13010604",
                path: "/BranchesPlanSystem/RepMizMonthlyPlanRepSbranch",
                component: "RepMizMonthlyPlanRepSbranch",
              }, {
                id: "13010602",
                path: "/BranchesPlanSystem/RepMizMonthlyPlanRep2",
                component: "RepMizMonthlyPlanRep2",
              }, {
                id: "13010605",
                path: "/BranchesPlanSystem/RepMizMonthlyPlanRepPathComp",
                component: "RepMizMonthlyPlanRepPathComp",
              }, {
                id: "13010601",
                path: "/BranchesPlanSystem/RepMizMonthlyPlanRep",
                component: "RepMizMonthlyPlanRep",
              },
         
            ],
          },
           {
            id: "130108",
            subObjects: [
              {
                id: "13010801",
                path: "/BranchesPlanSystem/MizMonthlyReadingsEstStat",
                component: "MizMonthlyReadingsEstStat",
              }, {
                id: "13010802",
                path: "/BranchesPlanSystem/MizMonthlyReadingsCalcStat",
                component: "MizMonthlyReadingsCalcStat",
              },
         
            ],
          },
         
        ],
      },
       {
        id: "1302",
        subMenus: [
          {
            id: "130201",
            subObjects: [
              {
                id: "13020101",
                path: "/BranchesPlanSystem/MisRItemsGroups",
                component: "MisRItemsGroups",
              },
              {
                id: "13020102",
                path: "/BranchesPlanSystem/MisRBranchesOffices",
                component: "MisRBranchesOffices",
              }, 
               {
                id: "13020103",
                path: "/BranchesPlanSystem/MisROfficesSbranches",
                component: "MisROfficesSbranches",
              },
              {
                id: "13020104",
                path: "/BranchesPlanSystem/MisROfficesCenters",
                component: "MisROfficesCenters",
              },   {
                id: "13020105",
                path: "/BranchesPlanSystem/MisRTree",
                component: "MisRTree",
              },
              {
                id: "13020106",
                path: "/BranchesPlanSystem/MisRTreeUsers",
                component: "MisRTreeUsers",
              },  {
                id: "13020107",
                path: "/BranchesPlanSystem/MisRTemplates",
                component: "MisRTemplates",
              }, 
              
            ],
          }, {
            id: "130202",
            subObjects: [
              {
                id: "13020201",
                path: "/BranchesPlanSystem/MisRReportMaster",
                component: "MisRReportMaster",
              },
         
            ],
          }, {
            id: "130203",
            subObjects: [
              {
                id: "13020301",
                path: "/BranchesPlanSystem/RepMisRReportMaster",
                component: "RepMisRReportMaster",
              }, {
                id: "13020302",
                path: "/BranchesPlanSystem/RepMisRReportComparison",
                component: "RepMisRReportComparison",
              },
         
            ],
          },
         
        ],
      },
    ],
  },
  //16
   {
    id: "16",
    systemIcon: "BIDSystem",
    subSystems: [
    
      {
        id: "1601",
        subMenus: [
          {
            id: "160101",
            subObjects: [
              {
                id: "16010100",
                path: "/BIDSystem/CompaniesSystemParamBIDSystem",
                component: "CompaniesSystemParamBIDSystem",
              },
              {
                id: "16010101",
                path: "/BIDSystem/BidBidsStatus",
                component: "BidBidsStatus",
              }, 
                {
                id: "16010102",
                path: "/BIDSystem/BidBidsTypes",
                component: "BidBidsTypes",
              },
              {
                id: "16010103",
                path: "/BIDSystem/BidGuaranteeActionTypes",
                component: "BidGuaranteeActionTypes",
              }, 
                {
                id: "16010104",
                path: "/BIDSystem/BidBidsActionTypes",
                component: "BidBidsActionTypes",
              },
              {
                id: "16010105",
                path: "/BIDSystem/BidGuaranteeStatus",
                component: "BidGuaranteeStatus",
              }, 
              //
              {
                id: "16010106",
                path: "/BIDSystem/BidGuaranteeTypes",
                component: "BidGuaranteeTypes",
              },
              {
                id: "16010107",
                path: "/BIDSystem/BidOrganizations",
                component: "BidOrganizations",
              }, 
                {
                id: "16010108",
                path: "/BIDSystem/BidDocumentTypes",
                component: "BidDocumentTypes",
              },
              //
               {
                id: "16010109",
                path: "/BIDSystem/BidContractsTypes",
                component: "BidContractsTypes",
              },
              {
                id: "16010110",
                path: "/BIDSystem/BidContractsStatus",
                component: "BidContractsStatus",
              }, 
                {
                id: "16010111",
                path: "/BIDSystem/BidContractsActionTypes",
                component: "BidContractsActionTypes",
              },
              //
               {
                id: "16010113",
                path: "/BIDSystem/BidLetterTypes",
                component: "BidLetterTypes",
              },
              {
                id: "16010114",
                path: "/BIDSystem/BidEmployees",
                component: "BidEmployees",
              }, 
                {
                id: "16010112",
                path: "/BIDSystem/BidDepartments",
                component: "BidDepartments",
              },
              
            ],
          }, {
            id: "160102",
            subObjects: [
              {
                id: "16010201",
                path: "/BIDSystem/BidBids",
                component: "BidBids",
              },
              {
                id: "16010202",
                path: "/BIDSystem/BidBidsGuarantees",
                component: "BidBidsGuarantees",
              }, 
                {
                id: "16010203",
                path: "/BIDSystem/BidGuaranteesVw",
                component: "BidGuaranteesVw",
              },
              {
                id: "16010204",
                path: "/BIDSystem/BidContracts",
                component: "BidContracts",
              }, 
                {
                id: "16010207",
                path: "/BIDSystem/BidUsersDepartments",
                component: "BidUsersDepartments",
              },
              {
                id: "16010208",
                path: "/BIDSystem/BidBidsInfoChange",
                component: "BidBidsInfoChange",
              }, 
              //
              {
                id: "16010209",
                path: "/BIDSystem/BidBidsLetters",
                component: "BidBidsLetters",
              },
              {
                id: "16010210",
                path: "/BIDSystem/BidBidsQuery",
                component: "BidBidsQuery",
              }, 
                {
                id: "16010205",
                path: "/BIDSystem/BidContractsGuarantees",
                component: "BidContractsGuarantees",
              },
              //
               {
                id: "16010206",
                path: "/BIDSystem/BidBidsOrganizationsOpen",
                component: "BidBidsOrganizationsOpen",
              },
             
              
            ],
          }, 
         
        ],
      },
    ],
  },
  //17
   {
    id: "17",
    systemIcon: "RentSystem",
    subSystems: [
    
      {
        id: "1701",
        subMenus: [
          {
            id: "170101",
            subObjects: [
              {
                id: "17010100",
                path: "/RentSystem/CompaniesSystemParamRentSystem",
                component: "CompaniesSystemParamRentSystem",
              },
              {
                id: "17010101",
                path: "/RentSystem/RntImmovablesTypes",
                component: "RntImmovablesTypes",
              },  {
                id: "17010102",
                path: "/RentSystem/RntDocumentTypes",
                component: "RntDocumentTypes",
              },
              {
                id: "17010103",
                path: "/RentSystem/RntPaymentYears",
                component: "RntPaymentYears",
              },  {
                id: "17010105",
                path: "/RentSystem/RntPaymentStatus",
                component: "RntPaymentStatus",
              },
              {
                id: "17010104",
                path: "/RentSystem/RntUsersBranches",
                component: "RntUsersBranches",
              }, 
          
            ],
          }, {
            id: "170102",
            subObjects: [
              {
                id: "17010209",
                path: "/RentSystem/RntPartiesDiv",
                component: "RntPartiesDiv",
              },
              {
                id: "17010210",
                path: "/RentSystem/RntImmContractPeriodsVwQ",
                component: "RntImmContractPeriodsVwQ",
              },  {
                id: "17010207",
                path: "/RentSystem/RntPartiesPaymentsVw",
                component: "RntPartiesPaymentsVw",
              },
              {
                id: "17010201",
                path: "/RentSystem/RntParties",
                component: "RntParties",
              },  {
                id: "17010202",
                path: "/RentSystem/RntImmovables",
                component: "RntImmovables",
              },
              {
                id: "17010203",
                path: "/RentSystem/RntImmovablesContractsVw",
                component: "RntImmovablesContractsVw",
              }, 
               {
                id: "17010204",
                path: "/RentSystem/RntImmovablesLog",
                component: "RntImmovablesLog",
              },
              {
                id: "17010205",
                path: "/RentSystem/RntImmContractPeriodsVw",
                component: "RntImmContractPeriodsVw",
              }, 
               {
                id: "17010206",
                path: "/RentSystem/RntPartiesPaymentsTrans",
                component: "RntPartiesPaymentsTrans",
              },
              {
                id: "17010208",
                path: "/RentSystem/RntPrpareAllPayments",
                component: "RntPrpareAllPayments",
              }, 
          
            ],
          },{
            id: "170103",
            subObjects: [
              {
                id: "17010301",
                path: "/RentSystem/RepRntImmovablesLog",
                component: "RepRntImmovablesLog",
              },
              {
                id: "17010302",
                path: "/RentSystem/RepRntPartiesPayments",
                component: "RepRntPartiesPayments",
              },  {
                id: "17010303",
                path: "/RentSystem/RepRntPartiesPaymentsLog",
                component: "RepRntPartiesPaymentsLog",
              },
             
            ],
          },
         
        ],
      },
    ],
  },
   //18
   {
    id: "18",
    systemIcon: "AlShamelSystem",
    subSystems: [
      {
        id: "1801",
        subMenus: [
          {
            id: "180101",
            subObjects: [
              {
                id: "18010104",
                path: "/AlShamelSystem/ZHhuGpsSystemUsers",
                component: "ZHhuGpsSystemUsers",
              },
              {
                id: "18010102",
                path: "/AlShamelSystem/HhuGpsTaskTypes",
                component: "HhuGpsTaskTypes",
              },  {
                id: "18010103",
                path: "/AlShamelSystem/HhuGpsTaskResults",
                component: "HhuGpsTaskResults",
              },
              {
                id: "18010101",
                path: "/AlShamelSystem/HhuGpsUsers",
                component: "HhuGpsUsers",
              },   {
                id: "18010100",
                path: "/AlShamelSystem/CompaniesSystemParamAlShamelSystem",
                component: "CompaniesSystemParamAlShamelSystem",
              }, 
            ],
          },{
            id: "180102",
            subObjects: [
              {
                id: "18010202",
                path: "/AlShamelSystem/HhuGpsMeters",
                component: "HhuGpsMeters",
              },
              {
                id: "18010201",
                path: "/AlShamelSystem/HhuGpsInfoChange",
                component: "HhuGpsInfoChange",
              },  {
                id: "18010203",
                path: "/AlShamelSystem/HhuGpsMeterTestTemp",
                component: "HhuGpsMeterTestTemp",
              },
              //
                {
                id: "18010204",
                path: "/AlShamelSystem/HhuGpsPaymentsEntry",
                component: "HhuGpsPaymentsEntry",
              },
              {
                id: "18010205",
                path: "/AlShamelSystem/HhuGpsServiceNotes",
                component: "HhuGpsServiceNotes",
              },  {
                id: "18010206",
                path: "/AlShamelSystem/HhuGpsDailyServices",
                component: "HhuGpsDailyServices",
              },
             
            ],
          },
         
        ],
      },
    ],
  },
  //19
   {
    id: "19",
    systemIcon: "CallCenterSystem",
    subSystems: [
      {
        id: "1901",
        subMenus: [
          {
            id: "190101",
            subObjects: [
              {
                id: "19010101",
                path: "/CallCenterSystem/CompaniesSystemParamCallCenterSystem",
                component: "CompaniesSystemParamCallCenterSystem",
              },
              {
                id: "19010102",
                path: "/CallCenterSystem/CcCategories",
                component: "CcCategories",
              },  {
                id: "19010103",
                path: "/CallCenterSystem/CcGrades",
                component: "CcGrades",
              },
              {
                id: "19010105",
                path: "/CallCenterSystem/CcEmployees",
                component: "CcEmployees",
              },   {
                id: "19010106",
                path: "/CallCenterSystem/CcLookups",
                component: "CcLookups",
              }, 
            ],
          },
         
        ],
      }, {
        id: "1902",
        subMenus: [
          {
            id: "190202",
            subObjects: [
              {
                id: "19020202",
                path: "/CallCenterSystem/CcCallPatchesEmployees",
                component: "CcCallPatchesEmployees",
              },
              {
                id: "19020201",
                path: "/CallCenterSystem/CcCallPatches",
                component: "CcCallPatches",
              },  {
                id: "19020204",
                path: "/CallCenterSystem/CcCallLog",
                component: "CcGrades",
              },
              {
                id: "19020203",
                path: "/CallCenterSystem/CcCallPatchesSupervisor",
                component: "CcCallPatchesSupervisor",
              },
            ],
          }, {
            id: "190203",
            subObjects: [
              {
                id: "19020202",
                path: "/CallCenterSystem/CcCallPatchesEmployees",
                component: "CcCallPatchesEmployees",
              },
              {
                id: "19020201",
                path: "/CallCenterSystem/CcCallPatches",
                component: "CcCallPatches",
              },  {
                id: "19020204",
                path: "/CallCenterSystem/CcCallLog",
                component: "CcGrades",
              },
              {
                id: "19020203",
                path: "/CallCenterSystem/CcCallPatchesSupervisor",
                component: "CcCallPatchesSupervisor",
              },
            ],
          },{
            id: "190201",
            subObjects: [
              {
                id: "19020101",
                path: "/CallCenterSystem/CcCallTypes",
                component: "CcCallTypes",
              },
              {
                id: "19020102",
                path: "/CallCenterSystem/CcCallResult",
                component: "CcCallResult",
              },  {
                id: "19020103",
                path: "/CallCenterSystem/CcQuestionairs",
                component: "CcQuestionairs",
              },
              {
                id: "19020104",
                path: "/CallCenterSystem/CcDepartments",
                component: "CcDepartments",
              },  {
                id: "19020105",
                path: "/CallCenterSystem/CcCallReasons",
                component: "CcCallReasons",
              },
            ],
          },
         
        ],
      },
       {
        id: "1903",
        subMenus: [
          {
            id: "190301",
            subObjects: [
              {
                id: "19030103",
                path: "/CallCenterSystem/CcEvaluationRating",
                component: "CcEvaluationRating",
              },
              {
                id: "19030102",
                path: "/CallCenterSystem/CcEvaluationScaleGroups",
                component: "CcEvaluationScaleGroups",
              },  {
                id: "19030101",
                path: "/CallCenterSystem/CcEvaluationScale",
                component: "CcEvaluationScale",
              },
             
            ],
          }, {
            id: "190303",
            subObjects: [
              {
                id: "19030301",
                path: "/CallCenterSystem/RepCcEvaluationSummary",
                component: "RepCcEvaluationSummary",
              },
              {
                id: "19030302",
                path: "/CallCenterSystem/RepCcEvaluationEmployees",
                component: "RepCcEvaluationEmployees",
              },  
            ],
          },{
            id: "190302",
            subObjects: [
              {
                id: "19030204",
                path: "/CallCenterSystem/CcEvaluationEmployeesUser",
                component: "CcEvaluationEmployeesUser",
              },
              {
                id: "19030203",
                path: "/CallCenterSystem/CcEvaluationEmployees",
                component: "CcEvaluationEmployees",
              },  {
                id: "19030202",
                path: "/CallCenterSystem/CcEvaluation",
                component: "CcEvaluation",
              },
              {
                id: "19030201",
                path: "/CallCenterSystem/CcDepartments",
                component: "CcDepartments",
              },  {
                id: "19020105",
                path: "/CallCenterSystem/CcEvaluationYears",
                component: "CcEvaluationYears",
              },
            ],
          },
         
        ],
      },
    ],
  }, {
    id: "21",
    systemIcon: "DamageSystem",
    subSystems: [
      {
        id: "2101",
        subMenus: [
          {
            id: "210103",
            subObjects: [
              {
                id: "21010303",
                path: "/CallCenterSystem/RepDamDamagesFinished",
                component: "RepDamDamagesFinished",
              },
              {
                id: "21010301",
                path: "/CallCenterSystem/RepDamDamages",
                component: "RepDamDamages",
              },  {
                id: "21010302",
                path: "/CallCenterSystem/RepDamDamagesCollections",
                component: "RepDamDamagesCollections",
              },
            
            ],
          }, {
            id: "210102",
            subObjects: [
              {
                id: "21010201",
                path: "/CallCenterSystem/DamDamages",
                component: "DamDamages",
              },
              {
                id: "21010202",
                path: "/CallCenterSystem/DamDamagesInfoChange",
                component: "DamDamagesInfoChange",
              }, 
            
            ],
          },{
            id: "210101",
            subObjects: [
              {
                id: "21010106",
                path: "/CallCenterSystem/DamDepartments",
                component: "DamDepartments",
              },
              {
                id: "21010100",
                path: "/CallCenterSystem/CompaniesSystemParamDamageSystem",
                component: "CompaniesSystemParamDamageSystem",
              },  {
                id: "21010107",
                path: "/CallCenterSystem/DamUsers",
                component: "DamUsers",
              },
              {
                id: "21010104",
                path: "/CallCenterSystem/DamActionCodes",
                component: "DamActionCodes",
              }, 
              //
                {
                id: "21010103",
                path: "/CallCenterSystem/DamAttachmentTypes",
                component: "DamAttachmentTypes",
              },
              {
                id: "21010102",
                path: "/CallCenterSystem/DamDamageStatus",
                component: "DamDamageStatus",
              },  {
                id: "21010101",
                path: "/CallCenterSystem/DamDamageTypes",
                component: "DamDamageTypes",
              },
              {
                id: "21010105",
                path: "/CallCenterSystem/DamCompanies",
                component: "DamCompanies",
              }, 
            
            ],
          },
         
        ],
      }, 
       
    ],
  },

];
export default routesConfig;
