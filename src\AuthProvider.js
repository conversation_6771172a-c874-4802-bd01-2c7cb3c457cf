import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import CircularProgress from '@mui/material/CircularProgress';
import { hostURL } from '../src/Common/APIs';
import Cookies from 'js-cookie';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState({
    token: '',
    userCode: '',
    permissions: [],
    favorites: [],
    userNameAr: '',
    userNameEn: '',
    email: '',
    empID: '',
    exNo: '',
    phoneNo: '',
    compCode: '',
  });

  const [loading, setLoading] = useState(true);
  const isAuthenticated = !!user.token;

  const token = Cookies.get('authToken');

  useEffect(() => {
    const fetchUserData = async () => {
      // console.log("asssssssssssdasdasdasdasd:  " + user.userNameAr);
      if (!token) {
        setLoading(false);
        return;
      }
      try {
        const response = await axios.get(`${hostURL}/getAuthProviderByUserCode`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (response.data.resultCode === '0') {
          const userData = response.data.data;
          setUser({
            token,
            ...userData,
          });
          sessionStorage.setItem('userData', JSON.stringify({ token, ...userData }));
        } else {
          console.error('API Error:', response.data.resultMessageE);
          logout();
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        logout();
      }

      setLoading(false);
    };

    const storedUser = sessionStorage.getItem('userData');
    // console.log(storedUser);
    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setUser(parsedUser);
      setLoading(false);
      // setUser(JSON.parse(storedUser));
      // setLoading(false);
    } else {
      fetchUserData();
    }
  }, [token]);
  const updateFavorites = (newFavorites) => {
    setUser((prevUser) => {
      const updatedUser = { ...prevUser, favorites: newFavorites };
      sessionStorage.setItem('userData', JSON.stringify(updatedUser));
      return updatedUser;
    });
  };
  const logout = () => {
    // Cookies.remove('authToken');
    const allCookies = Cookies.get();
    Object.keys(allCookies).forEach(cookie => Cookies.remove(cookie));
    console.log("All cookies have been removed!");

    sessionStorage.removeItem('userData');
    // setUser(null);
    setUser({
      token: '',
      userCode: '',
      permissions: [],
      favorites: [],
      userNameAr: '',
      userNameEn: '',
      email: '',
      empID: '',
      exNo: '',
      phoneNo: '',
      compCode: '',
    });
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </div>
    );
  }

  return <AuthContext.Provider value={{ user, setUser, isAuthenticated, logout, updateFavorites }}>{children}</AuthContext.Provider>;
};

export const useAuth = () => useContext(AuthContext);
