import * as React from 'react';
import DarkModeIcon from '@mui/icons-material/DarkModeRounded';
import LightModeIcon from '@mui/icons-material/LightModeRounded';
// import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { useColorScheme } from '@mui/material/styles';
import Colors from '../Common/Colors';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../AuthProvider';
import Cookies from 'js-cookie';

export default function ColorModeIconDropdown(props) {
  const { mode, setMode } = useColorScheme(); 
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  // const currentColors = Colors[user.compCode] || Colors.default;
  const compCode = Cookies.get('compCode') ? JSON.parse(Cookies.get('compCode')) : null;
  const currentColors = Colors[compCode] || Colors.default;
  
  const currentMode = mode || 'light'; 

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMode = (targetMode) => () => {
    setMode(targetMode);
    handleClose();
  };

  const icon = {
    light: <LightModeIcon sx={{color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary}}/>,
    dark: <DarkModeIcon/>,
  }[currentMode]; 

  return (
    <React.Fragment>
      <IconButton
        data-screenshot="toggle-mode"
        onClick={handleClick}
        disableRipple
        size="small"
        aria-controls={open ? 'color-scheme-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        {...props}
      >
       {icon || <LightModeIcon sx={{color: (theme) => theme.palette.mode === 'dark' ? 'white' : currentColors.secondary}}/>}
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            variant: 'outlined',
            elevation: 0,
            sx: {
              my: '4px',
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem selected={mode === 'light'} onClick={handleMode('light')}>
        {t("LC_000008")}
        </MenuItem>
        <MenuItem selected={mode === 'dark'} onClick={handleMode('dark')}>
          {t("LC_000009")}
        </MenuItem>
      </Menu>
    </React.Fragment>
  );
}
